/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Accordion: typeof import('primevue/accordion')['default']
    AccordionTab: typeof import('primevue/accordiontab')['default']
    Avatar: typeof import('primevue/avatar')['default']
    Badge: typeof import('primevue/badge')['default']
    BestSellingWidget: typeof import('./src/components/dashboard/BestSellingWidget.vue')['default']
    Button: typeof import('primevue/button')['default']
    Calendar: typeof import('primevue/calendar')['default']
    Card: typeof import('primevue/card')['default']
    Chart: typeof import('primevue/chart')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    Chip: typeof import('primevue/chip')['default']
    Column: typeof import('primevue/column')['default']
    ConfirmDialog: typeof import('primevue/confirmdialog')['default']
    DataTable: typeof import('primevue/datatable')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Divider: typeof import('primevue/divider')['default']
    Drawer: typeof import('primevue/drawer')['default']
    Dropdown: typeof import('primevue/dropdown')['default']
    Editor: typeof import('primevue/editor')['default']
    FeaturesWidget: typeof import('./src/components/landing/FeaturesWidget.vue')['default']
    FileUpload: typeof import('primevue/fileupload')['default']
    FloatingConfigurator: typeof import('./src/components/FloatingConfigurator.vue')['default']
    FooterWidget: typeof import('./src/components/landing/FooterWidget.vue')['default']
    HeroWidget: typeof import('./src/components/landing/HeroWidget.vue')['default']
    HighlightsWidget: typeof import('./src/components/landing/HighlightsWidget.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputSwitch: typeof import('primevue/inputswitch')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Menu: typeof import('primevue/menu')['default']
    Message: typeof import('primevue/message')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    NotificationsWidget: typeof import('./src/components/dashboard/NotificationsWidget.vue')['default']
    OverlayBadge: typeof import('primevue/overlaybadge')['default']
    OverlayPanel: typeof import('primevue/overlaypanel')['default']
    Panel: typeof import('primevue/panel')['default']
    Password: typeof import('primevue/password')['default']
    PricingWidget: typeof import('./src/components/landing/PricingWidget.vue')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    RecentSalesWidget: typeof import('./src/components/dashboard/RecentSalesWidget.vue')['default']
    RevenueStreamWidget: typeof import('./src/components/dashboard/RevenueStreamWidget.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('primevue/select')['default']
    SelectButton: typeof import('primevue/selectbutton')['default']
    SpeedDial: typeof import('primevue/speeddial')['default']
    SplitButton: typeof import('primevue/splitbutton')['default']
    StatsWidget: typeof import('./src/components/dashboard/StatsWidget.vue')['default']
    Stepper: typeof import('primevue/stepper')['default']
    Steps: typeof import('primevue/steps')['default']
    TabPanel: typeof import('primevue/tabpanel')['default']
    TabView: typeof import('primevue/tabview')['default']
    Tag: typeof import('primevue/tag')['default']
    Textarea: typeof import('primevue/textarea')['default']
    Timeline: typeof import('primevue/timeline')['default']
    Toast: typeof import('primevue/toast')['default']
    Toolbar: typeof import('primevue/toolbar')['default']
    TopbarWidget: typeof import('./src/components/landing/TopbarWidget.vue')['default']
  }
  export interface ComponentCustomProperties {
    StyleClass: typeof import('primevue/styleclass')['default']
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
