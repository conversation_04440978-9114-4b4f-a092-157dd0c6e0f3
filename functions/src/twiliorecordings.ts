import { onSchedule } from "firebase-functions/v2/scheduler";
import {
  assemblyA<PERSON>pi<PERSON>ey,
  openAIKey,
  twilioAccounSID,
  twilioAuthTpken,
} from "./utils";
import * as logger from "firebase-functions/logger";
import { HttpsError } from "firebase-functions/v2/https";
import { getCallDetails } from "./calls";

export const onTwilioRecordings = onSchedule(
  {
    schedule: "every day 00:00",
    secrets: [openAIKey, twilioAuthTpken, twilioAccounSID, assemblyAIApiKey],
    cpu: 4,
    memory: "16GiB",
    timeoutSeconds: 3600,
  },
  async () => {
    try {
      const today = new Date();
      const year = today.getFullYear();
      const month = (today.getMonth() + 1).toString().padStart(2, "0"); // Month is 0-indexed
      const day = today.getDate().toString().padStart(2, "0");
      const date_from = `${year}-${month}-${day}`;
      const date_to = `${year}-${month}-${day}`;

      await getCallDetails(null, {
        date_from: date_from,
        date_to: date_to,
      });
      logger.info("Finished processing all leads.");
      return;
    } catch (error) {
      logger.error("Error failed processing all leads:", error);
      throw new HttpsError("internal", "Error failed processing all leads.");
    }
  },
);
