import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';

export async function handelLeadDocumentWhatsapp(req: any) {
    const { ProfileName, WaId } = req;

    const leadsCollection = admin.firestore().collection('leads');
    const currentTime = Math.floor(Date.now() / 1000);

    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection.where('source_id', '==', req.WaId).where('lead_source', '==', 'whatsapp').where('lead_actions', '==', 'inquired').get();
    let leadDocRef, leadData;

    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    } else {
        // search for the tenantId
        let tenantId = 'Lw225tYPYTssrWaZ4ipb';
        const sourcePhone = req.To.replace('whatsapp:', '');
        const tenantCollection = admin.firestore().collection('companies');
        const tenantSnapshot = await tenantCollection.where('whatsapp.defaultNumber', '==', sourcePhone).get();
        if (tenantSnapshot.docs.length > 0) {
            tenantId = tenantSnapshot.docs[0].ref.id;
            logger.info(`Whatsapp: Found tenant ${tenantId} for source ${sourcePhone}`);
        } else {
            logger.info(`Whatsapp: No tenant found for source ${sourcePhone}`);
        }

        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            id: leadDocRef.id,
            source_id: WaId,
            phone: WaId,
            name: ProfileName,
            tenantId: tenantId,
            lead_source: 'whatsapp', // Track the source of the lead
            lastMessageTime: currentTime,
            lead_details: req,
            conversations: []
        };
    }
    return { leadDocRef: leadDocRef, leadData: leadData };
}
