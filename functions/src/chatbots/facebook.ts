import axios from 'axios';
import { facebookPageAccessToken } from '../utils';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';

export async function handelLeadDocumentFacebook(req: any) {
    const { sender } = req;
    const psid = sender.id;

    const leadsCollection = admin.firestore().collection('leads');
    const currentTime = Math.floor(Date.now() / 1000);

    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection.where('source_id', '==', psid).where('lead_source', '==', 'facebook').where('lead_actions', '==', 'inquired').get();
    let leadDocRef, leadData;

    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    } else {
        let record;
        // search for the tenantId
        let tenantId = 'Lw225tYPYTssrWaZ4ipb';
        let accessToken = facebookPageAccessToken.value();
        const tenantCollection = admin.firestore().collection('companies');
        const tenantSnapshot = await tenantCollection.where('facebook.pageId', '==', req.pageId).get();
        if (tenantSnapshot.docs.length > 0) {
            tenantId = tenantSnapshot.docs[0].ref.id;
            accessToken = tenantSnapshot.docs[0].data()!.facebook?.pageAccessToken;
            logger.info(`Facebook handelLeadDocumentFacebook: Found tenant ${tenantId} for facebook page ${req.pageId}`);
        } else {
            logger.info(`Facebook handelLeadDocumentFacebook: No tenant found for facebook page ${req.pageId}`);
        }

        try {
            record = await axios.get(`https://graph.facebook.com/${psid}?fields=email,name,locale,timezone,first_name,last_name,profile_pic&access_token=${accessToken}`);
            logger.info('Webhook facebook invoked:', record.data);

            // Log the full response for debugging
            logger.info(`Facebook API response status: ${record.status}`);
            if (record.status !== 200) {
                logger.warn(`Facebook API error: ${JSON.stringify(record.data)}`);
            }

            // Log if email was returned
            if (record.data?.email) {
                logger.info('Successfully retrieved email for user:', psid);
            } else {
                logger.info('Email not available for user:', psid);
            }
        } catch (error) {
            logger.error('Failed to fetch Facebook profile data:', error);
            record = {
                data: {
                    email: '',
                    name: '',
                    first_name: '',
                    last_name: '',
                    profile_pic: '',
                    gender: '',
                    locale: '',
                    timezone: ''
                }
            }; // Continue with empty data if there's an error
        }

        const { name, first_name, last_name, profile_pic, gender, locale, timezone, email } = record.data;

        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            id: leadDocRef.id,
            pageId: req.pageId,
            source_id: psid,
            email: email ?? '',
            name: name ?? '',
            locale: locale ?? '',
            tenantId: tenantId ?? '',
            timezone: timezone ?? '',
            first_name: first_name ?? '',
            last_name: last_name ?? '',
            profile_pic: profile_pic ?? '',
            gender: gender ?? '',
            lead_source: 'facebook', // Track the source of the lead
            lastMessageTime: currentTime,
            lead_details: {
                psid: psid,
                name: name ?? '',
                locale: locale ?? '',
                timezone: timezone ?? '',
                first_name: first_name ?? '',
                last_name: last_name ?? '',
                profile_pic: profile_pic ?? '',
                gender: gender ?? ''
            },
            conversations: []
        };
    }

    // if no email then send request to access email
    if (!leadData.email) {
        await requestEmailPermission(psid);
    }

    return { leadDocRef: leadDocRef, leadData: leadData };
}

// Function to send a message via Facebook Messenger
export async function sendMessengerMessage(pageId: string, recipientId: string, messageText: string) {
    try {
        let accessToken = facebookPageAccessToken.value();
        const tenantCollection = admin.firestore().collection('companies');
        const tenantSnapshot = await tenantCollection.where('facebook.pageId', '==', pageId).get();
        if (tenantSnapshot.docs.length > 0) {
            accessToken = tenantSnapshot.docs[0].data()!.facebook?.pageAccessToken;
            logger.info(`Facebook sendMessengerMessage: Found access token for tenant ${tenantSnapshot.docs[0].ref.id} for page ${pageId}`);
        } else {
            logger.info(`Facebook sendMessengerMessage: No tenant found for page ${pageId}`);
        }

        await axios.post(
            `https://graph.facebook.com/me/messages?access_token=${accessToken}`,
            {
                recipient: { id: recipientId },
                message: { text: messageText }
            },
            {
                headers: { 'Content-Type': 'application/json' }
            }
        );
    } catch (error) {
        logger.error('Error sending message to Facebook Messenger:', error);
    }
}

// Function to request app access for email permission
async function requestEmailPermission(recipientId: string) {
    try {
        await axios.post(
            `https://graph.facebook.com/v23.0/me/messages?access_token=${facebookPageAccessToken.value()}`,
            {
                recipient: { id: recipientId },
                message: {
                    attachment: {
                        type: 'template',
                        payload: {
                            template_type: 'button',
                            text: 'To provide better service, please log in to your account.',
                            buttons: [
                                {
                                    type: 'account_link',
                                    url: `https://us-central1-wyspre-ai.cloudfunctions.net/chatBots/auth-facebook-callback`
                                }
                            ]
                        }
                    }
                }
            },
            {
                headers: { 'Content-Type': 'application/json' }
            }
        );
        logger.info('Email permission request sent to user:', recipientId);
    } catch (error) {
        logger.error('Error requesting email permission:', error);
    }
}
