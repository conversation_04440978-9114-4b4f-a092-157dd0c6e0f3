import * as admin from "firebase-admin";
import axios from "axios";
import { telegramBotToken } from "../utils";

export async function handelLeadDocumentTelegram(req: any) {
  const { message } = req;

  // Extract user information
  const user = message.from;
  const firstName = user.first_name || "";
  const lastName = user.last_name || "";
  const username = user.username || "";
  const languageCode = user.language_code || "";
  const chatId = message.chat.id || "";

  const leadsCollection = admin.firestore().collection("leads");
  const currentTime = Math.floor(Date.now() / 1000);

  // Search for an existing lead by WaId (WhatsApp ID)
  const leadsSnapshot = await leadsCollection
    .where("source_id", "==", chatId)
    .where("lead_source", "==", "telegram")
    .where("lead_actions", "==", "inquired")
    .get();
  let leadDocRef, leadData;

  if (!leadsSnapshot.empty) {
    // Lead exists, get the reference and data
    const leadDoc = leadsSnapshot.docs[0];
    leadDocRef = leadDoc.ref;
    leadData = leadDoc.data();
  } else {
    // Create a new lead document with an autogenerated ID
    leadDocRef = leadsCollection.doc(); // Auto-generate document ID
    leadData = {
      id: leadDocRef.id,
      source_id: chatId,
      name: `${firstName} ${lastName}`,
      locale: languageCode ?? "",
      username: username ?? "",
      first_name: firstName ?? "",
      last_name: lastName ?? "",
      lead_source: "telegram", // Track the source of the lead
      lastMessageTime: currentTime,
      lead_details: user,
      conversations: [],
    };
  }
  return { leadDocRef: leadDocRef, leadData: leadData };
}

export async function sendTelegramMessage(
  chatId: number,
  text: string,
): Promise<void> {
  await axios.post(
    `https://api.telegram.org/bot${telegramBotToken.value()}/sendMessage`,
    {
      chat_id: chatId,
      text: text,
    },
  );
}
export async function sendTelegramIncomingMessage(
  chatId: number,
): Promise<void> {
  await axios.post(
    `https://api.telegram.org/bot${telegramBotToken.value()}/sendChatAction`,
    {
      chat_id: chatId,
      action: "typing",
    },
  );
}
