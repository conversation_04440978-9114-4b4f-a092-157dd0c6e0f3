import * as admin from 'firebase-admin';
import { resetFollowUpDatesForCalls } from '../calls';
import * as logger from 'firebase-functions/logger';
export async function getTenantIdForEmail(to: string) {
    const tenantCollection = admin.firestore().collection('companies');
    const tenantSnapshot = await tenantCollection.where('email.emails', 'array-contains-any', [to]).get();
    if (tenantSnapshot.docs.length > 0) {
        logger.info(`Email: Found tenant ${tenantSnapshot.docs[0].ref.id} for email ${to}`);
        return { ...tenantSnapshot.docs[0].data(), id: tenantSnapshot.docs[0].ref.id };
    } else {
        logger.info(`Email: No tenant found for email ${to}`);
        return null;
    }
}

export async function handelLeadDocumentEmail(req: any) {
    const { to, from, subject, text, html, attachments, sender_ip } = req;

    // Extract email and name from formats like:
    // "Liftt <<EMAIL>>" or ""Caller #447774984274" <<EMAIL>>"
    const emailRegex = /<([^>]+)>|([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
    const nameRegex = /^([^<]+)</;

    const emailMatch = from.match(emailRegex);
    const nameMatch = from.match(nameRegex);

    const email = emailMatch ? emailMatch[1] || emailMatch[2] : '';

    // Clean up the name - remove quotes and extra spaces
    let senderName = nameMatch ? nameMatch[1].trim() : '';
    senderName = senderName.replace(/^["']+|["']+$/g, '').trim(); // Remove quotes at start/end

    const leadsCollection = admin.firestore().collection('leads');
    const currentTime = Math.floor(Date.now() / 1000);

    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection.where('source_id', '==', email).where('lead_source', '==', 'email').where('lead_actions', '==', 'inquired').get();
    let leadDocRef, leadData;

    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    } else {
        // search for the tenantId
        const tenantId = (await getTenantIdForEmail(to)) ?? 'Lw225tYPYTssrWaZ4ipb';

        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            id: leadDocRef.id,
            source_id: email,
            to_lead: to,
            name: senderName ?? '',
            email: email ?? '',
            address: '',
            phone: '',
            tenantId: tenantId,
            lead_details: {
                from: email ?? '',
                to: to ?? '',
                subject: subject ?? '',
                text: text ?? '',
                html: html ?? '',
                attachments: attachments ?? '',
                sender_ip: sender_ip ?? ''
            },
            lead_source: 'email', // Track the source of the lead
            lastMessageTime: currentTime,
            conversations: []
        };
    }
    if (from) await resetFollowUpDatesForCalls(null, from);
    leadData.keywords = [...(leadData.name ? [leadData.name] : []), ...(leadData.email ? [leadData.email] : []), ...(leadData.phone ? [leadData.phone] : []), ...(leadData.id ? [leadData.id] : [])];
    return { leadDocRef: leadDocRef, leadData: leadData };
}
