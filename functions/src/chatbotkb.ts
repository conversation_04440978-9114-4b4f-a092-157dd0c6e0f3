import { onSchedule } from "firebase-functions/v2/scheduler";
import {
  googleDriveFolderId,
  googleServiceAccountEmail,
  googleServiceAccountKey,
  openAIKey,
} from "./utils";
import * as logger from "firebase-functions/logger";
import { HttpsError } from "firebase-functions/v2/https";
import { buildKB } from "./googledrive";

export const syncGoogleDriveFiles = onSchedule(
  {
    schedule: "0 0 * * *",
    secrets: [
      googleServiceAccountKey,
      googleServiceAccountEmail,
      openAIKey,
      googleDriveFolderId,
    ],
    cpu: 4,
    memory: "16GiB",
    timeoutSeconds: 3600,
  },
  async () => {
    try {
      await buildKB();
      logger.info("Google Drive files synced successfully.");
      return;
    } catch (error) {
      logger.error("Error syncing Google Drive files:", error);
      throw new HttpsError("internal", "Failed to sync Google Drive files.");
    }
  },
);
