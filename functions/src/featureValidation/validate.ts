import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';

export async function validateFeatures(feature: string, validateValue: any) {
    let tenantSnapshot: any;

    const tenantCollection = admin.firestore().collection('companies');
    switch (feature) {
        case 'whatsapp':
            tenantSnapshot = await tenantCollection.where('email.emails', 'array-contains-any', [validateValue]).get();
            break;
        case 'facebook':
            tenantSnapshot = await tenantCollection.where('email.emails', 'array-contains-any', [validateValue]).get();
            break;
        case 'email':
            tenantSnapshot = await tenantCollection.where('email.emails', 'array-contains-any', [validateValue]).get();
    }

    if (tenantSnapshot?.docs?.length > 0) {
        logger.info(`Service: Found tenant ${tenantSnapshot.docs[0].ref.id} for ${feature} ${validateValue}`);
        await admin
            .firestore()
            .collection('companies')
            .doc(tenantSnapshot.docs[0].ref.id)
            .set(
                {
                    [feature]: {
                        validated: true
                    }
                },
                { merge: true }
            );
        return true;
    } else {
        logger.info(`Service: No tenant found for ${feature} ${validateValue}`);
        return false;
    }
}
