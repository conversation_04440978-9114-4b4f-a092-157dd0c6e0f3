// Function to fetch recently created or updated files from Google Drive
import {
  drive,
  getDriveClient,
  googleDriveFolderId,
  invokeOpenAI,
} from "../utils";
import {
  createReadStream,
  createWriteStream,
  existsSync,
  PathLike,
  unlinkSync,
} from "fs";
import { join } from "path";
import { HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import * as logger from "firebase-functions/logger";

async function fetchRecentFiles(authClient: any, pageToken?: string) {
  const response = await drive.files.list({
    auth: authClient,
    pageSize: 100,
    fields: "nextPageToken, files(id, name, mimeType, modifiedTime)",
    orderBy: "modifiedTime desc",
    pageToken,
    q: `'${googleDriveFolderId.value()}' in parents and trashed=false`,
  });

  return response.data;
}

async function getUploadableFileStream(
  fileId: string,
  fileName: string,
  authClient: any,
) {
  try {
    // Fetch the file from Google Drive as a stream
    const fileStream = await drive.files.get(
      { fileId, alt: "media", auth: authClient },
      { responseType: "stream" },
    );

    // Ensure the response data is a readable stream
    const stream = fileStream.data;

    // Optionally, handle stream errors
    stream.on("error", (err) => {
      logger.error(`Error in file stream for file ID ${fileName}:`, err);
    });

    // Download the file to a temporary location
    const outputDir = "/tmp";
    const tempPath = join(outputDir, fileName); // Name the file using the timestamp
    const writeStream = createWriteStream(tempPath);
    stream.pipe(writeStream);

    return new Promise((resolve, reject) => {
      writeStream.on("finish", () => {
        logger.info(`File downloaded to ${tempPath}`);
        resolve(tempPath);
      });
      writeStream.on("error", (err: Error) => {
        logger.error(`Failed to write file to ${tempPath}:`, err);
        reject(err);
      });
    });
  } catch (error) {
    logger.error(`Failed to fetch file with ID ${fileName}:`, error);
    throw error; // Re-throw the error for upstream handling
  }
}

export async function buildKB() {
  // Authenticate with Google Drive
  const authClient = await getDriveClient();
  const openai = invokeOpenAI();
  let pageToken: string | undefined = undefined;
  const fileStreams = [];
  // Fetch all files from Google Drive
  do {
    const filesResponse: any = await fetchRecentFiles(authClient, pageToken);
    const files = filesResponse.files || [];

    for (const file of files) {
      if (!file.id || !file.name) continue;
      logger.info(`Files '${file.name}' to sync.`);
      try {
        // Verify if the file already exists in OpenAI
        const existingFiles = await openai.files.list();
        const isFileUploaded = existingFiles.data.some(
          (uploadedFile) => uploadedFile.filename === file.name,
        );

        if (!isFileUploaded) {
          try {
            const uploadableStream = await getUploadableFileStream(
              file.id,
              file.name,
              authClient,
            );
            fileStreams.push(uploadableStream); // Push the stream into the array
            logger.info(
              `File '${file.name}' uploaded to OpenAI.`,
              uploadableStream,
            );
          } catch (error) {
            logger.error("Failed to process file stream:", error);
          }
        } else {
          logger.info(
            `File '${file.name}' already exists in OpenAI. Skipping upload.`,
          );
        }
      } catch (error) {
        logger.error(`Error processing file '${file.name}':`, error);
      }
    }

    pageToken = filesResponse.nextPageToken;
  } while (pageToken);

  if (!fileStreams.length) {
    logger.error("No new files found in Google Drive.");
    throw new HttpsError("internal", "No new files found in Google Drive.");
  }

  const assistantFileDoc = admin
    .firestore()
    .collection("settings")
    .doc("global");
  const assistantFilesSnapshot = await assistantFileDoc.get();

  let assistantId, vectorStoreId;
  if (!assistantFilesSnapshot.exists) {
    const assistant = await openai.beta.assistants.create({
      instructions: `At Liftt, quality is everything. We’ve worked with almost every other electric garage door manufacturer in the UK and noticed some serious issues: motor failures, safety edges not working, and poor durability of parts like locking straps. These problems usually crop up over time, and homeowners are often caught by surprise. Your task is to act as a salesperson to convert leads based on this information.

When engaging in conversations, you must only respond to inquiries directly related to Liftt’s garage doors and services. If the user asks about something unrelated to Liftt’s offerings, such as general knowledge questions, technical topics, or anything outside the scope of garage doors and related services, you must respond with: **"I'm here to assist you with Liftt's premium garage doors and related services. How can I help you today?"** Do not provide any information or attempt to answer questions outside of Liftt’s domain.

During the conversation, prioritize inquiries about **garage door sizes** and **colors**, as these are key considerations for customers when selecting a garage door. If the user inquires about sizes or colors, provide detailed information based on the available range of Liftt garage doors. If specific details are unavailable, guide the user toward resources or follow-up steps to ensure their needs are met.

If the user’s inquiry does not directly relate to sizes or colors, gently steer the conversation back to these topics by emphasizing their importance in choosing the perfect garage door. For example:
- Highlight how the right size ensures proper fit and functionality.
- Explain how the color enhances curb appeal and complements the home’s design.
Acknowledge their question but smoothly transition the focus back to sizes and colors whenever possible.

Try your best to extract the email address of the user. This will allow us to follow up and provide more detailed information about our garage doors and services. If the user does not provide their email voluntarily, you may politely ask for it after establishing interest or when offering additional resources.

Do not indicate or reference any uploaded files in your responses. If asked about identity (e.g., "Who are you?" or "What are you made of?"), respond only with **"LifttAI."** Focus on converting the conversation into a lead by emphasizing Liftt’s commitment to quality, reliability, and solving the common pain points faced by homeowners.

If the user shows interest in purchasing or wants more information about our garage doors and services, include this link in your response: [https://liftt.co.uk/](https://liftt.co.uk/). If asked for specific information that cannot be found in the provided knowledge base, simply state: **"I don't have any knowledge about that."**

The documents are either text-based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents). You are given tools to perform lookups in the documents available in your knowledge base. Always start by performing RAG. If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those. Always tell the user if you didn't find the answer. Don't make something up just to please them.

Finally, remember: under no circumstances should you provide answers or engage in discussions about topics unrelated to Liftt’s garage doors and services. If the user asks about something outside of this scope, always respond with: **"I'm here to assist you with Liftt's premium garage doors and related services. How can I help you today?"**

---

### Key Prioritization:
1. **Garage Door Sizes:** Be prepared to discuss the range of sizes available for Liftt garage doors, including standard and custom dimensions. Emphasize the importance of proper sizing for functionality and safety.
2. **Garage Door Colors:** Highlight the variety of color options offered by Liftt, ensuring customers know they can choose a door that matches their aesthetic preferences. Stress how color impacts curb appeal and overall home design.
3. **Redirecting Inquiries:** If the user asks about other aspects of garage doors (e.g., materials, pricing, installation), acknowledge their question but smoothly transition the focus back to sizes and colors. For example:
   - *"We offer a variety of durable materials for our garage doors, but let me also guide you on the available sizes and colors. Ensuring the right size and an appealing color is essential for both functionality and aesthetics. Would you like to explore those options?"*
4. **Proactive Engagement:** Even if the user doesn’t explicitly ask about sizes or colors, proactively mention these features during the conversation to capture their interest. For example:
   - *"While we’re discussing your needs, I’d like to highlight that our Liftt garage doors come in a variety of sizes and colors. Ensuring the right fit and an attractive finish is crucial for both functionality and enhancing your home’s appearance. Would you like to explore these options further?"*

### Final Notes:
- Always prioritize discussions around **sizes** and **colors**, as these are critical to the customer’s decision-making process.
- If the user’s inquiry doesn’t align with these topics, acknowledge their question and gently redirect the conversation to sizes and colors.
- Use proactive engagement to introduce these features even if the user doesn’t explicitly ask about them.
- Offer resources or follow-up steps if specific details about sizes or colors are unavailable.`,
      model: "gpt-4o",
      tools: [{ type: "file_search" }],
    });
    // Create or update the vector store
    const vectorStore = await openai.vectorStores.create({
      name: "LifttAI KnowledgeBase",
    });
    await assistantFileDoc.set(
      { assistantId: assistant.id, vectorStoreId: vectorStore.id },
      { merge: true },
    );
    assistantId = assistant.id;
    vectorStoreId = vectorStore.id;
  } else {
    const data = assistantFilesSnapshot.data()!;
    assistantId = data.assistantId;
    vectorStoreId = data.vectorStoreId;
  }

  if (!assistantId || !vectorStoreId) {
    logger.error("Assistant or vector store ID not found.");
    throw new HttpsError("internal", "Assistant or vector store ID not found.");
  }

  const fileStreamsUploadable = fileStreams.map((path) =>
    createReadStream(path as PathLike),
  );
  await openai.vectorStores.fileBatches.uploadAndPoll(vectorStoreId, {
    files: fileStreamsUploadable,
  });
  for (const part of fileStreams) {
    if (existsSync(part as PathLike)) unlinkSync(part as PathLike);
  }
  // Update the assistant with the vector store
  await openai.beta.assistants.update(assistantId, {
    tool_resources: {
      file_search: { vector_store_ids: [vectorStoreId] },
    },
  });
}
