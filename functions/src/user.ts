import * as admin from "firebase-admin";
import { UserFilters } from "./types";
import { logger } from "firebase-functions/v2";
import { onDocumentCreated } from "firebase-functions/v2/firestore";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { requireAuth } from "./utils";
import { firestore } from "firebase-admin";
import Filter = firestore.Filter;
import FieldPath = firestore.FieldPath;

/**
 * Generates Firestore query constraints based on provided filters.
 * @param usersCollection
 * @param filters - The filters to apply to the Firestore query.
 * @returns An array of QueryConstraint objects for Firestore querying.
 */
const generateFirestoreConstraints = (filters: UserFilters = {}): Filter[] => {
  const constraints: Filter[] = [];
  // Apply filters
  if (filters.name) {
    const nameStart = filters.name;
    const nameEnd = filters.name + "\uf8ff";

    const emailFPE = new FieldPath("email");
    const nameFPE = new FieldPath("name");
    const idFPE = new FieldPath("id");

    logger.info(`Test1: ${emailFPE} ${nameFPE} ${idFPE}`);
    logger.info(`Test2: ${nameStart} ${nameEnd}`);
    constraints.push(
      Filter.or(
        Filter.and(
          Filter.where(emailFPE, ">=", nameStart),
          Filter.where(emailFPE, "<=", nameEnd),
        ),
        Filter.and(
          Filter.where(nameFPE, ">=", nameStart),
          Filter.where(nameFPE, "<=", nameEnd),
        ),
        Filter.and(
          Filter.where(idFPE, ">=", nameStart),
          Filter.where(idFPE, "<=", nameEnd),
        ),
      ),
    );
  }
  return constraints;
};

export const onUsersFetchRecords = onCall({ cors: true }, async (request) => {
  try {
    const { data, auth } = request;
    logger.info("Not creating users", data);
    if (!auth) {
      throw new HttpsError("unauthenticated", "Authentication required");
    }
    requireAuth(auth);
    try {
      const { filters, pageSize, page } = data;

      // loading.value = true;
      const usersCollection = admin.firestore().collection("users");

      // Build the query
      const constraints = generateFirestoreConstraints(filters);
      let query;
      try {
        query =
          constraints && constraints.length > 0
            ? usersCollection.where(constraints[0])
            : usersCollection;

        if (filters.role && filters.role.length > 0) {
          query = query.where("role", "in", filters.role);
        }
        if (filters.id) {
          query = query.where("id", "==", filters.id);
        }

        // Apply sorting
        if (filters.sort?.col && filters.sort?.order) {
          query = query.orderBy(filters.sort.col, filters.sort.order);
        }
      } catch (error) {
        query = usersCollection;
        logger.error("Error applying constraints:", error);
      }

      const count = await query.count().get();

      query = query.limit(pageSize).offset(pageSize * (page - 1));
      const snapshot = await query.get();

      return {
        resulta: snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })),
        totalSiza: count.data().count || 0,
      };
    } catch (error) {
      logger.error("Error fetching users:", error);

      return { resulta: [], totalSiza: 0 };
    }
  } catch (error) {
    logger.error("Error creating users", error);
    throw new HttpsError("unknown", "Error fetching users.");
  }
});

const userDoc = "users/{userId}";

export const onUserCreate = onDocumentCreated(userDoc, async (event) => {
  try {
    const userId = event.params.userId;
    const snapshot = event.data!;
    const data = snapshot.data()!;
    const createUser = {
      uid: userId,
      displayName: data.name,
      email: data.email,
      password: "P@ssw0rd",
      emailVerified: true,
      createdAt: Date.now(),
    };
    try {
      if (data.isUpload) await admin.auth().createUser(createUser);
    } catch (err) {
      logger.error(`Error creating user ${userId} from auth.`, err);
    }
  } catch (error) {
    logger.error("Error creating users", error);
  }
});
