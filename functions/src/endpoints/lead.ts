import * as admin from 'firebase-admin';

// Filters for leads
export interface LeadFilters {
    name?: string;
    id?: string;
    email?: string;
    searchTerm?: string;
    searchTerms?: string[];
    number?: string;
    lead_source?: string[];
    lead_status?: string[];
    lead_actions?: string[];
    is_assigned?: boolean;
    mark_for_deletion?: boolean;
    assigned_to?: string;
    is_qualified?: boolean;
    failed_sync?: boolean;
    has_followup_date?: boolean;
    to_followup_date?: number;
    sort?: {
        col: string;
        order: 'asc' | 'desc';
    };
    page?: {
        pageSize?: number;
        page?: number;
        currentPage?: number;
    };
    dateRange?: {
        lastMessageTimeStart?: number;
        lastMessageTimeEnd?: number;
        to_followup_dateStart?: number;
        to_followup_dateEnd?: number;
    };
}

/**
 * Fetches leads from Firestore based on provided filters.
 * @param {LeadFilters} filters - Filters to apply to the query.
 * @returns {Promise<Lead[]>} Promise that resolves to an array of leads.
 */
export const getLeadsFromFirestore = async (filters: LeadFilters = {}) => {
    let query = admin.firestore().collection('leads') as admin.firestore.Query<admin.firestore.DocumentData>;

    // facebook bot messages
    // query = query.where('id', '!=', '64tKak7khn3au4cYuJqb');

    if (filters.mark_for_deletion === true) {
        query = query.where('mark_for_deletion', '==', true);
    } else {
        query = query.where('mark_for_deletion', '==', false);
    }
    // Apply filters
    // Search:
    if (filters.searchTerm) {
        query = query.where('keywords', 'array-contains', filters.searchTerm);
    }
    if (filters.searchTerms) {
        query = query.where('keywords', 'array-contains-any', filters.searchTerms);
    }
    if (filters.failed_sync === true) {
        query = query.where('ai_conversation_summary', '==', '');
    }
    if (filters.lead_source && filters.lead_source.length > 0) {
        query = query.where('lead_source', 'in', filters.lead_source);
    }
    if (filters.lead_actions && filters.lead_actions.length > 0) {
        query = query.where('lead_actions', 'in', filters.lead_actions);
    }
    if (filters.is_qualified !== undefined) {
        query = query.where('is_qualified', '==', filters.is_qualified);
    }

    if (filters.lead_status && filters.lead_status.length > 0) {
        query = query.where('lead_status', 'in', filters.lead_status);
    }

    if (filters.has_followup_date === true) {
        if (filters.dateRange?.lastMessageTimeStart && filters.dateRange?.lastMessageTimeEnd) {
            query = query.where('to_followup_date', '>=', filters.dateRange.lastMessageTimeStart);
            query = query.where('to_followup_date', '<=', filters.dateRange.lastMessageTimeEnd);
        } else {
            const todayStart = new Date();
            todayStart.setHours(0, 0, 0, 0); // Start of today
            query = query.where('to_followup_date', '>=', todayStart.getTime() / 1000);
        }
    } else {
        // Apply date range filters
        if (filters.dateRange?.lastMessageTimeStart && filters.dateRange?.lastMessageTimeEnd) {
            query = query.where('lastMessageTime', '>=', filters.dateRange.lastMessageTimeStart);
            query = query.where('lastMessageTime', '<=', filters.dateRange.lastMessageTimeEnd);
        }
    }

    // Apply sorting
    if (filters.sort?.col && filters.sort?.order) {
        query = query.orderBy(filters.sort.col, filters.sort.order);
    }

    const snapshot = await query.get();
    return snapshot.empty ? [] : snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
};
