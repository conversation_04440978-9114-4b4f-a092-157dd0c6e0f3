import * as logger from 'firebase-functions/logger';
import { openAIKey, twilioAccounSID, twilioAuthTpken, facebookVerifyToken, facebookPageAccessToken, telegramBotToken, sendGridApiKey, assemblyAIApiKey, twilioWhatsappNumber, requireAuth, vapiAIApiKey } from './utils';
import { HttpsError, onCall } from 'firebase-functions/v2/https';
// import { onSchedule } from 'firebase-functions/v2/scheduler';
// import { getFollowUpMessageByIds, handleFollowUps, processFollowUpByIds, sendFollowUpByIds } from './followup';
import { getFollowUpMessageByIds, processFollowUpByIds, sendFollowUpByIds } from './followup';

// export const onAIFollowUp = onSchedule(
//     {
//         schedule: '0 */2 * * *',
//         secrets: [openAIKey, twilioAuthTpken, twilioAccounSID, twilioWhatsappNumber, facebookVerifyToken, facebookPageAccessToken, telegramBotToken, sendGridApiKey, assemblyAIApiKey],
//         cpu: 4,
//         memory: '16GiB',
//         timeoutSeconds: 3600
//     },
//     async () => {
//         try {
//             await handleFollowUps();
//             logger.info('Finished processing all leads.');
//             return;
//         } catch (error) {
//             logger.error('Error failed processing all leads:', error);
//             throw new HttpsError('internal', 'Error failed processing all leads.');
//         }
//     }
// );

// Export the chatbots webhook handler
export const onAIFollowUpByIds = onCall(
    {
        cors: true,
        secrets: [vapiAIApiKey, openAIKey, twilioAuthTpken, twilioAccounSID, twilioWhatsappNumber, facebookVerifyToken, facebookPageAccessToken, telegramBotToken, sendGridApiKey, assemblyAIApiKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;
        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);
        try {
            const customParams = data.params || {};
            const leadIds: string[] = data.id ? [data.id] : data.ids;
            if (leadIds.length === 0) {
                logger.warn('No lead IDs provided.');
                throw new Error('No lead IDs provided.');
            }
            if (!data.tenantId) {
                logger.warn('No company ID provided.');
                throw new Error('No company ID provided.');
            }
            customParams.tenantId = data.tenantId;
            return await processFollowUpByIds(leadIds, customParams);
        } catch (err) {
            logger.error('Error processing lead.', err);
            throw new HttpsError('unknown', 'Error processing lead.');
        }
    }
);

// Export the chatbots webhook handler
export const onAISendFollowUp = onCall(
    {
        cors: true,
        secrets: [vapiAIApiKey, openAIKey, twilioAuthTpken, twilioAccounSID, twilioWhatsappNumber, facebookVerifyToken, facebookPageAccessToken, telegramBotToken, sendGridApiKey, assemblyAIApiKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;
        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);
        try {
            const customParams = data.messageParams || {};
            const message = data.messageParams.message;
            const leadIds: string[] = data.id ? [data.id] : data.ids;
            if (leadIds.length === 0) {
                logger.warn('No lead IDs provided.');
                throw new Error('No lead IDs provided.');
            }
            if (!message && !['call', 'ai_call'].includes(customParams?.channel)) {
                logger.warn('No message provided.');
                throw new Error('No message provided.');
            }
            if (!customParams?.voice && ['call', 'ai_call'].includes(customParams?.channel)) {
                logger.warn('No voice message provided. ' + customParams?.voice);
                throw new Error('No voice message provided. ' + customParams?.voice);
            }
            if (!data.tenantId) {
                logger.warn('No company ID provided.');
                throw new Error('No company ID provided.');
            }
            customParams.tenantId = data.tenantId;
            return await sendFollowUpByIds(leadIds, customParams, message);
        } catch (err) {
            logger.error('Error processing lead.', err);
            throw new HttpsError('unknown', 'Error processing lead.');
        }
    }
);

// Export the chatbots webhook handler
export const onAIGetFollowUpMessage = onCall(
    {
        cors: true,
        secrets: [openAIKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;
        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);
        try {
            const leadIds: string[] = data.id ? [data.id] : data.ids;
            const customParams = data.messageParams || {};
            if (leadIds.length === 0) {
                logger.warn('No lead IDs provided.');
                throw new Error('No lead IDs provided.');
            }

            if (!customParams?.channel) {
                logger.warn('No message channel provided.');
                throw new Error('No message channel provided."');
            }
            if (!data.tenantId) {
                logger.warn('No company ID provided.');
                throw new Error('No company ID provided.');
            }
            customParams.tenantId = data.tenantId;
            return await getFollowUpMessageByIds(leadIds, customParams);
        } catch (err) {
            logger.error('Error processing lead.', err);
            throw new HttpsError('unknown', 'Error processing lead.');
        }
    }
);
