import { defineSecret } from 'firebase-functions/params';
import OpenAI from 'openai';
import { Twilio } from 'twilio/lib/index';
import { google } from 'googleapis';
import { logger } from 'firebase-functions';
import { HttpsError } from 'firebase-functions/v2/https';
import type { CallableRequest } from 'firebase-functions/v2/https';
import sgMail from '@sendgrid/mail';
import * as admin from 'firebase-admin';

export const openAIKey = defineSecret('OPENAI_API_KEY');
export const twilioAccounSID = defineSecret('TWILIO_ACCOUNT_SID');
export const twilioAuthTpken = defineSecret('TWILIO_AUTH_TOKEN');
export const twilioWhatsappNumber = defineSecret('TWILIO_WHATSAPP_NUMBER');
export const googleServiceAccountEmail = defineSecret('GOOGLE_SERVICE_ACCOUNT_EMAIL');
export const googleServiceAccountKey = defineSecret('GOOGLE_SERVICE_ACCOUNT_KEY');
export const googleDriveFolderId = defineSecret('GOOGLE_DRIVE_FOLDER_ID');
export const telegramBotToken = defineSecret('TELEGRAM_BOT_TOKEN');
export const facebookVerifyToken = defineSecret('FACEBOOK_VERIFY_TOKEN');
export const facebookPageAccessToken = defineSecret('FACEBOOK_PAGE_ACCESS_TOKEN');
export const facebookAppId = defineSecret('FACEBOOK_APP_ID');
export const facebookAppSecret = defineSecret('FACEBOOK_APP_SECRET');
export const assemblyAIApiKey = defineSecret('ASSEMBLYAI_API_KEY');
export const sendGridApiKey = defineSecret('SENDGRID_API_KEY');
export const gravityFormConsumerKey = defineSecret('GF_CONSUMER_KEY');
export const gravityFormConsumerSecret = defineSecret('GF_CONSUMER_SECRET');

export const invokeOpenAI = () => new OpenAI({ apiKey: openAIKey.value() });
export const invokeTwilio = () => new Twilio(twilioAccounSID.value(), twilioAuthTpken.value());
export const vapiAIApiKey = defineSecret('VAPI_API_KEY');

// Initialize Google Drive API client
export const drive = google.drive('v3');

// Function to authenticate with Google Drive
export async function getDriveClient() {
    try {
        // Authenticate using JWT
        const auth = new google.auth.JWT({
            email: googleServiceAccountEmail.value(),
            key: googleServiceAccountKey.value().replace(/\\n/gm, '\n'),
            scopes: ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/calendar.events']
        });

        return auth;
    } catch (error) {
        logger.error('Error authenticating with Google Drive:', error);
        throw new Error('Failed to authenticate with Google Drive.');
    }
}

export async function getCalendarClient() {
    try {
        // Authenticate using JWT
        const auth = new google.auth.JWT({
            email: googleServiceAccountEmail.value(),
            key: googleServiceAccountKey.value().replace(/\\n/gm, '\n'),
            scopes: ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events', 'https://www.googleapis.com/auth/gmail.send'],
            subject: '<EMAIL>',
            keyId: '113018441059461744732'
        });

        return auth;
    } catch (error) {
        logger.error('Error authenticating with Google Calendar:', error);
        throw new Error('Failed to authenticate with Google Calendar.');
    }
}
export const calendar = google.calendar('v3');
// Refactored requireAuth function
export function requireAuth(auth: CallableRequest['auth'] | undefined | null): CallableRequest['auth'] {
    if (!auth) {
        throw new HttpsError('unauthenticated', 'Only authenticated users can invoke this function.');
    }
    return auth;
}

export const getEmailTemplate = async (contentStr: string, tenantId?: string) => {
    let template = '';
    if (tenantId) {
        const tenantCollection = admin.firestore().collection('companies');
        const tenantDocRef = tenantCollection.doc(tenantId);
        const tenantDocSnapshot = await tenantDocRef.get();
        template = tenantDocSnapshot.data()?.email.template ?? (await getSettingEmailTemplate('main_template'));
    } else {
        template = await getSettingEmailTemplate('main_template');
    }
    if (template) {
        const hasMatch = /\{\{content\}\}/.test(template);
        if (hasMatch) {
            return template.replace(/\{\{content\}\}/g, contentStr);
        } else {
            return contentStr;
        }
    }
    return contentStr;
};

export const getSettingEmailTemplate = async (templateName: string) => {
    const settingsCollection = admin.firestore().collection('settings');
    const settingsDocRef = settingsCollection.doc('global');
    const settingsDocSnapshot = await settingsDocRef.get();
    const template = settingsDocSnapshot.data()?.email_templates[templateName] ?? '';
    logger.info('Global Email template found:', template);
    return template;
};
export const tokenKey = 'F7pIGUyA38LJP/5NDsPxn0yaVOxaN0LqZMOIQ5C0ubmajhx1Enp1wRicRAmANKb2\n';

export const lifttCalendarId = '<EMAIL>';

// Define secrets for Algolia credentials
export const algoliaAppId = defineSecret('ALGOLIA_APP_ID');
export const algoliaApiKey = defineSecret('ext-firestore-algolia-search-ALGOLIA_API_KEY');

export const gravityFormApiUrl = 'https://liftt.co.uk/wp-json/gf/v2';

export async function sendEmail(data: any): Promise<any> {
    try {
        // Use SendGrid to send the response email
        sgMail.setApiKey(sendGridApiKey.value());
        const msg = {
            to: data.to, // Recipient's email address
            from: data.from, // Dynamically set the sender email
            subject: data.subject, // Email subject
            html: data.message, // Plain text body
            cc: data?.cc,
            bcc: data?.bcc
        };

        if (data?.cc && data?.cc.includes(data.to)) {
            delete msg.cc;
        }
        logger.info('Email template found:', msg);
        await sgMail.send(msg);
        logger.info('Email sent successfully.');
        return true;
    } catch (error) {
        logger.error(error);
        return error;
    }
}

export const ccEmails = ['<EMAIL>'];
export const noReply = '<EMAIL>';
