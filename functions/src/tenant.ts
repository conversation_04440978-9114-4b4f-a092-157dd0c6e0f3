import * as admin from 'firebase-admin';

import * as logger from 'firebase-functions/logger';
import { ccEmails, getEmailTemplate, getSettingEmailTemplate, noReply, sendEmail, sendGridApiKey } from './utils';
import { onDocumentWritten } from 'firebase-functions/v2/firestore';
import { HttpsError, onCall } from 'firebase-functions/v2/https';

const tenantDoc = 'companies/{tenantId}';

const opts = {
    document: tenantDoc,
    cors: true,
    secrets: [sendGridApiKey]
};

export const onTenantWritten = onDocumentWritten(opts, async (event) => {
    try {
        const snapshot = event.data!;

        const tenantId = event.params.tenantId;
        let message: any;
        let subject: any;
        let email: any;

        const documentExistsAfter = snapshot.after.exists;
        const documentExistedBefore = snapshot.before.exists;
        if (!documentExistsAfter && documentExistedBefore) {
            subject = 'Company Deleted';
            email = snapshot.before.data()!.emailAddress;
            const db = admin.firestore();
            const dateDeleted = new Date().toLocaleString('en-GB');
            const messageDeleted = await getSettingEmailTemplate('company_deleted');
            message = messageDeleted.replace(/\{\{date_deleted\}\}/g, dateDeleted);
            const usersCollection = db.collection('users');
            const leadsCollection = db.collection('leads');

            const usersSnapshot = await usersCollection.where('tenantId', '==', tenantId).get();
            const leadsSnapshot = await leadsCollection.where('tenantId', '==', tenantId).get();
            const batch = admin.firestore().batch();
            logger.info('Deleting users and leads.', usersSnapshot.docs.length, leadsSnapshot.docs.length);
            usersSnapshot.docs.forEach(async (doc) => {
                await admin.auth().deleteUser(doc.ref.id);
            });
            leadsSnapshot.docs.forEach(async (doc) => {
                batch.delete(doc.ref);
            });
            await batch.commit();
            logger.info('Tenant deleted.', tenantId);
        } else {
            const isApproved = snapshot.after.data()!.approved;
            email = snapshot.after.data()!.emailAddress;
            const companyName = snapshot.after.data()!.name;

            if (isApproved) {
                subject = 'Company Registration Approved';
                const messageRegistered = await getSettingEmailTemplate('company_registration_approved');
                message = messageRegistered.replace(/\{\{company_name\}\}/g, companyName);
                logger.info('Tenant registered and approved.', tenantId);
            } else {
                subject = 'Company Registration Pending';
                const messageAwaiting = await getSettingEmailTemplate('company_registration_requested');
                message = messageAwaiting.replace(/\{\{company_name\}\}/g, companyName);
                logger.info('Tenant registered and pending approval.', tenantId);
            }
        }
        const messageTemplate = await getEmailTemplate(message);
        await sendEmail({
            to: email,
            from: noReply,
            subject: subject,
            message: messageTemplate,
            cc: ccEmails
        });
    } catch (error) {
        logger.error('Error fetching tenant data, updating user meta count, or updating user document:', error);
    }
});
export const onRegisterTenant = onCall({ cors: true }, async (request) => {
    try {
        const createUser = {
            displayName: request.data.userFullName,
            email: request.data.emailAddress,
            password: request.data.userPassword,
            emailVerified: true
        };
        delete request.data.userFullName;
        delete request.data.userPassword;
        // get user by email
        let user;
        try {
            user = await admin.auth().getUserByEmail(request.data.emailAddress);
        } catch (error: any) {
            if (error.code === 'auth/user-not-found') {
                user = await admin.auth().createUser(createUser);
            } else {
                // Re-throw other errors
                throw error;
            }
        }

        const tenantCollection = admin.firestore().collection('companies');
        const templatesDoc = admin.firestore().collection('templates').doc('default');
        const templatesSnapshot = await templatesDoc.get();
        const templatesData = templatesSnapshot.data()!;
        const docRef = await tenantCollection.add({ ...request.data, ...templatesData });

        const data = {
            phone: request.data?.phone,
            name: user.displayName,
            email: user.email,
            id: user.uid,
            role: 'company_admin',
            tenantId: docRef.id,
            createdAt: Date.now()
        };

        await admin.firestore().collection('users').doc(user.uid).set(data, { merge: true });

        return docRef.id;
    } catch (err) {
        logger.error('Error creating tenant.', err);
        throw new HttpsError('unknown', 'Error creating tenant.');
    }
});
