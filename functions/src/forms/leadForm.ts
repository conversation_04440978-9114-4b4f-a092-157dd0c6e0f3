import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';

export async function handelLeadDocumentForm(req: any) {
    const leadsSettingDoc = admin.firestore().collection('companies').doc('Lw225tYPYTssrWaZ4ipb');
    const leadsSettingSnapshot = await leadsSettingDoc.get();
    const leadsSetting = leadsSettingSnapshot.data()!;

    const findFormById = (data: any, targetFormId: string): any | null => (data.forms ? Object.values(data.forms).find((form: any) => form.form_id === targetFormId) || null : null);

    logger.info('Forms', leadsSetting.forms);
    logger.info('id', req.form_id);
    const formData: any = findFormById(leadsSetting, req.form_id);
    logger.info('formData', formData);

    if (!formData) throw new Error('Form could not be found');

    const leadDetailsArray: Record<string, any> = {};
    for (const key of Object.keys(req)) {
        const formKey = formData[key];
        if (formKey) {
            leadDetailsArray[formKey] = req[key];
        }
    }

    logger.info('leadDetailsArray', leadDetailsArray);
    if (!leadDetailsArray) throw new Error('Form Lead Details could not be found');

    if (leadDetailsArray.phone && leadDetailsArray.phone.startsWith('0')) {
        leadDetailsArray.phone = leadDetailsArray.phone?.replace(/^0/, '+44');
    }

    const leadsCollection = admin.firestore().collection('leads');
    const currentTime = Math.floor(Date.now() / 1000);

    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection.where('source_id', '==', leadDetailsArray.email).where('lead_source', '==', 'gravity_form').where('lead_actions', '==', 'quoted').get();
    let leadDocRef: FirebaseFirestore.DocumentReference<FirebaseFirestore.DocumentData>, leadData: any;

    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    } else {
        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            id: leadDocRef.id,
            source_id: leadDetailsArray.email,
            name: `${leadDetailsArray?.first_name} ${leadDetailsArray?.last_name}`.trim() || '',
            email: leadDetailsArray.email,
            lead_details: leadDetailsArray,
            phone: leadDetailsArray.phone,
            address: `${leadDetailsArray?.address || ''} ${leadDetailsArray?.address1 || ''} ${leadDetailsArray?.address2 || ''} ${leadDetailsArray?.address3 || ''} ${leadDetailsArray?.postcode || ''} ${leadDetailsArray?.city || ''}`.trim() || '',

            lead_source: 'gravity_form', // Track the source of the lead
            lead_actions: 'quoted', // Track the source of the lead
            lastMessageTime: currentTime,
            conversations: [
                {
                    role: 'user',
                    message: `Thank you for providing the quote. I’ve reviewed everything, and it looks good to me. I’d like to move forward with the purchase.

To confirm, you may find all the necessary details regarding my selection, including the door size, color, delivery/installation address, contact number, and email address. Everything should be in order for you to proceed.

Could you please let me know the next steps to finalize the order? Also, if there’s anything I need to prepare before the installation, please let me know.`,
                    lastMessageTime: currentTime
                }
            ]
        };
    }

    leadData.ai_conversation_summary = '';
    leadData.lead_actions = 'quoted';
    leadData.conversations_count = 0;
    leadData.mark_for_deletion = false;
    leadData.tenantId = 'Lw225tYPYTssrWaZ4ipb';
    leadData.keywords = [...(leadData.name ? [leadData.name] : []), ...(leadData.email ? [leadData.email] : []), ...(leadData.phone ? [leadData.phone] : []), ...(leadData.id ? [leadData.id] : []), ...(leadData.tenantId ? [leadData.tenantId] : [])];

    // set latestAppointmentDate to lead
    leadData.latestAppointmentDate = leadData?.latestAppointmentDate || 0;

    leadDocRef.set(leadData, { merge: true });

    return { leadDocRef: leadDocRef, leadData: leadData };
}
