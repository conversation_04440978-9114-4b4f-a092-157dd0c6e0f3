import { onCall } from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";
import algoliasearch from "algoliasearch";
import { algoliaApiKey, algoliaAppId, requireAuth } from "./utils";

// Create the search function
export const searchLeads = onCall(
  {
    cors: true,
    secrets: [algoliaAppId, algoliaApiKey],
    maxInstances: 10,
  },
  async (request) => {
    const { data, auth } = request;

    // Require authentication
    if (!auth) {
      throw new Error("Authentication required");
    }
    requireAuth(auth);

    try {
      // Extract search parameters
      const { searchTerm, page = 0, hitsPerPage = 10 } = data;

      if (!searchTerm || typeof searchTerm !== "string") {
        throw new Error("Valid searchTerm is required");
      }

      // Initialize Algolia client
      const client = algoliasearch(algoliaAppId.value(), algoliaApiKey.value());

      // Use the leads_search index
      const index = client.initIndex("leads_search");

      // Configure the search to prioritize specific attributes
      const searchResults = await index.search(searchTerm, {
        attributesToRetrieve: ["id", "name", "email", "phone", "address"],
        // Specify which attributes to search in
        restrictSearchableAttributes: ["name", "email", "phone", "address"],
        // Highlight matching text
        highlightPreTag: "<strong>",
        highlightPostTag: "</strong>",
        // Pagination parameters
        page,
        hitsPerPage,
      });

      logger.info(
        `Search for "${searchTerm}" returned ${searchResults.hits.length} results (page ${page})`,
      );

      // Calculate pagination information
      const totalPages = Math.ceil(searchResults.nbHits / hitsPerPage);

      return {
        results: searchResults.hits,
        totalHits: searchResults.nbHits,
        processingTimeMS: searchResults.processingTimeMS,
        pagination: {
          currentPage: searchResults.page,
          totalPages,
          hitsPerPage: searchResults.hitsPerPage,
          hasNextPage: searchResults.page < totalPages - 1,
          hasPreviousPage: searchResults.page > 0,
          firstPage: 0,
          lastPage: Math.max(0, totalPages - 1),
          nextPage:
            searchResults.page < totalPages - 1 ? searchResults.page + 1 : null,
          previousPage: searchResults.page > 0 ? searchResults.page - 1 : null,
        },
      };
    } catch (error) {
      logger.error("Error searching Algolia:", error);
      throw new Error(
        `Search failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  },
);
