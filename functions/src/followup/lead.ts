import { getEmailTemplate, invoke<PERSON>penA<PERSON>, invoke<PERSON><PERSON><PERSON>, sendGridApiKey, vapiAIApiKey } from '../utils';
import { HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';
import { sendMessengerMessage, sendTelegramMessage } from '../chatbots';
import sgMail from '@sendgrid/mail';
// import VoiceResponse from "twilio/lib/twiml/VoiceResponse";
import { resetFollowUpDatesForCalls } from '../calls';
// import { getCallScript } from './script';

interface BlandCallParams {
    from: string;
    model: 'base' | 'turbo';
    voice: string;
    record: boolean;
    language: string; // Use 'en-GB' format
    timezone: string;
    temperature: number;
    max_duration: number;
    phone_number: string;
    first_sentence?: string;
    background_track: 'office' | 'cafe' | 'restaurant' | 'none' | null;
    wait_for_greeting: boolean;
    noise_cancellation: boolean;
    block_interruptions: boolean;
    ignore_button_press: boolean;
    request_data: object;
    pronunciation_guide: Array<{
        word: string;
        pronunciation: string;
    }>;
    interruption_threshold: number;
    tools?: string[]; // Array of tool IDs
    webhook: string;
    task?: string;
    pathway_id?: string;
}

export async function handleFollowUps() {
    try {
        await processFollowUps();
    } catch (error) {
        logger.error('Error handling user query:', error);
        throw new HttpsError('internal', 'Failed to handle user query.');
    }
}

export async function processFollowUp(leadData: any, followUpParams: any, leadId: string, promptMain: string, channel: string) {
    try {
        const openai = invokeOpenAI();

        let messageFormatting = `Format your response using markdown.`;
        switch (channel) {
            case 'whatsapp':
                messageFormatting = `You are a helpful AI optimized for WhatsApp communication. Your responses must align with the platform’s informal and conversational style, using emojis sparingly where appropriate to enhance engagement. The chat history will be provided as a variable, and you should always refer to it or consider it important when crafting your response. Ensure your replies are concise, clear, and formatted in short paragraphs or bullet points if necessary for readability.`;
                break;
            case 'facebook':
                messageFormatting = `You are a helpful AI designed to provide responses tailored specifically for Facebook Messenger. You will adhere to the platform's formatting and style guidelines, ensuring your replies are concise, engaging, and suitable for casual conversations. The chat history will be provided as a variable, and you must analyze it to ensure your responses are contextually relevant and consistent with previous interactions. Always prioritize clarity and brevity while maintaining a friendly tone.`;
                break;
            case 'telegram':
                messageFormatting = `You are a helpful AI tailored for Telegram, a platform known for its versatility and support for rich text formatting. Your responses should utilize Markdown formatting (e.g., italics , bold , code, etc.) to enhance readability and emphasize key points when needed. The chat history will be provided as a variable, and you must analyze it to maintain contextual relevance and coherence with past interactions. Balance professionalism with a conversational tone, adapting your style based on the user's preferences evident from the chat history.`;
                break;
            case 'email':
            case 'gravity_form':
                messageFormatting = `You are a helpful AI designed to craft responses specifically for email communication in HTML format. Your replies should adhere to professional formatting standards, ensuring clarity, conciseness, and a polished tone. Use proper salutations (e.g., \`<p>Dear [Name],</p>\` or \`<p>Hi [Name],</p>\`) and closings (e.g., \`<p>Best regards,</p>\` or \`<p>Sincerely,</p>\`) to maintain professionalism. The chat history will be provided as a variable, and you must analyze it to ensure your responses are contextually relevant, consistent with previous interactions, and address all key points raised in the conversation.

Structure your response using HTML tags for readability and visual appeal:
- Use \`<p>\` tags for paragraphs.
- Use \`<strong>\` for bold text to emphasize key points.
- Use \`<em>\` for italicized text where appropriate.
- Use \`<ul>\` and \`<li>\` for bullet points if listing information.
- Avoid unnecessary jargon unless it aligns with the recipient's communication style evident from the chat history.

Ensure the email is mobile-friendly and visually balanced, avoiding overly long blocks of text.`;
                break;
        }

        const chatmessages = [];
        chatmessages.push({
            role: 'system',
            content:
                'Customer Data: ' +
                JSON.stringify({
                    name: leadData?.name || '',
                    email: leadData?.email || '',
                    phone: leadData?.phone || '',
                    address: leadData?.address || '',
                    lead_source: leadData.lead_source
                })
        });
        if (leadData.conversations?.length && leadData.conversations?.length > 0) {
            let convos = [];
            const limitedMessageHistory = leadData.conversations.filter((message: any) => message.role === 'user' || message.role === 'assistant');

            // Ensure the message history is in the correct format
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            if (limitedMessageHistory?.length) {
                convos = limitedMessageHistory
                    .filter((msg: any) => msg.content)
                    .map((msg: any) => ({
                        role: msg.role,
                        content: msg.message
                    }));
            } else {
                convos = [];
            }
            if (convos.length > 0) {
                chatmessages.push({
                    role: 'system',
                    content: 'Conversations: ' + JSON.stringify(convos)
                });
            }
        }
        // Format the response to the user
        chatmessages.push({
            role: 'system',
            content: messageFormatting
        });
        chatmessages.push({
            role: 'system',
            content: promptMain
        });

        followUpParams.messages = chatmessages;

        try {
            logger.info(`Params request to be sent to to OpenAI`, followUpParams);

            // Retrieve the assistant's response
            const response = await openai.chat.completions.create(followUpParams);
            let message = response.choices[0].message.content as string;
            if (message) {
                message = message
                    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
                    .replace(/\*(.*?)\*/g, '$1') // Remove italic
                    .replace(/\[(.*?)\]\((.*?)\)/g, '$2'); // Remove link markdown;
                logger.info(`Lead ${leadId} Successfully processed user query to OpenAI`, message);

                return message;
            } else {
                throw new Error('Empty response from OpenAI');
            }
        } catch (error) {
            logger.error(`Lead ${leadId} Failed to get a response from OpenAI:`, error);
            return null;
        }
    } catch (error) {
        logger.error(`Lead ${leadId} Failed to process.`, error);
        return null;
    }
}

export async function getFollowUpParams(companyId: string) {
    const aiFollowUpDoc = admin.firestore().collection('companies').doc(companyId);
    const aiFollowUpSnapshot = await aiFollowUpDoc.get();
    const { followup } = aiFollowUpSnapshot.data()!;
    return followup;
}

export async function getConversationSettings(companyId: string) {
    const conversationsDoc = admin.firestore().collection('companies').doc(companyId);
    const conversationsSnapshot = await conversationsDoc.get();
    const data = conversationsSnapshot.data()!;
    logger.info(`Conversation settings`, data);
    return data;
}
async function processFollowUps() {
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
    const twoHoursAgoTimestamp = Math.floor(twoHoursAgo.getTime() / 1000);
    const currentTime = Math.floor(Date.now() / 1000);

    const leadsCollection = admin.firestore().collection('leads');

    const batchSize = 5; // Number of records per page

    let lastVisibleDoc: FirebaseFirestore.QueryDocumentSnapshot | undefined;
    let hasMoreLeads = true;

    let followUpParams, promptMain;

    while (hasMoreLeads) {
        // Build the query with pagination
        let leadsQuery = leadsCollection
            .where('to_followup_date', '>=', twoHoursAgoTimestamp)
            .where('to_followup_date', '<=', currentTime)
            .where('is_qualified', '==', true)
            .where('lead_actions', 'in', ['inquired', 'quoted'])
            .orderBy('to_followup_date', 'desc') // Ensure consistent ordering
            .limit(batchSize);

        if (lastVisibleDoc) {
            leadsQuery = leadsQuery.startAfter(lastVisibleDoc); // Start after the last document of the previous page
        }

        const leadsSnapshot = await leadsQuery.get();

        if (leadsSnapshot.empty || leadsSnapshot.docs.length === 0) {
            hasMoreLeads = false; // No more leads to process
            break;
        }

        logger.info(`Processing ${leadsSnapshot.docs.length} leads`);

        const batch = admin.firestore().batch();

        for (const leadDoc of leadsSnapshot.docs) {
            const leadData = leadDoc.data()!;
            if (!leadData?.ai_conversation_summary?.follow_up_recommendation?.should_pursue_followup) {
                logger.info(`Lead ${leadDoc.id} should not pursue followup. Skipping.`, leadData.ai_conversation_summary?.follow_up_recommendation);
                continue;
            }

            followUpParams = await getFollowUpParams(leadData.tenantId);
            promptMain = followUpParams.prompt;
            delete followUpParams.prompt;
            delete followUpParams.datesWithoutAppointments;

            const conversations = leadData.conversations || [];
            const leadDocRef = leadDoc.ref;
            const leadId = leadDoc.id;

            let followUpCount = leadData?.follow_up_count || 0;

            if (followUpCount === leadData?.ai_conversation_summary?.follow_up_recommendation?.recurrence_strategy?.max_followups) {
                logger.info(`Lead ${leadDoc.id} has reached max followups. Skipping.`, leadData.ai_conversation_summary?.follow_up_recommendation);
                continue;
            }

            const followUpDate = leadData?.to_followup_date || leadData?.ai_conversation_summary?.follow_up_recommendation?.to_followup_date || currentTime;

            const frequencyDays = leadData?.ai_conversation_summary?.follow_up_recommendation?.recurrence_strategy?.frequency_days || 1;

            let nextFollowUpDateTime = followUpDate + frequencyDays * 86400;

            // Convert followUpDate to Date object (milliseconds)
            const followUpDateObj = new Date(followUpDate * 1000);

            // Simulate UK time (GMT+1) by adding 1 hour to UTC hour
            const ukHour = (followUpDateObj.getUTCHours() + 1) % 24;

            if (ukHour < 9) {
                // If before 6am UK time, set to 3 hours from now
                nextFollowUpDateTime = Math.floor(Date.now() / 1000) + 3 * 3600;

                logger.log(
                    'Invalid Operation Hours: Next follow-up date (UK time):',
                    new Date(nextFollowUpDateTime * 1000).toLocaleString('en-GB', {
                        timeZone: 'Europe/London',
                        hour12: false
                    })
                );
            } else if (ukHour >= 19) {
                // If after 7pm UK time, set to tomorrow at 6am UK time (5am UTC)
                const tomorrow = new Date(followUpDateObj);
                tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
                tomorrow.setUTCHours(5, 0, 0, 0); // 5am UTC == 6am UK
                nextFollowUpDateTime = Math.floor(tomorrow.getTime() / 1000);

                logger.log(
                    'Invalid Operation Hours:  Next follow-up date (UK time):',
                    new Date(nextFollowUpDateTime * 1000).toLocaleString('en-GB', {
                        timeZone: 'Europe/London',
                        hour12: false
                    })
                );
            } else {
                logger.log(
                    'Valid Operation Hours:  Next follow-up date (UK time):',
                    new Date(nextFollowUpDateTime * 1000).toLocaleString('en-GB', {
                        timeZone: 'Europe/London',
                        hour12: false
                    })
                );
                if (!['call', 'ai_call'].includes(leadData.lead_source)) {
                    const response = await processFollowUp(leadData, followUpParams, leadId, promptMain, leadData.lead_source);
                    const conversations = leadData.conversations?.length > 0 ? leadData.conversations : [];
                    let isWhatsappTemplate = false;
                    if (leadData.conversations?.length > 0 && leadData.lead_source === 'whatsapp') {
                        isWhatsappTemplate = checkIfWhatsappTemplate(leadData);
                    }
                    conversations.push({
                        role: 'assistant',
                        message: response,
                        lastMessageTime: currentTime
                    });
                    if (!response) {
                        logger.warn(`Lead ${leadId} Failed to process.`);
                    } else {
                        if (response) {
                            try {
                                await handleSendingOfFollowUp(
                                    {
                                        channel: leadData.lead_source,
                                        voice: null,
                                        isWhatsappTemplate: isWhatsappTemplate
                                    },
                                    response,
                                    leadData
                                );
                                followUpCount += 1;
                            } catch (error) {
                                logger.error(`Lead ${leadId} Failed to send followup message:`, error);
                            }
                        }
                    }
                } else {
                    logger.log(`Lead ${leadId} is a call lead. Skipping follow-up processing.`);
                    try {
                        await handleSendingOfFollowUp(
                            {
                                channel: leadData.lead_source,
                                voice: null,
                                isWhatsappTemplate: false
                            },
                            '',
                            leadData
                        );
                    } catch (error) {
                        logger.error(`Lead ${leadId} Failed to send followup message:`, error);
                    }
                }
            }

            // Update the lead document with the AI summary
            batch.set(
                leadDocRef,
                {
                    follow_up_count: followUpCount,
                    to_followup_date: nextFollowUpDateTime || 0,
                    conversations: conversations
                },
                { merge: true }
            );
        }

        // Commit the batch updates
        await batch.commit();

        // Update the last visible document for the next iteration
        lastVisibleDoc = leadsSnapshot.docs[leadsSnapshot.docs.length - 1];
    }
}

export async function processFollowUpByIds(leadIds: string[], customParams: any) {
    if (leadIds.length === 0) {
        logger.warn('No lead IDs provided.');
        throw new Error('No lead IDs provided.');
    }

    const batch = admin.firestore().batch();
    const leadsCollection = admin.firestore().collection('leads');
    const followUpParams = await getFollowUpParams(customParams.tenantId);
    const promptMain = followUpParams.prompt;
    const script = customParams.script;
    delete followUpParams.prompt;
    delete followUpParams.datesWithoutAppointments;

    const currentTime = Math.floor(Date.now() / 1000);

    // Loop through each lead ID
    const promises = leadIds.map(async (leadId) => {
        // Use leadId to query for the specific lead
        const leadDocRef = leadsCollection.doc(leadId);
        const leadDoc = await leadDocRef.get();

        if (!leadDoc.exists) {
            logger.warn(`Lead with ID ${leadId} not found.`);
        } else {
            const leadData = leadDoc.data()!;
            const conversations = leadData.conversations || [];
            let isWhatsappTemplate = false;
            const msgChannel = customParams?.channel || leadData.lead_source;
            if (leadData.conversations?.length > 0 && msgChannel === 'whatsapp') {
                isWhatsappTemplate = checkIfWhatsappTemplate(leadData);
            }

            const followUpDate = leadData?.to_followup_date || leadData?.ai_conversation_summary?.follow_up_recommendation?.to_followup_date || currentTime;

            const frequencyDays = leadData?.ai_conversation_summary?.follow_up_recommendation?.recurrence_strategy?.frequency_days || 1;

            let nextFollowUpDateTime = followUpDate + frequencyDays * 86400;

            // Convert followUpDate to Date object (milliseconds)
            const followUpDateObj = new Date(followUpDate * 1000);

            // Simulate UK time (GMT+1) by adding 1 hour to UTC hour
            const ukHour = (followUpDateObj.getUTCHours() + 1) % 24;

            if (ukHour < 9) {
                // If before 6am UK time, set to 3 hours from now
                nextFollowUpDateTime = Math.floor(Date.now() / 1000) + 3 * 3600;
                logger.log(
                    'Invalid Operation Hours: Next follow-up date (UK time):',
                    new Date(nextFollowUpDateTime * 1000).toLocaleString('en-GB', {
                        timeZone: 'Europe/London',
                        hour12: false
                    })
                );
            } else if (ukHour >= 19) {
                // If after 7pm UK time, set to tomorrow at 6am UK time (5am UTC)
                const tomorrow = new Date(followUpDateObj);
                tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
                tomorrow.setUTCHours(5, 0, 0, 0); // 5am UTC == 6am UK
                nextFollowUpDateTime = Math.floor(tomorrow.getTime() / 1000);
                logger.log(
                    'Invalid Operation Hours: Next follow-up date (UK time):',
                    new Date(nextFollowUpDateTime * 1000).toLocaleString('en-GB', {
                        timeZone: 'Europe/London',
                        hour12: false
                    })
                );
            } else {
                logger.log(
                    'Valid Operation Hours: Next follow-up date (UK time):',
                    new Date(nextFollowUpDateTime * 1000).toLocaleString('en-GB', {
                        timeZone: 'Europe/London',
                        hour12: false
                    })
                );
                if (!['call', 'ai_call'].includes(leadData.lead_source)) {
                    const response = await processFollowUp(leadData, followUpParams, leadId, promptMain, leadData.lead_source);
                    conversations.push({
                        role: 'assistant',
                        message: response,
                        lastMessageTime: currentTime
                    });
                    if (response) {
                        try {
                            await handleSendingOfFollowUp(
                                {
                                    channel: leadData.lead_source,
                                    voice: null,
                                    isWhatsappTemplate: isWhatsappTemplate,
                                    script: script
                                },
                                response,
                                leadData
                            );
                        } catch (error) {
                            logger.error(`Lead ${leadId} Failed to send followup message:`, error);
                        }
                    }

                    // Update the lead document with the AI summary
                    batch.set(
                        leadDocRef,
                        {
                            to_followup_date: nextFollowUpDateTime || 0,
                            conversations: conversations
                        },
                        { merge: true }
                    );
                } else {
                    logger.log(`Lead ${leadId} is a call lead. Skipping follow-up processing.`);
                    try {
                        await handleSendingOfFollowUp(
                            {
                                channel: leadData.lead_source,
                                voice: null,
                                isWhatsappTemplate: false,
                                script: script
                            },
                            '',
                            leadData
                        );
                    } catch (error) {
                        logger.error(`Lead ${leadId} Failed to send followup message:`, error);
                    }
                }
            }
        }
    });

    await Promise.all(promises);
    await batch.commit();
    return true;
}

export async function sendFollowUpByIds(leadIds: string[], customParams: any, message: string) {
    if (leadIds.length === 0) {
        logger.warn('No lead IDs provided.');
        throw new Error('No lead IDs provided.');
    }

    // const followUpParams = await getFollowUpParams();

    const batch = admin.firestore().batch();
    const leadsCollection = admin.firestore().collection('leads');
    const currentTime = Math.floor(Date.now() / 1000);

    // Loop through each lead ID
    const promises = leadIds.map(async (leadId) => {
        // Use leadId to query for the specific lead
        const leadDocRef = leadsCollection.doc(leadId);
        const leadDoc = await leadDocRef.get();

        if (!leadDoc.exists) {
            logger.warn(`Lead with ID ${leadId} not found.`);
        } else {
            const leadData = leadDoc.data()!;
            const frequencyDays = leadData?.ai_conversation_summary?.follow_up_recommendation?.recurrence_strategy?.frequency_days || 1;
            const followUpDate = leadData?.to_followup_date || leadData?.ai_conversation_summary?.follow_up_recommendation?.to_followup_date || currentTime;
            const nextFollowUpDateTime = followUpDate + frequencyDays * 86400;

            if (!['call', 'ai_call'].includes(customParams.channel)) {
                const conversations = leadData.conversations || [];
                let isWhatsappTemplate = false;
                if (leadData.conversations?.length > 0 && customParams.channel === 'whatsapp') {
                    isWhatsappTemplate = checkIfWhatsappTemplate(leadData);
                }

                customParams.isWhatsappTemplate = isWhatsappTemplate;
                conversations.push({
                    role: 'assistant',
                    message: message,
                    lastMessageTime: currentTime
                });
                // Update the lead document with the AI summary
                batch.set(
                    leadDocRef,
                    {
                        to_followup_date: nextFollowUpDateTime || 0,
                        conversations: conversations
                    },
                    { merge: true }
                );
                if (message) {
                    try {
                        await handleSendingOfFollowUp(customParams, message, leadData);
                    } catch (error) {
                        logger.error(`Lead ${leadId} Failed to send followup message:`, error);
                    }
                }
            } else {
                logger.log(`Lead ${leadId} is a call lead. Skipping follow-up processing.`);
                try {
                    customParams.isWhatsappTemplate = false;
                    await handleSendingOfFollowUp(customParams, '', leadData);
                } catch (error) {
                    logger.error(`Lead ${leadId} Failed to send followup message:`, error);
                }
            }
        }
    });

    await Promise.all(promises);
    await batch.commit();
    return true;
}

export async function getFollowUpMessageByIds(leadIds: string[], customParams: any) {
    if (leadIds.length === 0) {
        logger.warn('No lead IDs provided.');
        throw new Error('No lead IDs provided.');
    }

    // const batch = admin.firestore().batch();
    const leadsCollection = admin.firestore().collection('leads');
    const followUpParams = await getFollowUpParams(customParams.tenantId);
    const promptMain = followUpParams.prompt;
    delete followUpParams.prompt;
    delete followUpParams.datesWithoutAppointments;

    // const currentTime = Math.floor(Date.now() / 1000);
    let response;
    // Loop through each lead ID
    const promises = leadIds.map(async (leadId) => {
        // Use leadId to query for the specific lead
        const leadDocRef = leadsCollection.doc(leadId);
        const leadDoc = await leadDocRef.get();

        if (!leadDoc.exists) {
            logger.warn(`Lead with ID ${leadId} not found.`);
        } else {
            const leadData = leadDoc.data()!;
            response = await processFollowUp(leadData, followUpParams, leadId, promptMain, customParams.channel);
        }
    });

    await Promise.all(promises);
    // await batch.commit();
    if (leadIds.length === 1) {
        return response;
    } else return true;
}

export async function handleSendingOfFollowUp(
    provider: {
        channel: string;
        message?: string;
        voice?: string | null;
        isWhatsappTemplate?: boolean;
        script?: string;
        source?: string | null | undefined;
    },
    message: string | '',
    leadData: any
) {
    try {
        let vapiVoiceSettings;
        const conversationSettings = await getConversationSettings(leadData.tenantId);
        logger.info(`Provider`, provider);
        if (!provider?.script) {
            provider.script = ['inquired', 'quoted', 'booked'].includes(leadData?.lead_actions) ? leadData?.lead_actions : 'inquired';
        }

        if (['call', 'ai_call'].includes(provider.channel)) {
            // Handle voices as a map/object from Firestore
            const voicesSettings = conversationSettings?.voices || {};

            // Convert voices map to array if needed
            let voicesArray: any[] = [];

            // The voices object is directly in conversationSettings.voices, not in a nested "voices" property
            if (voicesSettings) {
                // If it's already an array, use it directly
                if (Array.isArray(voicesSettings)) {
                    voicesArray = voicesSettings;
                }
                // If it's an object/map (which is the case based on your data), convert it to array
                else if (typeof voicesSettings === 'object' && voicesSettings !== null) {
                    voicesArray = Object.values(voicesSettings);
                }
            }

            logger.info(`Found ${voicesArray.length} voices in settings`);
            logger.info(`Voice settings structure: ${JSON.stringify(voicesSettings, null, 2)}`);

            // Find voice by ID if provided
            if (provider.voice) {
                vapiVoiceSettings = voicesArray.find((voice: any) => voice?.uid === provider.voice);

                if (!vapiVoiceSettings) {
                    logger.warn(`Voice with ID ${provider.voice} not found, will try finding by time`);
                }
            }

            // If no voice specified or not found, find by time
            if (!vapiVoiceSettings) {
                const now = new Date();
                const currentTime = now.toTimeString().slice(0, 8); // "hh:mm:ss"

                vapiVoiceSettings = voicesArray.find((voice: any) => {
                    return voice?.start_time && voice?.end_time && currentTime >= voice.start_time && currentTime <= voice.end_time;
                });

                // If still no voice found, use the first available voice or create a default
                if (!vapiVoiceSettings && voicesArray.length > 0) {
                    vapiVoiceSettings = voicesArray[0];
                    logger.info('No voice found for current time, using first available voice');
                } else if (!vapiVoiceSettings) {
                    // Create a default voice settings object if no voices are available
                    vapiVoiceSettings = {
                        uid: 'default',
                        name: 'Default Voice',
                        assistantId: '061d84b2-b2ec-46bf-9015-f6a8289cd001',
                        start_time: '08:00:00',
                        end_time: '17:00:00'
                    };
                    logger.warn('No voices configured, using default voice settings');
                }
            }

            if (!vapiVoiceSettings) {
                throw new Error('Voice not found');
            }
        }

        if (leadData.phone) await resetFollowUpDatesForCalls(leadData.phone);
        if (leadData.email) await resetFollowUpDatesForCalls(null, leadData.email);

        if (provider.channel) {
            switch (provider.channel) {
                case 'sms':
                case 'whatsapp': {
                    let defaultNumberForWhatsapp: any;
                    if (!provider?.source) {
                        defaultNumberForWhatsapp = provider.channel === 'whatsapp' ? conversationSettings.whatsapp.defaultNumber : conversationSettings.sms.defaultNumber;
                    } else {
                        defaultNumberForWhatsapp = provider?.source;
                    }

                    const whatsappTemplateId = conversationSettings.whatsapp.templateId;
                    const twilio = invokeTwilio();
                    const maxMessageLength = 1600;
                    const fromWhatsapp = provider.channel === 'whatsapp' ? `whatsapp:${defaultNumberForWhatsapp}` : defaultNumberForWhatsapp;
                    if (leadData?.lead_details?.From) {
                        leadData.phone = leadData.lead_details.From.replace('whatsapp:', '');
                    }
                    if (!leadData.phone) {
                        throw new Error('No phone number found for lead');
                    }

                    let toWhatsapp = provider.channel === 'whatsapp' ? `whatsapp:${leadData.phone}` : leadData.phone;
                    if (provider.channel === 'sms') {
                        toWhatsapp = formatUKPhone(leadData.phone);
                    }
                    const sendParams = {
                        from: fromWhatsapp,
                        to: toWhatsapp
                    };
                    logger.info('Sending followup message', {
                        ...sendParams,
                        isWhatsappTemplate: provider?.isWhatsappTemplate
                    });
                    const name = leadData?.name || 'there';
                    if (message.length > maxMessageLength) {
                        const chunks = message.match(new RegExp(`.{1,${maxMessageLength}}`, 'g'));

                        if (chunks) {
                            for (const chunk of chunks) {
                                if (provider?.isWhatsappTemplate as boolean) {
                                    await twilio.messages.create({
                                        ...sendParams,
                                        contentSid: whatsappTemplateId,
                                        contentVariables: JSON.stringify({
                                            name: name
                                        }),
                                        forceDelivery: true
                                    });
                                } else {
                                    await twilio.messages.create({
                                        ...sendParams,
                                        body: chunk
                                    });
                                }
                            }
                        }
                    } else {
                        if (provider?.isWhatsappTemplate as boolean) {
                            await twilio.messages.create({
                                ...sendParams,
                                contentSid: whatsappTemplateId,
                                contentVariables: JSON.stringify({ name: name }),
                                forceDelivery: true
                            });
                        } else {
                            await twilio.messages.create({
                                ...sendParams,
                                body: message
                            });
                        }
                    }
                    break;
                }
                case 'call':
                case 'ai_call': {
                    let phoneNumber: any;
                    if (!provider?.source) {
                        phoneNumber = conversationSettings?.ai_call?.defaultNumber;
                    } else {
                        phoneNumber = provider?.source;
                    }

                    await makeVapiCall(
                        conversationSettings,
                        vapiVoiceSettings,
                        leadData.phone || leadData.lead_details.call.customer.number,
                        phoneNumber,
                        {
                            customer_name: leadData?.name || '',
                            customer_email: leadData?.email || '',
                            address: leadData?.address || '',
                            conversations: leadData?.conversations || [],
                            tenantId: leadData?.tenantId || ''
                        },
                        leadData
                    );
                    break;
                }
                case 'facebook':
                    await sendMessengerMessage(leadData.pageId, leadData.source_id, message);
                    break;
                case 'telegram':
                    await sendTelegramMessage(leadData.source_id, message);
                    break;
                case 'email':
                case 'gravity_form':
                    {
                        // Use SendGrid to send the response email
                        const msg = {
                            to: leadData.email, // Recipient's email address
                            from: '<EMAIL>', // Dynamically set the sender email
                            subject: `Re: Your Liftt Electric Garage Door Inquiry  – A Quick Follow-Up`, // Email subject
                            html: await getEmailTemplate(message.replace(/```json\s*|\s*```/g, ''), leadData.tenantId)
                        };
                        sgMail.setApiKey(sendGridApiKey.value());
                        await sgMail.send(msg);
                    }
                    break;
            }
            logger.info('Followup Message', message);
        } else {
            logger.error('No provider found');

            throw new HttpsError('internal', 'Failed sending of followup to user.');
        }
    } catch (error) {
        logger.error('Error handling sending of followup to user:', error);

        throw new HttpsError('internal', 'Failed sending of followup to user.');
    }
}

const makeVapiCall = async (conversationSettings: any, voiceSettings: any, phoneNumberToCall: string, twilioPhoneNumber: string, metadata: Record<string, any> = {}, leadData: any) => {
    try {
        // Data
        // const callScript = getCallScript(metadata.script, voiceSettings.assistantId, leadData, conversationSettings);
        const script = metadata.script || leadData.lead_actions || 'inquired';
        const pathwayId = script === 'inquired' ? voiceSettings?.inquired_pathway : voiceSettings?.quoted_pathway;
        const params: BlandCallParams = {
            from: twilioPhoneNumber,
            model: 'base',
            voice: voiceSettings.assistantId,
            record: true,
            language: 'en-GB',
            timezone: 'Europe/London',
            temperature: 0.7,
            max_duration: 12,
            phone_number: phoneNumberToCall,
            pathway_id: pathwayId,
            // first_sentence: "Hi, I'm " + voiceSettings.assistantId + " from Lift. Just checking in to see if you'd like to chat about our electric garage doors. Are you free for a quick call?",
            background_track: 'office',
            wait_for_greeting: true,
            noise_cancellation: true,
            block_interruptions: false,
            ignore_button_press: false,
            pronunciation_guide: [
                {
                    word: 'Liftt',
                    pronunciation: 'lift'
                }
            ],
            request_data: metadata,
            interruption_threshold: 170,
            // tools: voiceSettings.kb,
            webhook: voiceSettings.webhook
            // task: callScript
        };
        logger.info('AI call params', params);

        const headers = {
            encrypted_key: voiceSettings.encrypted_key,
            authorization: vapiAIApiKey.value(),
            'Content-Type': 'application/json'
        };
        const options = {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(params)
        };

        try {
            const response = await fetch('https://api.bland.ai/v1/calls', options);
            const data = await response.json();
            logger.info(`Call started: ${JSON.stringify(data)}`);
            return data;
        } catch (err) {
            throw err;
        }
    } catch (error: any) {
        logger.error('Error starting call:', error.response?.errors || error.message);
        throw error;
    }
};

function formatUKPhone(phone: any) {
    const ukPhoneRegex = /^(\+44\s?7\d{3}|\(?07\d{3}\)?)\s?\d{3}\s?\d{3}$/;
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '').trim();

    // Check if already starts with +44
    if (phone.startsWith('+44')) {
        return ukPhoneRegex.test(phone) ? phone : null;
    }

    // If starts with 07 and is valid length
    if ((digits.startsWith('07') && digits.length === 11) || (digits.startsWith('44') && digits.length === 12)) {
        let formatted;
        if (digits.startsWith('07')) {
            formatted = '+44' + digits.slice(1);
        } else if (digits.startsWith('44')) {
            formatted = '+' + digits;
        }
        return ukPhoneRegex.test(formatted as string) ? formatted : null;
    }

    // Not a valid UK mobile number
    throw new Error('Invalid phone number');
}

function checkIfWhatsappTemplate(leadData: any) {
    // Get all valid lastMessageTime values (filter out undefined/null)
    let isWhatsappTemplate = false;
    const messageTimestamps = leadData.conversations
        .filter((conv: any) => conv?.role === 'user')
        .filter((conv: any) => conv?.lastMessageTime)
        .map((conv: any) => conv.lastMessageTime as number);

    // Check if we have any valid timestamps
    if (messageTimestamps.length > 0) {
        // Find the most recent message timestamp
        const lastMessageTime = Math.max(...messageTimestamps);

        // Calculate time difference in milliseconds
        const timeDifference = Date.now() - lastMessageTime * 1000;

        // Check if more than 24 hours (86400000 ms) have passed
        isWhatsappTemplate = timeDifference > 24 * 60 * 60 * 1000;

        logger.info(`Last message time: ${new Date(lastMessageTime * 1000).toISOString()}`);
        logger.info(`Time difference: ${timeDifference / (60 * 60 * 1000)} hours`);
        logger.info(`Using WhatsApp template: ${isWhatsappTemplate}`);
    } else {
        // If no valid timestamps, default to using template
        isWhatsappTemplate = true;
        logger.info(`No valid message timestamps found, defaulting to WhatsApp template`);
    }
    return isWhatsappTemplate;
}
