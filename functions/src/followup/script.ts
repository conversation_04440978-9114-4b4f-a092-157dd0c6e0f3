import * as logger from 'firebase-functions/logger';

/*
 * Get call script from Firestore
 * @param {string} selectedScript The selected script
 * @param {string} selectedVoice The selected voice
 * @param {any} lead The lead data
 * @param {any} data The conversation settings
 * @returns {string} The call script
 */
export const getCallScript = (selectedScript: 'inquired' | 'booked' | 'quoted', selectedVoice: string, lead: any, data: any) => {
    try {
        const scriptString = data.scripts[selectedScript];
        if (!scriptString) {
            throw new Error('Script could not be found!');
        }

        const srciptParams = buildCallScriptJSONParams(selectedScript, lead, data);
        const markedConversations = srciptParams?.parsed_conversations?.map((convo: any) => ({
            role: convo.role,
            message: convo.message.replace(/<[^>]*>/g, '').replace(/[_*~`>#-]/g, '')
        }));

        const customer_name = lead.name ?? 'there';
        const agent_name = selectedVoice ?? 'Estella';
        const jsonContent = JSON.stringify(markedConversations || {}, null, 2);

        const jsonMarkdown: string = `
\`\`\`
\n
\n
${jsonContent}
\n
\`\`\``;

        const script = scriptString
            .replace(/\[customer_name]/gi, customer_name)
            .replace(/\[agent_name]/gi, agent_name)
            .replace(/\[summary_of_parsed_conversations]/gi, jsonMarkdown)
            .replace(/\[filtered_appointment_dates]/gi, srciptParams?.filtered_appointment_dates ?? '')
            .replace(/\[quote_details_doorType]/gi, srciptParams?.quote_details?.door_size ?? '')
            .replace(/\[quote_details_colour\]/gi, srciptParams?.quote_details?.[`${srciptParams?.quote_details?.door_size?.toLowerCase?.()}_door_colour`] ?? '')
            .replace(/\[quote_details_amount]/gi, srciptParams?.quote_details?.total_calc ?? '')
            .replace(/\[booking_details_date]/gi, srciptParams?.booking_details_date ?? '')
            .replace(/\[booking_details_address]/gi, srciptParams?.booking_details_address ?? '');

        return script;
    } catch (err) {
        logger.error('Failed to build call script', err);
        return null;
    }
};

/*
 * Build call script JSON params
 * @param {string} selectedScript The selected script
 * @param {any} lead The lead data
 * @param {any} data The conversation settings
 * @returns {any} The call script JSON params
 */
export const buildCallScriptJSONParams = (selectedScript: 'inquired' | 'booked' | 'quoted', lead: any, data: any) => {
    try {
        const groupedDates = data?.call?.availableAppointmentDates?.reduce((acc: any, dateStr: string) => {
            const date = new Date(dateStr as string);
            const monthKey = date.toLocaleString('en-GB', { month: 'long', year: 'numeric' });
            if (!acc[monthKey]) acc[monthKey] = [];
            acc[monthKey].push(date.getDate());
            return acc;
        }, {});

        const formattedDates = Object.entries(groupedDates)
            .map(([month, days]) => {
                // Explicitly type days as number[]
                const sortedDays = [...new Set(days as number[])].sort((a, b) => a - b);
                let range = [],
                    i = 0;
                while (i < sortedDays.length) {
                    let start = sortedDays[i];
                    while (sortedDays[i + 1] === sortedDays[i] + 1) i++;
                    range.push(start === sortedDays[i] ? start : `${start}–${sortedDays[i]}`);
                    i++;
                }
                return `- **${month}**: ${range.join(', ')}`;
            })
            .join('\n');

        const customer_details = {
            customer_name: lead.name ?? '',
            phone: lead.phone ?? '',
            email: lead.email ?? '',
            address: lead.address ?? ''
        };

        switch (selectedScript) {
            case 'inquired':
                return {
                    lead_details: customer_details,
                    parsed_conversations: lead.conversations ?? [],
                    filtered_appointment_dates: formattedDates ?? []
                };
            case 'booked':
                const currentTime = Math.floor(Date.now() / 1000);
                const getLatestAppointment = Array.isArray(lead?.appointmentHistory)
                    ? (lead?.appointmentHistory?.filter((app: any) => app.appointmentDate >= currentTime).sort((a: any, b: any) => a.appointmentDate - b.appointmentDate)[0] ?? null)
                    : null;
                const formattedDateString = getLatestAppointment?.appointmentDate
                    ? new Date(getLatestAppointment?.appointmentDate * 1000)
                          .toLocaleString('en-US', {
                              weekday: 'long',
                              month: 'long',
                              day: 'numeric',
                              hour: 'numeric',
                              minute: '2-digit',
                              hour12: true
                          })
                          .replace(/:\d{2}\s/, ' ') // Remove minutes if you want just the hour and AM/PM
                          .replace(',', ' at')
                    : '';
                return {
                    lead_details: customer_details,
                    booking_details_date: formattedDateString ?? '',
                    booking_details_address: getLatestAppointment?.location_string ?? lead.address ?? '',
                    parsed_conversations: lead.conversations ?? [],
                    filtered_appointment_dates: formattedDates ?? []
                };
            case 'quoted':
            default:
                return {
                    lead_details: customer_details,
                    quote_details: lead.lead_details ?? {},
                    parsed_conversations: lead.conversations ?? [],
                    filtered_appointment_dates: formattedDates ?? []
                };
        }
    } catch (err) {
        logger.error('Failed to build call script json params', err);
        return null;
    }
};
