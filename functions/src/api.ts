import { onRequest } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import express from 'express';
import { getLeadsFromFirestore } from './endpoints';
import { processLeadAppointmentCalendar } from './appointments';
import axios from 'axios';
import { googleServiceAccountEmail, googleServiceAccountKey, gravityFormApiUrl, gravityFormConsumerKey, gravityFormConsumerSecret } from './utils';

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.get('/lead', async (req, res) => {
    try {
        const { searchColumn, searchTerm, leadAction } = req.query;
        if (!searchColumn || !searchTerm || !leadAction) {
            throw new Error('Missing required parameters');
        }
        // Decode URI components to handle special characters like +
        let decodedSearchTerm = decodeURIComponent(searchTerm as string).trim();
        const decodedLeadAction = decodeURIComponent(leadAction as string).trim();

        // Format phone number
        if (decodedSearchTerm.startsWith('0')) {
            decodedSearchTerm = decodedSearchTerm.replace(/^0/, '+44');
        } else if (decodedSearchTerm.startsWith('44')) {
            decodedSearchTerm = decodedSearchTerm.replace(/^44/, '+44');
        }

        logger.info('Search Term: ', decodedSearchTerm);
        logger.info('Lead Action: ', decodedLeadAction);

        const leads = await getLeadsFromFirestore({
            searchTerm: decodedSearchTerm,
            lead_actions: [decodedLeadAction]
        });
        // if no leads found, return empty array
        if (leads.length === 0) {
            throw new Error('No leads found');
        }

        // get the first lead
        const lead = leads[0] as any;
        const customer_details = {
            customer_name: lead.name ?? '',
            phone: lead.phone ?? '',
            email: lead.email ?? '',
            address: lead.address ?? ''
        };

        // build the lead details
        let lead_details = {};
        switch (leadAction) {
            case 'inquired':
                lead_details = {
                    ...customer_details
                };
                break;
            case 'quoted':
            default:
                lead_details = {
                    ...customer_details,
                    price: lead?.lead_details?.total_calc ? '£' + lead.lead_details.total_calc : '£0',
                    vat: lead?.lead_details?.vat ? '£' + lead.lead_details.vat : '£0',
                    subtotal: lead?.lead_details?.vat ? '£' + lead.lead_details.vat : '£0',
                    retail_price: lead?.lead_details?.retail_price ? '£' + lead.lead_details.retail_price : '£0',
                    door_size: lead?.lead_details?.door_size || '',
                    door_colour: lead?.lead_details?.door_size ? lead.lead_details[`${lead.lead_details.door_size.toLowerCase()}_door_colour`]?.split('|')[0] || '' : ''
                };
        }
        logger.info('Lead Details:  ', lead_details);
        return res.status(200).send(lead_details);
    } catch (error) {
        logger.error('Error in get lead:', error);
        return res.status(500).send('Error in get lead');
    }
});

app.get('/appointment/available', async (req, res) => {
    try {
        const aiFollowUpDoc = admin.firestore().collection('settings').doc('conversations');
        const followUpSnapshot = await aiFollowUpDoc.get();

        const availableDates = followUpSnapshot.data()!.call?.availableAppointmentDates ?? [];

        // Format the dates
        const formattedDates = availableDates.map((date: string) => {
            const dateObj = new Date(date);
            return dateObj.toLocaleString('en-GB', {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            });
        });
        // Join the dates with a comma
        const formattedDatesString = formattedDates.join(', ');
        logger.info('Available dates are:', formattedDatesString);

        return res.status(200).send({ available_appointment_dates: formattedDatesString ?? '' });
    } catch (error) {
        logger.error('Error in get available appointment dates:', error);
        return res.status(500).send('Error in get available appointment dates');
    }
});

app.post('/appointment/book', async (req, res) => {
    try {
        let bookingParams = req.body;
        let { appointmentDate, name, email, phone, notes } = bookingParams;
        if (typeof appointmentDate === 'string') {
            appointmentDate = Math.floor(new Date(appointmentDate).getTime() / 1000);
        }
        bookingParams = {
            ...bookingParams,
            appointmentDate,
            notes: [notes],
            lead: {
                name,
                email,
                phone
            }
        };
        logger.info('Book appointment called:', bookingParams);
        await processLeadAppointmentCalendar(bookingParams);
        return res.status(200).send('Book appointment called successfully.');
    } catch (error) {
        logger.error('Error in in book appointment:', error);
        return res.status(500).send('Error in book appointment');
    }
});

app.post('/warranty/send', async (req, res) => {
    try {
        const searchParams = req.body;
        logger.info('Send Warranty Email Called:', searchParams);
        const { email } = searchParams;
        if (!email) {
            throw new Error('Missing email');
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            throw new Error('Invalid email');
        }

        const token = Buffer.from(`${gravityFormConsumerKey.value()}:${gravityFormConsumerSecret.value()}`).toString('base64');

        const getFormEntryByEmailAddress = async (email: string) => {
            const axiosResponse = await axios.get(`${gravityFormApiUrl}/forms/3/entries`, {
                headers: {
                    Authorization: `Basic ${token}`,
                    'Content-Type': 'application/json'
                },
                params: {
                    search: JSON.stringify({
                        field_filters: [{ key: 15, value: email }]
                    })
                }
            });
            logger.info('Gravity Form Search Entry Response:', axiosResponse.data);
            if (axiosResponse.status !== 200) {
                throw new Error('No form entry found for email');
            }
            if (!axiosResponse.data) {
                throw new Error('No form entry found for email');
            } else {
                if (axiosResponse.data.total_count === 0) {
                    throw new Error('No form entry found for email');
                }
                return axiosResponse.data.entries[0].id;
            }
        };
        const formEntryId = await getFormEntryByEmailAddress(email);
        logger.info('Form Entry ID:', formEntryId);

        const resendWarrantyEmail = async (entryId: string) => {
            const axiosResponse = await axios.post(
                `${gravityFormApiUrl}/entries/${entryId}/notifications`,
                {
                    _notifications: '66cb2ef6e90af'
                },
                {
                    headers: {
                        Authorization: `Basic ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            logger.info('Gravity Form Resend Warranty Email Response:', axiosResponse.data);
            if (axiosResponse.status !== 200) {
                throw new Error('Error resending warranty email');
            }
            if (!axiosResponse.data?.length) {
                throw new Error('Error resending warranty email');
            }
        };

        await resendWarrantyEmail(formEntryId);
        return res.status(200).send('Successfully sent warranty email');
    } catch (error) {
        logger.error('Error in send warranty email:', error);
        return res.status(500).send('Error in send warranty email');
    }
});

// Export the endpoints handler
export const api = onRequest(
    {
        cors: true,
        secrets: [gravityFormConsumerKey, gravityFormConsumerSecret, googleServiceAccountEmail, googleServiceAccountKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    app
);
