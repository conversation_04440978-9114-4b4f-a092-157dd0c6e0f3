import * as logger from 'firebase-functions/logger';
import { twilioAccounSID, twilioAuthTpken, sendGridA<PERSON><PERSON><PERSON>, requireAuth, invokeTwilio } from './utils';
import { HttpsError, onCall } from 'firebase-functions/v2/https';

// Helper function to extract country code from phone number
function extractCountryFromPhoneNumber(phoneNumber: string): string | null {
    // Remove any non-digit characters except +
    const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');

    // Common country code mappings
    const countryMappings: { [key: string]: string } = {
        '+1': 'US',     // US/Canada
        '+44': 'GB',    // United Kingdom
        '+33': 'FR',    // France
        '+49': 'DE',    // Germany
        '+39': 'IT',    // Italy
        '+34': 'ES',    // Spain
        '+31': 'NL',    // Netherlands
        '+32': 'BE',    // Belgium
        '+41': 'CH',    // Switzerland
        '+43': 'AT',    // Austria
        '+45': 'DK',    // Denmark
        '+46': 'SE',    // Sweden
        '+47': 'NO',    // Norway
        '+358': 'FI',   // Finland
        '+353': 'IE',   // Ireland
        '+351': 'PT',   // Portugal
        '+30': 'GR',    // Greece
        '+48': 'PL',    // Poland
        '+420': 'CZ',   // Czech Republic
        '+36': 'HU',    // Hungary
        '+61': 'AU',    // Australia
        '+64': 'NZ',    // New Zealand
        '+81': 'JP',    // Japan
        '+82': 'KR',    // South Korea
        '+86': 'CN',    // China
        '+91': 'IN',    // India
        '+55': 'BR',    // Brazil
        '+52': 'MX',    // Mexico
        '+54': 'AR',    // Argentina
        '+56': 'CL',    // Chile
        '+57': 'CO',    // Colombia
        '+58': 'VE',    // Venezuela
        '+51': 'PE',    // Peru
        '+27': 'ZA',    // South Africa
        '+234': 'NG',   // Nigeria
        '+254': 'KE',   // Kenya
        '+20': 'EG',    // Egypt
        '+971': 'AE',   // UAE
        '+966': 'SA',   // Saudi Arabia
        '+65': 'SG',    // Singapore
        '+60': 'MY',    // Malaysia
        '+66': 'TH',    // Thailand
        '+84': 'VN',    // Vietnam
        '+63': 'PH',    // Philippines
        '+62': 'ID',    // Indonesia
    };

    // Try to match country codes (longest first for specificity)
    const sortedCodes = Object.keys(countryMappings).sort((a, b) => b.length - a.length);

    for (const code of sortedCodes) {
        if (cleanNumber.startsWith(code)) {
            return countryMappings[code];
        }
    }

    return null;
}

// Helper function to get supported Twilio countries
function getSupportedTwilioCountries(): string[] {
    return [
        'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AO', 'AR', 'AS', 'AT', 'AU', 'AW', 'AZ', 'BA', 'BB', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BM', 'BN', 'BO', 'BR', 'BS', 'BT', 'BW', 'BY', 'BZ', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM', 'CN', 'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ', 'DK', 'DM', 'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK', 'FM', 'FO', 'FR', 'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN', 'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY', 'HK', 'HM', 'HN', 'HR', 'HT', 'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM', 'JO', 'JP', 'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY', 'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY', 'MA', 'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ', 'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ', 'NA', 'NC', 'NE', 'NF', 'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG', 'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY', 'QA', 'RE', 'RO', 'RS', 'RU', 'RW', 'SA', 'SB', 'SC', 'SD', 'SE', 'SG', 'SH', 'SI', 'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX', 'SY', 'SZ', 'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO', 'TR', 'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ', 'VA', 'VC', 'VE', 'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'YE', 'YT', 'ZA', 'ZM', 'ZW'
    ];
}

// Interface for search parameters
interface SearchNumberParams {
    areaCode?: string;
    contains?: string;
    smsEnabled?: boolean;
    voiceEnabled?: boolean;
    mmsEnabled?: boolean;
    faxEnabled?: boolean;
    excludeAllAddressRequired?: boolean;
    excludeLocalAddressRequired?: boolean;
    excludeForeignAddressRequired?: boolean;
    beta?: boolean;
    nearNumber?: string;
    nearLatLong?: string;
    distance?: number;
    inPostalCode?: string;
    inRegion?: string;
    inRateCenter?: string;
    inLata?: string;
    inLocality?: string;
    limit?: number;
}

// Interface for buy number parameters
interface BuyNumberParams {
    phoneNumber: string;
    areaCode?: string;
    friendlyName?: string;
    voiceUrl?: string;
    voiceMethod?: string;
    voiceFallbackUrl?: string;
    voiceFallbackMethod?: string;
    statusCallback?: string;
    statusCallbackMethod?: string;
    voiceCallerIdLookup?: boolean;
    voiceApplicationSid?: string;
    smsUrl?: string;
    smsMethod?: string;
    smsFallbackUrl?: string;
    smsFallbackMethod?: string;
    smsApplicationSid?: string;
    accountSid?: string;
    apiVersion?: string;
    bundleSid?: string;
    identitySid?: string;
    addressSid?: string;
    emergencyStatus?: string;
    emergencyAddressSid?: string;
    trunkSid?: string;
}

// Search for available phone numbers
export const onSearchTwilioNumbers = onCall(
    {
        cors: true,
        secrets: [twilioAuthTpken, twilioAccounSID, sendGridApiKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;

        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);

        try {
            const searchParams: SearchNumberParams = data.searchParams || {};
            const countryCode = data.countryCode || 'GB'; // Default to GB (En-GB)

            // Handle if the request param is a phone number (extract country from number)
            let finalCountryCode = countryCode;
            if (data.phoneNumber && typeof data.phoneNumber === 'string') {
                // Extract country code from phone number if provided
                const extractedCountry = extractCountryFromPhoneNumber(data.phoneNumber);
                if (extractedCountry) {
                    finalCountryCode = extractedCountry;
                    logger.info(`Extracted country code ${extractedCountry} from phone number ${data.phoneNumber}`);
                }

                // If searching by phone number, add it to search params
                if (!searchParams.contains && !searchParams.nearNumber) {
                    // Extract the last 4-7 digits for contains search
                    const digits = data.phoneNumber.replace(/[^\d]/g, '');
                    if (digits.length >= 4) {
                        searchParams.contains = digits.slice(-4);
                        logger.info(`Searching for numbers containing: ${searchParams.contains}`);
                    }
                }
            }

            // Validate country code against supported countries
            const supportedCountries = getSupportedTwilioCountries();
            if (!finalCountryCode || typeof finalCountryCode !== 'string' || !supportedCountries.includes(finalCountryCode.toUpperCase())) {
                throw new Error(`Invalid or unsupported country code: ${finalCountryCode}. Supported countries: ${supportedCountries.join(', ')}`);
            }

            finalCountryCode = finalCountryCode.toUpperCase();

            const twilio = invokeTwilio();

            // Build search parameters
            const twilioSearchParams: any = {
                limit: searchParams.limit || 20
            };

            // Add optional search parameters
            if (searchParams.areaCode) twilioSearchParams.areaCode = searchParams.areaCode;
            if (searchParams.contains) twilioSearchParams.contains = searchParams.contains;
            if (searchParams.smsEnabled !== undefined) twilioSearchParams.smsEnabled = searchParams.smsEnabled;
            if (searchParams.voiceEnabled !== undefined) twilioSearchParams.voiceEnabled = searchParams.voiceEnabled;
            if (searchParams.mmsEnabled !== undefined) twilioSearchParams.mmsEnabled = searchParams.mmsEnabled;
            if (searchParams.faxEnabled !== undefined) twilioSearchParams.faxEnabled = searchParams.faxEnabled;
            if (searchParams.excludeAllAddressRequired !== undefined) twilioSearchParams.excludeAllAddressRequired = searchParams.excludeAllAddressRequired;
            if (searchParams.excludeLocalAddressRequired !== undefined) twilioSearchParams.excludeLocalAddressRequired = searchParams.excludeLocalAddressRequired;
            if (searchParams.excludeForeignAddressRequired !== undefined) twilioSearchParams.excludeForeignAddressRequired = searchParams.excludeForeignAddressRequired;
            if (searchParams.beta !== undefined) twilioSearchParams.beta = searchParams.beta;
            if (searchParams.nearNumber) twilioSearchParams.nearNumber = searchParams.nearNumber;
            if (searchParams.nearLatLong) twilioSearchParams.nearLatLong = searchParams.nearLatLong;
            if (searchParams.distance) twilioSearchParams.distance = searchParams.distance;
            if (searchParams.inPostalCode) twilioSearchParams.inPostalCode = searchParams.inPostalCode;
            if (searchParams.inRegion) twilioSearchParams.inRegion = searchParams.inRegion;
            if (searchParams.inRateCenter) twilioSearchParams.inRateCenter = searchParams.inRateCenter;
            if (searchParams.inLata) twilioSearchParams.inLata = searchParams.inLata;
            if (searchParams.inLocality) twilioSearchParams.inLocality = searchParams.inLocality;

            logger.info('Searching for available numbers with params:', twilioSearchParams);
            logger.info(`Searching in country: ${finalCountryCode}`);

            // Search for available phone numbers - try different number types
            let availableNumbers: any[] = [];
            const numberTypes = ['local', 'tollFree', 'mobile'];

            for (const numberType of numberTypes) {
                try {
                    let typeNumbers: any[] = [];

                    switch (numberType) {
                        case 'local':
                            typeNumbers = await twilio.availablePhoneNumbers(finalCountryCode).local.list(twilioSearchParams);
                            break;
                        case 'tollFree':
                            // Only search toll-free for supported countries
                            if (['US', 'CA', 'GB', 'AU'].includes(finalCountryCode)) {
                                typeNumbers = await twilio.availablePhoneNumbers(finalCountryCode).tollFree.list(twilioSearchParams);
                            }
                            break;
                        case 'mobile':
                            // Only search mobile for supported countries
                            if (['US', 'CA', 'GB', 'AU', 'SE', 'FI', 'NO', 'DK'].includes(finalCountryCode)) {
                                typeNumbers = await twilio.availablePhoneNumbers(finalCountryCode).mobile.list(twilioSearchParams);
                            }
                            break;
                    }

                    // Add number type to each result
                    const typedNumbers = typeNumbers.map(number => ({ ...number, numberType }));
                    availableNumbers = availableNumbers.concat(typedNumbers);

                    logger.info(`Found ${typeNumbers.length} ${numberType} numbers`);
                } catch (error) {
                    logger.warn(`Error searching ${numberType} numbers:`, error);
                    // Continue with other number types
                }
            }

            const formattedNumbers = availableNumbers.map(number => ({
                phoneNumber: number.phoneNumber,
                friendlyName: number.friendlyName,
                locality: number.locality,
                region: number.region,
                postalCode: number.postalCode,
                isoCountry: number.isoCountry,
                numberType: number.numberType || 'local',
                capabilities: {
                    voice: number.capabilities?.voice || false,
                    sms: number.capabilities?.sms || false,
                    mms: number.capabilities?.mms || false,
                    fax: number.capabilities?.fax || false
                },
                addressRequirements: number.addressRequirements,
                beta: number.beta
            }));

            logger.info(`Found ${formattedNumbers.length} total available numbers`);

            return {
                success: true,
                availableNumbers: formattedNumbers,
                searchParams: twilioSearchParams,
                countryCode: finalCountryCode,
                searchedCountries: [finalCountryCode],
                totalResults: formattedNumbers.length,
                resultsByType: {
                    local: formattedNumbers.filter(n => n.numberType === 'local').length,
                    tollFree: formattedNumbers.filter(n => n.numberType === 'tollFree').length,
                    mobile: formattedNumbers.filter(n => n.numberType === 'mobile').length
                }
            };

        } catch (error) {
            logger.error('Error searching for Twilio numbers:', error);

            if (error instanceof Error) {
                // Handle specific Twilio errors
                if (error.message.includes('Invalid country code')) {
                    throw new HttpsError('invalid-argument', 'Invalid country code provided');
                }
                if (error.message.includes('Invalid area code')) {
                    throw new HttpsError('invalid-argument', 'Invalid area code provided');
                }
                throw new HttpsError('internal', `Error searching for numbers: ${error.message}`);
            }

            throw new HttpsError('internal', 'Error searching for Twilio numbers');
        }
    }
);

// Buy a Twilio phone number
export const onBuyTwilioNumber = onCall(
    {
        cors: true,
        secrets: [twilioAuthTpken, twilioAccounSID, sendGridApiKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;

        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);

        try {
            const buyParams: BuyNumberParams = data.buyParams || {};

            // Support both buyParams.phoneNumber and direct data.phoneNumber
            const phoneNumber = buyParams.phoneNumber || data.phoneNumber;

            // Validate required parameters
            if (!phoneNumber) {
                throw new Error('Phone number is required');
            }

            // Validate phone number format (basic validation)
            const phoneNumberRegex = /^\+[1-9]\d{1,14}$/;
            if (!phoneNumberRegex.test(phoneNumber)) {
                throw new Error('Invalid phone number format. Must be in E.164 format (e.g., +1234567890)');
            }

            // Extract country from phone number for validation
            const extractedCountry = extractCountryFromPhoneNumber(phoneNumber);
            const requestCountry = data.countryCode || 'GB'; // Default to GB

            if (extractedCountry && extractedCountry !== requestCountry) {
                logger.info(`Phone number country (${extractedCountry}) differs from requested country (${requestCountry}). Using extracted country.`);
            }

            // Validate against supported countries
            const finalCountry = extractedCountry || requestCountry;
            const supportedCountries = getSupportedTwilioCountries();
            if (!supportedCountries.includes(finalCountry.toUpperCase())) {
                throw new Error(`Unsupported country code: ${finalCountry}. Supported countries: ${supportedCountries.join(', ')}`);
            }

            const twilio = invokeTwilio();

            // Build purchase parameters
            const purchaseParams: any = {
                phoneNumber: phoneNumber
            };

            // Add optional parameters
            if (buyParams.areaCode) purchaseParams.areaCode = buyParams.areaCode;
            if (buyParams.friendlyName) purchaseParams.friendlyName = buyParams.friendlyName;
            if (buyParams.voiceUrl) purchaseParams.voiceUrl = buyParams.voiceUrl;
            if (buyParams.voiceMethod) purchaseParams.voiceMethod = buyParams.voiceMethod;
            if (buyParams.voiceFallbackUrl) purchaseParams.voiceFallbackUrl = buyParams.voiceFallbackUrl;
            if (buyParams.voiceFallbackMethod) purchaseParams.voiceFallbackMethod = buyParams.voiceFallbackMethod;
            if (buyParams.statusCallback) purchaseParams.statusCallback = buyParams.statusCallback;
            if (buyParams.statusCallbackMethod) purchaseParams.statusCallbackMethod = buyParams.statusCallbackMethod;
            if (buyParams.voiceCallerIdLookup !== undefined) purchaseParams.voiceCallerIdLookup = buyParams.voiceCallerIdLookup;
            if (buyParams.voiceApplicationSid) purchaseParams.voiceApplicationSid = buyParams.voiceApplicationSid;
            if (buyParams.smsUrl) purchaseParams.smsUrl = buyParams.smsUrl;
            if (buyParams.smsMethod) purchaseParams.smsMethod = buyParams.smsMethod;
            if (buyParams.smsFallbackUrl) purchaseParams.smsFallbackUrl = buyParams.smsFallbackUrl;
            if (buyParams.smsFallbackMethod) purchaseParams.smsFallbackMethod = buyParams.smsFallbackMethod;
            if (buyParams.smsApplicationSid) purchaseParams.smsApplicationSid = buyParams.smsApplicationSid;
            if (buyParams.accountSid) purchaseParams.accountSid = buyParams.accountSid;
            if (buyParams.apiVersion) purchaseParams.apiVersion = buyParams.apiVersion;
            if (buyParams.bundleSid) purchaseParams.bundleSid = buyParams.bundleSid;
            if (buyParams.identitySid) purchaseParams.identitySid = buyParams.identitySid;
            if (buyParams.addressSid) purchaseParams.addressSid = buyParams.addressSid;
            if (buyParams.emergencyStatus) purchaseParams.emergencyStatus = buyParams.emergencyStatus;
            if (buyParams.emergencyAddressSid) purchaseParams.emergencyAddressSid = buyParams.emergencyAddressSid;
            if (buyParams.trunkSid) purchaseParams.trunkSid = buyParams.trunkSid;

            logger.info('Attempting to purchase phone number:', phoneNumber);
            logger.info('Detected country:', finalCountry);

            // Purchase the phone number
            const purchasedNumber = await twilio.incomingPhoneNumbers.create(purchaseParams);

            logger.info('Successfully purchased phone number:', {
                sid: purchasedNumber.sid,
                phoneNumber: purchasedNumber.phoneNumber,
                friendlyName: purchasedNumber.friendlyName
            });

            return {
                success: true,
                countryCode: finalCountry,
                extractedCountry: extractedCountry,
                purchasedNumber: {
                    sid: purchasedNumber.sid,
                    phoneNumber: purchasedNumber.phoneNumber,
                    friendlyName: purchasedNumber.friendlyName,
                    accountSid: purchasedNumber.accountSid,
                    capabilities: purchasedNumber.capabilities,
                    voiceUrl: purchasedNumber.voiceUrl,
                    voiceMethod: purchasedNumber.voiceMethod,
                    smsUrl: purchasedNumber.smsUrl,
                    smsMethod: purchasedNumber.smsMethod,
                    statusCallback: purchasedNumber.statusCallback,
                    statusCallbackMethod: purchasedNumber.statusCallbackMethod,
                    dateCreated: purchasedNumber.dateCreated,
                    dateUpdated: purchasedNumber.dateUpdated
                }
            };

        } catch (error) {
            logger.error('Error purchasing Twilio number:', error);

            if (error instanceof Error) {
                // Handle specific Twilio errors
                if (error.message.includes('not available')) {
                    throw new HttpsError('failed-precondition', 'The requested phone number is not available for purchase');
                }
                if (error.message.includes('insufficient funds')) {
                    throw new HttpsError('failed-precondition', 'Insufficient funds in Twilio account to purchase number');
                }
                if (error.message.includes('Invalid phone number')) {
                    throw new HttpsError('invalid-argument', 'Invalid phone number format');
                }
                if (error.message.includes('Address required')) {
                    throw new HttpsError('failed-precondition', 'Address verification required for this number');
                }
                throw new HttpsError('internal', `Error purchasing number: ${error.message}`);
            }

            throw new HttpsError('internal', 'Error purchasing Twilio number');
        }
    }
);
