import { calendar, getCalendarClient, invokeOpenAI, lifttCalendarId, tokenKey } from '../utils';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import CryptoJS from 'crypto-js';

export async function renewGoogleCalendateEventWebhook() {
    try {
        const tenantCollection = admin.firestore().collection('companies');
        const tenantSnapshot = await tenantCollection.where('calendar.validated', '==', true).get();
        tenantSnapshot.docs.forEach(async (doc) => {
            const calendarData = doc.data()!.calendar;
            await renewCalendarWebhook(doc.ref.id, calendarData.calendarId);
            logger.info(`Google Calendar event webhook renewed: for tenant ${doc.id}`);
        });
    } catch (error) {
        logger.error('Error renewing Google Calendar event webhook:', error);
        throw new Error('Failed to renew Google Calendar event webhook');
    }
}

const renewCalendarWebhook = async (tenantId: string, tenantCalendarId?: string) => {
    try {
        const auth = await getCalendarClient();

        const params = {
            id: tenantId ? `liftt-crm-appointment-listener-${tenantId}-${Date.now()}` : `liftt-crm-appointment-listener-${Date.now()}`,
            token: tenantId ?? tokenKey,
            params: {
                ttl: '2592000'
            },
            type: 'web_hook',
            address: 'https://us-central1-wyspre-ai.cloudfunctions.net/chatBots/listen-to-appointments'
        };
        await calendar.events.watch({
            auth: auth,
            calendarId: tenantCalendarId ?? lifttCalendarId,
            requestBody: {
                ...params
            }
        });

        logger.info(`Google Calendar event webhook renewed: for tenant calendar ${tenantCalendarId}`);
    } catch (error) {
        logger.error('Error renewing Google Calendar event webhook:', error);
    }
};

export async function createGoogleCalendar(calendarName: string, tenantId: string, timezone?: string) {
    try {
        const auth = await getCalendarClient();

        const calendarResource = {
            summary: calendarName + ' - ' + tenantId || 'New Calendar',
            timeZone: timezone || 'Europe/London'
        };
        const response = await calendar.calendars.insert({
            auth: auth,
            requestBody: {
                ...calendarResource
            }
        });

        logger.info('Calendar created successfully!');
        logger.info('Calendar ID:', response.data.id);

        if (response.data.id) {
            const tenantCollection = admin.firestore().collection('companies');
            const tenantDocRef = tenantCollection.doc(tenantId);
            await tenantDocRef.set(
                {
                    calendar: {
                        token: tenantDocRef.id,
                        calendarId: response.data.id,
                        validated: true
                    }
                },
                { merge: true }
            );
            await renewCalendarWebhook(tenantDocRef.id, response.data.id);
        }
        return response.data.id;
    } catch (error) {
        logger.error('Error creating calendar:', error);
        throw new Error('Error creating calendar');
    }
}

export async function processLeadAppointmentCalendar(leadData: any) {
    try {
        const { lead, location_string, gps_coordinates, notes, doorType, doorSize, customSize, doorColor, customColor, tradeIn, google_link, eventId, action, has_alternative_garage_access, wants_external_override, has_electrical_connection } =
            leadData;

        // Build Description
        const descriptionLines = [];

        if (lead.phone) descriptionLines.push(`Phone: ${lead.phone}`);
        if (lead.email) descriptionLines.push(`Email: ${lead.email}`);
        if (notes?.length) descriptionLines.push(`Notes: ${notes.join(', ')}`);

        // Format product details
        const productDetails = [];
        if (doorType) productDetails.push(`Door Type: ${doorType}`);
        if (doorSize) productDetails.push(`Size: ${doorSize}`);
        if (customSize) productDetails.push(`Custom Size: ${customSize}`);
        if (doorColor) productDetails.push(`Color: ${doorColor}`);
        if (customColor) productDetails.push(`Custom Color: ${customColor}`);
        if (tradeIn) productDetails.push('Trade-In: Yes');
        if (has_electrical_connection) productDetails.push('Has electrical connection: Yes');
        if (has_alternative_garage_access) productDetails.push('Has alternative garage access: Yes');
        if (wants_external_override) productDetails.push('Wants external override: Yes');

        if (productDetails.length) descriptionLines.push(`Details:\n${productDetails.join('\n')}`);

        descriptionLines.push(`Address: ${location_string}`);
        if (google_link) descriptionLines.push(`Google Maps: ${google_link}`);
        if (gps_coordinates) descriptionLines.push(`GPS Coordinates: ${gps_coordinates}`);
        if (lead.id) descriptionLines.push(`\n\nUID: (${lead.id})`);
        const encrypted = CryptoJS.AES.encrypt(
            JSON.stringify({
                location_string: location_string || '',
                gps_coordinates: gps_coordinates || '',
                notes: notes,
                doorType: doorType,
                doorSize: doorSize,
                customSize: customSize,
                doorColor: doorColor,
                customColor: customColor,
                tradeIn: tradeIn,
                google_link: google_link,
                leadId: lead.id || '',
                tenantId: lead.tenantId || ''
            }),
            tokenKey
        ).toString();
        descriptionLines.push(`EncryptedID: (${encrypted})`);

        const description = descriptionLines.join('\n');

        // Get calendar ID from the URL
        // From: https://calendar.google.com/calendar/u/0?cid=*******************************************************************************************
        let calendarId = lifttCalendarId;
        if (lead.tenantId) {
            const tenantCollection = admin.firestore().collection('companies');
            const tenantSnapshot = await tenantCollection.doc(lead.tenantId).get();
            if (tenantSnapshot.data()?.calendar?.validated) {
                calendarId = tenantSnapshot.data()?.calendar?.calendarId;
            }
        }
        calendarId = decodeURIComponent(calendarId);

        // Format appointmentDate as ISO string
        const start = new Date(leadData.appointmentDate * 1000);
        const end = new Date(start.getTime() + 60 * 60 * 1000); // Add 1 hour

        const event = {
            summary: lead.name,
            location: location_string,
            description,
            start: {
                dateTime: start.toISOString(),
                timeZone: 'Europe/London' // Adjust to your timezone
            },
            end: {
                dateTime: end.toISOString(),
                timeZone: 'Europe/London'
            },
            source: {
                url: 'https://crm.liftt.co.uk',
                title: 'LifttCRM'
            },
            attendees: [
                {
                    ...(lead.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(lead.email) ? { email: lead.email } : {})
                },
                { email: '<EMAIL>' },
                { email: '<EMAIL>' },
                { email: '<EMAIL>' }
            ]
        };
        const auth = await getCalendarClient();
        // let responseEventId;
        // Add or cancel event
        if (action === 'add' || !action) {
            await calendar.events.insert({
                auth: auth,
                calendarId: calendarId,
                requestBody: event
            });
            // responseEventId = response.data.id;
        } else if (eventId) {
            // responseEventId = eventId;
            if (action === 'cancel') {
                await calendar.events.delete({
                    auth: auth,
                    calendarId: calendarId,
                    eventId: leadData.eventId,
                    sendUpdates: 'all'
                });
            } else if (action === 'update') {
                await calendar.events.update({
                    auth: auth,
                    calendarId: calendarId,
                    eventId: leadData.eventId,
                    requestBody: event,
                    sendUpdates: 'all'
                });
            }
        }

        // const { leadId, appointmentId } = await saveLeadAppointment(
        //   responseEventId,
        //   leadData,
        //   event,
        //   action,
        // );
        return {
            success: true,
            message: 'Appointment booked and Google Calendar event created successfully'
        };
    } catch (error) {
        logger.error('Error creating calendar event:', error);
        throw new Error('Failed to create calendar event');
    }
}

export async function processLeadCheckAvailableAppointmentSlots(data: any) {
    const { preferredAppointmentDate } = data;

    try {
        // Convert preferredAppointmentDate to start and end of day timestamps
        const preferredDate = new Date(preferredAppointmentDate * 1000);
        preferredDate.setHours(0, 0, 0, 0);
        const startOfDay = Math.floor(preferredDate.getTime() / 1000);

        preferredDate.setHours(23, 59, 59, 999);
        const endOfDay = Math.floor(preferredDate.getTime() / 1000);

        // Get appointments for the selected date
        const appointmentsCollection = admin.firestore().collection('appointments');
        const appointmentsSnapshot = await appointmentsCollection.where('appointmentDate', '>=', startOfDay).where('appointmentDate', '<=', endOfDay).get();

        const dateAppointments = appointmentsSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data()
        }));

        logger.info(`Found ${dateAppointments.length} appointments for date: ${new Date(preferredAppointmentDate * 1000).toISOString().split('T')[0]}`);

        // Calculate available time slots based on existing appointments and selected location
        const { validatedSlots, travelEstimates, bookedAppointments } = await calculateAvailableTimeSlots(preferredAppointmentDate, dateAppointments, data);

        // Format the response
        const response = {
            travelEstimates: travelEstimates,
            bookedAppointments: bookedAppointments,
            available: validatedSlots.length > 0,
            slots: validatedSlots,
            date: new Date(preferredAppointmentDate * 1000).toISOString().split('T')[0]
        };

        return response;
    } catch (error) {
        logger.error('Failed to process available slots:', error);
        return {
            available: false,
            slots: [],
            error: 'Failed to process available slots'
        };
    }
}

export async function getScheduleParams(companyId: string) {
    const aiFollowUpDoc = admin.firestore().collection('companies').doc(companyId);
    const aiFollowUpSnapshot = await aiFollowUpDoc.get();
    const { scheduler } = aiFollowUpSnapshot.data()!;
    return scheduler;
}

/**
 * Calculates available time slots based on existing appointments and their locations
 * @param preferredDate The preferred date in timestamp format
 * @param dateAppointments Array of appointments for a specific date
 * @param data The request data containing preferredAppointmentDate, selectedLocation, and selectedLocationLatLong
 * @returns Array of available time slots as timestamp numbers
 */
async function calculateAvailableTimeSlots(preferredDate: any, dateAppointments: any[], data: any) {
    const { selectedLocation, selectedLocationLatLong } = data;

    // Convert preferredDate to Date object
    const preferredDateObj = new Date(preferredDate * 1000);
    const year = preferredDateObj.getFullYear();
    const month = preferredDateObj.getMonth();
    const day = preferredDateObj.getDate();

    // Define business hours (9AM to 12PM)
    const businessStart = new Date(year, month, day, 9, 0, 0);
    const businessEnd = new Date(year, month, day, 12, 0, 0);

    // Format date as DD/MM/YYYY
    const formattedDate = `${day.toString().padStart(2, '0')}/${(month + 1).toString().padStart(2, '0')}/${year}`;

    // If no appointments, return all available slots with 30-minute intervals
    if (dateAppointments.length === 0) {
        const validatedSlots = generateTimeSlotsAsTimestamps(businessStart, businessEnd, 30);
        return { validatedSlots, travelEstimates: [], bookedAppointments: [] };
    }

    // For appointments, use OpenAI to analyze travel times between locations
    const openai = invokeOpenAI();

    // Sort appointments by time
    dateAppointments.sort((a, b) => {
        const timeA = a.appointmentDate; // Using timestamp directly
        const timeB = b.appointmentDate;
        return timeA - timeB;
    });

    // Prepare data for OpenAI
    const appointmentsData = dateAppointments.map((app) => ({
        id: app.id,
        time: new Date(app.appointmentDate * 1000).toLocaleString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }),
        location: app.location_string,
        coordinates: app.gps_coordinates
    }));
    const maxTravelTimeMinutes = 60;
    // Call OpenAI to analyze travel times
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            {
                role: 'system',
                content: 'You are a scheduling assistant that analyzes travel times between locations in London.'
            },
            {
                role: 'user',
                content: `I have these appointments on ${formattedDate}:
${JSON.stringify(appointmentsData, null, 2)}

I want to add a new appointment at this location: ${selectedLocation} ${selectedLocationLatLong ? `(coordinates: ${selectedLocationLatLong})` : ''}.

IMPORTANT INSTRUCTIONS FOR ACCURATE CALCULATIONS:
- Use real-world routing data (Google Maps API, MapBox, or similar) for travel time and distance calculations
- Consider actual road networks, traffic patterns, and routing constraints
- If coordinates are not provided, use geocoding services to obtain precise latitude/longitude
   - Use driving mode for all travel calculations (car/vehicle routing)
- Consider time of day for traffic-adjusted travel times

Analyze the travel times between all locations and identify time slots where I could fit additional 30-minute appointments.

SCHEDULING CONSTRAINTS:
- Each appointment takes 30 minutes
- Business hours: 9:00 AM to 12:00 PM (3-hour window)
- Minimum travel buffer: 30 minutes between appointments
- Maximum reasonable travel time: ${maxTravelTimeMinutes || 90} minutes between any two locations
- If constraints cannot be met, return availableSlots: []

CALCULATION REQUIREMENTS:
1. **Coordinate Estimation**: 
   - Use provided coordinates when available (must be within London boundaries)
   - For locations without coordinates, use geocoding APIs for precise lat/lng within London, UK
   - Fallback to London borough/area center coordinates if specific address unavailable
   - Validate all coordinates are within Greater London boundaries
   - Always include coordinate source in response (provided/geocoded/estimated)

2. **Distance & Duration Calculation**:
   - Use routing APIs (Google Maps Distance Matrix, MapBox, etc.) for real-world driving distances within London
   - Calculate driving distances via actual London road networks, not straight-line distances
   - Include London traffic considerations for driving time accuracy (consider peak hours, congestion zones)
   - Account for London-specific driving factors: congestion charge, ULEZ zones, narrow streets
   - Provide both optimistic (light traffic) and realistic (typical London traffic) estimates

3. **Error Handling**:
   - If API calls fail, use fallback estimation methods
   - Document any approximations or limitations in the response
   - Indicate confidence level of estimates (high/medium/low)

Return your response as a JSON object with:

1. **availableSlots**: Array of time slots in format "DD/MM/YYYY HH:MM:SS" (9AM-12PM only)

2. **travelEstimates**: Array of objects with:
   - "from": location name or ID
   - "to": destination name or ID  
   - "durationMinutes": travel time in minutes (realistic estimate with traffic)
   - "durationMinutesOptimistic": travel time without traffic/delays
   - "distanceKm": distance in kilometers via actual roads
   - "distanceKmStraightLine": straight-line distance for reference
   - "fromCoordinates": [latitude, longitude] for starting point
   - "toCoordinates": [latitude, longitude] for destination
   - "coordinateSource": "provided" | "geocoded" | "estimated"
   - "confidenceLevel": "high" | "medium" | "low"
   - "travelMode": "driving" (always driving mode for all calculations)
   - "notes": any relevant London driving considerations (congestion charge, ULEZ, peak hours, etc.)

3. **bookedAppointments**: Array of objects with:
   - "time": formatted appointment time
   - "location": location name/address
   - "coordinates": [latitude, longitude] if available
   - "id": appointment ID

4. **calculationNotes**: Array of strings explaining any approximations, API limitations, or assumptions made

5. **feasibilityAnalysis**: Object with:
   - "canAddAppointment": boolean
   - "reasonsIfNotFeasible": array of constraint violations
   - "suggestedAlternatives": array of alternative time slots or locations if applicable`
            }
        ],
        response_format: { type: 'json_object' }
    });
    try {
        const result = JSON.parse(response.choices[0].message.content as string);
        logger.info('OpenAI suggested available slots:', result);
        const { travelEstimates, bookedAppointments } = result;

        // Convert string time slots to timestamps and validate time range
        if (result.availableSlots && Array.isArray(result.availableSlots)) {
            const validatedSlots = result.availableSlots
                .map((slotStr: string) => {
                    try {
                        // Parse the time string
                        const hasComma = slotStr.includes(', ');
                        const [datePart, timePart] = hasComma ? slotStr.split(', ') : slotStr.split(' ');
                        const [hours, minutes] = timePart.split(':').map(Number);

                        // Check if the time is within 9AM to 12PM
                        if (hours < 9 || hours > 12) {
                            logger.warn(`Filtering out slot outside business hours: ${slotStr} - ${datePart} ${timePart} | ${hours}:${minutes}`);
                            return null;
                        }

                        // Convert to timestamp
                        const timestamp = convertUkTimeStringToTimestamp(slotStr);
                        return timestamp;
                    } catch (error) {
                        logger.error(`Error parsing time slot: ${slotStr}`, error);
                        return null;
                    }
                })
                .filter((timestamp: any): timestamp is number => timestamp !== null);

            logger.info(`Filtered slots: ${validatedSlots.length} valid slots within business hours`);
            return { validatedSlots, travelEstimates, bookedAppointments };
        }
        return { validatedSlots: [], travelEstimates, bookedAppointments };
    } catch (error) {
        logger.error('Error parsing OpenAI response:', error);

        // Fallback: Find gaps between appointments
        const availableSlots = fallbackCalculateTimeSlots(dateAppointments, businessStart, businessEnd);
        return {
            validatedSlots: availableSlots,
            travelEstimates: [],
            bookedAppointments: []
        };
    }
}

/**
 * Fallback method to calculate time slots when OpenAI fails
 */
function fallbackCalculateTimeSlots(dateAppointments: any[], businessStart: Date, businessEnd: Date): number[] {
    const availableSlots = [];
    let currentTime = new Date(businessStart);

    // Ensure business hours are strictly 9AM to 12PM
    const startHour = 9;
    const endHour = 12;

    // Reset business hours to ensure they're exactly 9AM to 12PM
    businessStart.setHours(startHour, 0, 0, 0);
    businessEnd.setHours(endHour, 0, 0, 0);

    // Sort appointments by time
    dateAppointments.sort((a, b) => a.appointmentDate - b.appointmentDate);

    for (const appointment of dateAppointments) {
        const appointmentTime = new Date(appointment.appointmentDate * 1000);

        // If there's at least 60 minutes before the appointment, add slots
        if (appointmentTime.getTime() - currentTime.getTime() >= 60 * 60 * 1000) {
            // Ensure we don't go past the end time
            const endTimeForSlots = new Date(Math.min(appointmentTime.getTime() - 30 * 60 * 1000, businessEnd.getTime()));

            const slotsBeforeAppointment = generateTimeSlotsAsTimestamps(currentTime, endTimeForSlots, 30);
            availableSlots.push(...slotsBeforeAppointment);
        }

        // Move current time to after the appointment plus 30 minutes buffer
        currentTime = new Date(appointmentTime.getTime() + 30 * 60 * 1000);
    }

    // Add slots after the last appointment if within business hours
    if (currentTime < businessEnd) {
        const slotsAfterLastAppointment = generateTimeSlotsAsTimestamps(currentTime, businessEnd, 30);
        availableSlots.push(...slotsAfterLastAppointment);
    }

    // Final validation to ensure all slots are within business hours
    return availableSlots.filter((timestamp) => {
        const date = new Date(timestamp * 1000);
        const hour = date.getHours();
        return hour >= startHour && hour < endHour;
    });
}

/**
 * Converts a UK time string in format "DD/MM/YYYY HH:MM:SS" to timestamp
 */
function convertUkTimeStringToTimestamp(timeString: string): number {
    // Handle both formats: with and without comma
    const hasComma = timeString.includes(', ');
    const [datePart, timePart] = hasComma ? timeString.split(', ') : timeString.split(' ');
    const [day, month, year] = datePart.split('/').map(Number);
    const [hours, minutes, seconds] = timePart.split(':').map(Number);

    const date = new Date(year, month - 1, day, hours, minutes, seconds || 0);
    return Math.floor(date.getTime() / 1000);
}

/**
 * Generates time slots between start and end times with specified interval as timestamps
 * Ensures all slots are within business hours (9AM to 12PM)
 */
function generateTimeSlotsAsTimestamps(start: Date, end: Date, intervalMinutes: number): number[] {
    const slots = [];
    let current = new Date(start);

    // Ensure start time is not before 9AM
    const businessStart = new Date(current);
    businessStart.setHours(9, 0, 0, 0);
    if (current < businessStart) {
        current = new Date(businessStart);
    }

    // Ensure end time is not after 12PM
    const businessEnd = new Date(end);
    businessEnd.setHours(12, 0, 0, 0);
    if (end > businessEnd) {
        end = new Date(businessEnd);
    }

    // Generate slots within the validated time range
    while (current < end) {
        const hour = current.getHours();

        // Only add slots between 9AM and 12PM
        if (hour >= 9 && hour < 12) {
            slots.push(Math.floor(current.getTime() / 1000));
        }

        current = new Date(current.getTime() + intervalMinutes * 60 * 1000);
    }

    return slots;
}
