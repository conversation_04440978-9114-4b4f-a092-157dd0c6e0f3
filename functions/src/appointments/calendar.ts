import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import { tokenKey } from '../utils';
import CryptoJS from 'crypto-js';

// Updated processLeadAppointmentFromCalendarEvent with batch support
export async function processLeadAppointmentFromCalendarEvent(batch: admin.firestore.WriteBatch, item: any) {
    try {
        const action = item.status === 'cancelled' ? 'cancel' : 'add';
        if (!item?.summary || (item.summary && item.summary.includes('No Appointments'))) {
            return { skipped: true, id: item.id }; // Return a value for skipped events
        }

        let name, email, phone, address, tenantId, location_string, gps_coordinates, doorType, doorSize, customSize, has_alternative_garage_access, wants_external_override, has_electrical_connection, doorColor, customColor, tradeIn, google_link;
        let notes = [];
        let leadId = '';

        if (action !== 'cancel') {
            // First, strip HTML tags from the description to make regex matching easier
            const stripHtml = (html: string) => {
                return html
                    ? html
                          .replace(/<[^>]*>/g, ' ')
                          .replace(/\s+/g, ' ')
                          .trim()
                    : '';
            };

            let cleanDescription = '';
            if (item?.description) {
                cleanDescription = stripHtml(item.description);
                logger.info(`Cleaned description for event ${item.id}: ${cleanDescription.substring(0, 100)}...`);
            }

            // Updated regex pattern for plain text (after HTML is stripped)
            const regexPattern = /Booked by\s+(.*?)\s+([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)\s+(\+?\d+)\s+House name or Number\s+(.*?)\s+Postcode\s+(.*?)(?:\s|$)/;
            const matches = cleanDescription ? cleanDescription.match(regexPattern) : null;

            if (matches) {
                const [_, nameF, emailF, phoneF, addressF, postcodeF] = matches;
                name = nameF;
                email = emailF;
                phone = phoneF;
                address = `${addressF}, ${postcodeF}`;
                logger.info(`Regex match found for event: ${item.id}`, {
                    name,
                    email,
                    phone,
                    address
                });
            } else {
                logger.info(`No regex match found for event: ${item.id}`);

                // Try to extract email with a simpler regex
                const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/;
                if (cleanDescription) {
                    const emailMatch = cleanDescription.match(emailRegex);
                    if (emailMatch) {
                        email = emailMatch[0];
                        logger.info(`Found email with simple regex: ${email}`);
                    }
                }

                // Try to extract phone with a simpler regex
                const phoneRegex = /(\+?\d{10,})/;
                if (cleanDescription) {
                    const phoneMatch = cleanDescription.match(phoneRegex);
                    if (phoneMatch) {
                        phone = phoneMatch[0];
                        logger.info(`Found phone with simple regex: ${phone}`);
                    }
                }
            }

            if (!address && item?.location) {
                address = item.location;
                logger.info(`Using location as address: ${address}`);
            }

            if (item?.description) notes.push(item.description);
        }

        if (item?.description) {
            const leadIdRegex = /UID:\s*\(([^)]+)\)/;
            const leadEncryptedIdRegex = /EncryptedID:\s*\(([^)]+)\)/;

            const leadIdMatch = item.description.match(leadIdRegex);
            if (leadIdMatch && leadIdMatch[1]) {
                if (typeof leadIdMatch !== 'undefined' && leadIdMatch[1] !== undefined) {
                    leadId = leadIdMatch[1];
                    logger.info(`Found lead ID in description: ${leadId}`);
                }
            }

            const leadEncryptedIdMatch = item.description.match(leadEncryptedIdRegex);
            if (leadEncryptedIdMatch && leadEncryptedIdMatch[1]) {
                const encrypted = leadEncryptedIdMatch[1];
                try {
                    const bytes = CryptoJS.AES.decrypt(encrypted, tokenKey);
                    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);

                    if (decryptedData) {
                        try {
                            const parsedData = JSON.parse(decryptedData);
                            if (parsedData.tenantId) tenantId = parsedData.tenantId;
                            if (parsedData.location_string) location_string = parsedData.location_string;
                            if (parsedData.gps_coordinates) gps_coordinates = parsedData.gps_coordinates;
                            if (parsedData.notes) notes = parsedData.notes;
                            if (parsedData.doorType) doorType = parsedData.doorType;
                            if (parsedData.doorSize) doorSize = parsedData.doorSize;
                            if (parsedData.customSize) customSize = parsedData.customSize;
                            if (parsedData.doorColor) doorColor = parsedData.doorColor;
                            if (parsedData.customColor) customColor = parsedData.customColor;
                            if (parsedData.tradeIn) tradeIn = parsedData.tradeIn;
                            if (parsedData.google_link) google_link = parsedData.google_link;
                            if (parsedData.has_alternative_garage_access) has_alternative_garage_access = parsedData.has_alternative_garage_access;
                            if (parsedData.wants_external_override) wants_external_override = parsedData.wants_external_override;
                            if (parsedData.has_electrical_connection) has_electrical_connection = parsedData.has_electrical_connection;
                            logger.info(`Found encrypted lead data in description for event ${item.id}`);
                        } catch (e) {
                            logger.error(`Invalid JSON format in decrypted data for event ${item.id}:`, e);
                        }
                    } else {
                        logger.error(`Decryption resulted in empty string for event ${item.id}`);
                    }
                } catch (e) {
                    logger.error(`Decryption failed for event ${item.id}:`, e);
                }
            }
        }

        const currentTime = Math.floor(Date.now() / 1000);
        const lead = {
            id: item.id,
            lead: {
                id: leadId || '',
                name: name || item.summary || '',
                email: email || '',
                phone: phone || '',
                tenantId: tenantId || ''
            },
            appointmentDate: item.start?.dateTime ? Math.floor(new Date(item.start.dateTime).getTime() / 1000) : currentTime,
            address: address || '',
            description: item.description || '',
            start: item.start || {},
            end: item.end || {},
            creator: item.creator || {},
            location_string: location_string || address || '',
            gps_coordinates: gps_coordinates || '',
            doorType: doorType || '',
            doorSize: doorSize || '',
            customSize: customSize || '',
            doorColor: doorColor || '',
            customColor: customColor || '',
            tradeIn: tradeIn || false,
            notes: notes || [],
            google_link: google_link || '',
            has_alternative_garage_access: has_alternative_garage_access || '',
            wants_external_override: wants_external_override || '',
            has_electrical_connection: has_electrical_connection || ''
        };

        // Use batch-compatible saveLeadAppointment
        await saveLeadAppointmentWithBatch(batch, item.id, lead, item, action);

        return { lead: lead, id: item.id, error: false };
    } catch (error) {
        logger.error(`Error processing event ${item?.id || 'unknown'}:`, error);
        return {
            error: true,
            id: item?.id || 'unknown',
            message: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}

// Updated saveLeadAppointment to use batch operations
export async function saveLeadAppointmentWithBatch(batch: admin.firestore.WriteBatch, eventId: any, leadData: any, event: any, action?: string) {
    try {
        const db = admin.firestore();
        const leadsCollection = db.collection('leads');
        const appointmentsCollection = db.collection('appointments');

        // 1. Find or create lead document
        let leadDocRef: any = null;
        let leadDocData: any = null;
        let leadId = '';
        let appointmentId = '';

        // Check if we have a valid lead ID and it exists in the database
        if (leadData.lead?.id && leadData.lead.id !== '') {
            leadDocRef = leadsCollection.doc(leadData.lead.id);
            const doc = await leadDocRef.get();
            if (doc.exists) {
                leadDocData = doc.data();
                leadId = doc.id;
                logger.info(`Found lead document with ID: ${leadId}`);
            } else {
                logger.info(`Lead ID ${leadData.lead.id} provided but document not found`);
                leadDocRef = null; // Reset if document doesn't exist
            }
        }

        // If we don't have a valid lead document yet, search by contact info
        if (!leadDocRef) {
            const contactFields = [
                { field: 'phone', value: leadData.lead.phone },
                { field: 'email', value: leadData.lead.email },
                { field: 'name', value: leadData.lead.name },
                { field: 'address', value: leadData.location_string }
            ];

            for (const { field, value } of contactFields) {
                if (value && value.trim() !== '') {
                    logger.info(`Searching for lead by ${field}: ${value}`);
                    const querySnapshot = await leadsCollection.where(field, '==', value).where('lead_actions', 'in', ['inquired', 'booked']).limit(1).get();

                    if (!querySnapshot.empty) {
                        leadDocRef = querySnapshot.docs[0].ref;
                        leadDocData = querySnapshot.docs[0].data();
                        leadId = querySnapshot.docs[0].id;
                        logger.info(`Found lead by ${field}: ${leadId}`);
                        break;
                    }
                }
            }
        }

        // Check if event was already processed recently
        const processedEvents = admin.firestore().collection('processedEvents');
        const eventRecord = {
            timestamp: Date.now(),
            lastProcessed: new Date().toISOString(),
            eventId: event.id || '',
            summary: event?.summary || '',
            description: event?.description || '',
            status: event?.status || 'unknown',
            location: event?.location || 'unknown'
        };

        // 2. Handle cancellation
        if (action === 'cancel') {
            // Find appointment by eventId or location_string
            let appointmentsToDelete: any[] = [];

            // First try to find by eventId (most reliable)
            const appointmentsByEventId = await appointmentsCollection.where('eventId', '==', eventId).get();

            if (!appointmentsByEventId.empty) {
                appointmentsToDelete = appointmentsByEventId.docs;
                logger.info(`Found ${appointmentsToDelete.length} appointments to delete by eventId`);
            }
            // If no appointments found by eventId and we have location_string, try that
            else if (leadData.location_string && appointmentsToDelete.length === 0) {
                const appointmentsByLocation = await appointmentsCollection.where('location_string', '==', leadData.location_string).get();

                // Filter to only include appointments with matching eventId
                appointmentsToDelete = appointmentsByLocation.docs.filter((doc) => doc.data().eventId === eventId);
                logger.info(`Found ${appointmentsToDelete.length} appointments to delete by location`);
            }

            // Delete all found appointments
            for (const doc of appointmentsToDelete) {
                batch.delete(doc.ref);
                logger.info(`Marked appointment ${doc.id} for deletion`);

                // If we don't have a lead reference yet, try to get it from the appointment
                if (!leadDocRef) {
                    const appData = doc.data();
                    if (appData.lead?.id && appData.lead.id !== '') {
                        const leadDoc = await leadsCollection.doc(appData.lead.id).get();
                        if (leadDoc.exists) {
                            leadDocRef = leadDoc.ref;
                            leadDocData = leadDoc.data();
                            leadId = leadDoc.id;
                            logger.info(`Found lead ${leadId} from appointment data`);
                        }
                    }
                }
            }

            // Update lead document if we found one
            if (leadDocRef && leadId && leadDocData?.appointmentHistory) {
                const filteredHistory = leadDocData.appointmentHistory.filter((app: any) => app.eventId !== eventId);

                // If no appointments left and minimal conversation, delete lead
                if (filteredHistory.length === 0 && (!leadDocData.conversations || leadDocData.conversations.length <= 1)) {
                    batch.delete(leadDocRef);
                    logger.info(`Marked lead ${leadId} for deletion (no appointments left)`);
                }
                // Otherwise update the lead with filtered appointment history
                else {
                    batch.update(leadDocRef, {
                        appointmentHistory: filteredHistory,
                        lead_actions: filteredHistory.length === 0 ? 'inquired' : 'booked'
                    });
                    logger.info(`Updated lead ${leadId} appointment history (removed cancelled appointment)`);
                }
            } else {
                logger.info(`No lead document found to update for cancelled event ${eventId}`);
            }

            // Update processed events tracking
            batch.set(processedEvents.doc(event.id), {
                ...eventRecord,
                leadId: leadId || ''
            });
            return { leadId: leadId || '', appointmentId: '' };
        }

        // 3. Create or update lead
        if (!leadDocRef) {
            // Create a new lead document with an auto-generated ID
            leadDocRef = leadsCollection.doc();
            leadId = leadDocRef.id;
            logger.info(`Creating new lead with ID: ${leadId}`);

            const newLeadData = {
                id: leadId,
                name: leadData.lead.name ?? '',
                phone: leadData.lead.phone ?? '',
                email: leadData.lead.email ?? '',
                tenantId: leadData.lead.tenantId ?? '',
                lead_source: !leadData.gps_coordinates ? 'google_calendar_booking' : 'appointment_booking',
                lead_actions: 'booked',
                lastMessageTime: Math.floor(Date.now() / 1000),
                mark_for_deletion: false,
                conversations: [
                    {
                        role: 'user',
                        message: !leadData.gps_coordinates ? 'Booked an appointment through Google Calendar.' : 'Booked an appointment through the CRM website.',
                        lastMessageTime: Math.floor(Date.now() / 1000)
                    }
                ],
                keywords: [
                    ...(leadData.lead.name ? [leadData.lead.name] : []),
                    ...(leadData.lead.email ? [leadData.lead.email] : []),
                    ...(leadData.lead.phone ? [leadData.lead.phone] : []),
                    leadId,
                    ...(leadData.lead.tenantId ? [leadData.lead.tenantId] : [])
                ].filter(Boolean)
            };

            batch.set(leadDocRef, newLeadData);
            leadDocData = newLeadData;
        }

        // Verify we have a valid lead document reference before proceeding
        if (!leadDocRef || !leadId) {
            logger.error(`Failed to create or find a valid lead document for event ${eventId}`);
            return { leadId: '', appointmentId: '' };
        }

        // 4. Handle appointment
        const appointmentSnapshot = await appointmentsCollection.where('eventId', '==', eventId).limit(1).get();

        let appointmentDocRef;
        if (!appointmentSnapshot.empty) {
            appointmentDocRef = appointmentSnapshot.docs[0].ref;
            appointmentId = appointmentSnapshot.docs[0].id;
            logger.info(`Found existing appointment ${appointmentId} for event ${eventId}`);
        } else {
            appointmentDocRef = appointmentsCollection.doc();
            appointmentId = appointmentDocRef.id;
            logger.info(`Creating new appointment with ID: ${appointmentId}`);
        }

        const appointmentData = {
            id: appointmentId,
            appointmentDate: leadData.appointmentDate,
            location_string: leadData.location_string ?? '',
            gps_coordinates: leadData.gps_coordinates ?? '',
            doorType: leadData.doorType ?? '',
            doorSize: leadData.doorSize ?? '',
            customSize: leadData.customSize ?? '',
            doorColor: leadData.doorColor ?? '',
            customColor: leadData.customColor ?? '',
            tradeIn: leadData.tradeIn ?? false,
            notes: leadData.notes ?? [],
            google_link: leadData.google_link ?? '',
            eventId: eventId,
            createdAt: Math.floor(Date.now() / 1000),
            calendar_event: event ?? {},
            keywords: [
                leadId,
                ...(leadData.lead.name ? [leadData.lead.name] : []),
                ...(leadData.lead.email ? [leadData.lead.email] : []),
                ...(leadData.lead.phone ? [leadData.lead.phone] : []),
                appointmentId,
                ...(leadData.lead.tenantId ? [leadData.lead.tenantId] : [])
            ].filter(Boolean),
            lead: {
                id: leadId,
                name: leadData.lead.name ?? '',
                phone: leadData.lead.phone ?? '',
                email: leadData.lead.email ?? '',
                tenantId: leadData.lead.tenantId ?? ''
            }
        };

        // Update lead's appointment history
        const currentHistory = leadDocData?.appointmentHistory || [];

        // Check if this event already exists in history
        const existingIndex = currentHistory.findIndex((app: any) => app.eventId === eventId);
        let updatedHistory;

        if (existingIndex >= 0) {
            // Update existing entry
            currentHistory[existingIndex] = {
                ...currentHistory[existingIndex],
                ...appointmentData,
                updatedAt: Math.floor(Date.now() / 1000)
            };
            updatedHistory = [...currentHistory];
            logger.info(`Updated existing appointment in history at index ${existingIndex}`);
        } else {
            // Add new entry
            updatedHistory = [...currentHistory, appointmentData];
            logger.info(`Added new appointment to history`);
        }

        // Filter out any empty objects
        updatedHistory = updatedHistory.filter((item: any) => item && Object.keys(item).length > 0);

        batch.set(
            leadDocRef,
            {
                latestAppointmentDate: leadData.appointmentDate,
                appointmentHistory: updatedHistory,
                lead_actions: 'booked',
                lastMessageTime: appointmentData.createdAt
            },
            { merge: true }
        );

        batch.set(appointmentDocRef, appointmentData, { merge: true });

        // Update processed events tracking
        batch.set(processedEvents.doc(event.id || `manual-${Date.now()}`), {
            ...eventRecord,
            leadId: leadId
        });

        logger.info(`Successfully prepared batch operations for event ${eventId}`);

        return { leadId, appointmentId };
    } catch (error) {
        logger.error(`Error in saveLeadAppointmentWithBatch for event ${eventId}:`, error);
        throw error;
    }
}

// Legacy function for backward compatibility
export async function saveLeadAppointment(eventId: any, leadData: any, event: any, action?: string) {
    const batch = admin.firestore().batch();
    const result = await saveLeadAppointmentWithBatch(batch, eventId, leadData, event, action);
    await batch.commit();
    return result;
}
