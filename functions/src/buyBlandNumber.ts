import * as logger from 'firebase-functions/logger';
import { send<PERSON>rid<PERSON><PERSON><PERSON><PERSON>, requireAuth, vapiAIApi<PERSON><PERSON> } from './utils';
import { HttpsError, onCall } from 'firebase-functions/v2/https';
import axios from 'axios';

// Interface for Bland AI inbound number purchase parameters
interface BlandNumberPurchaseParams {
    phone_number: string;
    webhook?: string;
    max_duration?: number;
    voice_id?: string;
    first_sentence?: string;
    model?: 'base' | 'turbo';
    language?: string;
    timezone?: string;
    temperature?: number;
    background_track?: 'office' | 'cafe' | 'restaurant' | 'none';
    wait_for_greeting?: boolean;
    noise_cancellation?: boolean;
    block_interruptions?: boolean;
    ignore_button_press?: boolean;
    pronunciation_guide?: Array<{
        word: string;
        pronunciation: string;
    }>;
    interruption_threshold?: number;
    tools?: string[];
    task?: string;
    pathway_id?: string;
    request_data?: Record<string, any>;
}

// Interface for Bland AI API response
interface BlandNumberPurchaseResponse {
    success: boolean;
    phone_number?: string;
    inbound_id?: string;
    message?: string;
    error?: string;
}

// Helper function to validate phone number format
function validatePhoneNumber(phoneNumber: string): boolean {
    // E.164 format validation
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
}

// Helper function to validate webhook URL
function validateWebhookUrl(url: string): boolean {
    try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'https:' || urlObj.protocol === 'http:';
    } catch {
        return false;
    }
}

// Helper function to get supported languages
function getSupportedLanguages(): string[] {
    return [
        'en-US',
        'en-GB',
        'en-AU',
        'en-CA',
        'en-IN',
        'en-NZ',
        'en-ZA',
        'es-ES',
        'es-MX',
        'es-AR',
        'es-CO',
        'es-CL',
        'es-PE',
        'es-VE',
        'fr-FR',
        'fr-CA',
        'de-DE',
        'it-IT',
        'pt-BR',
        'pt-PT',
        'nl-NL',
        'sv-SE',
        'no-NO',
        'da-DK',
        'fi-FI',
        'pl-PL',
        'ru-RU',
        'ja-JP',
        'ko-KR',
        'zh-CN',
        'zh-TW',
        'hi-IN',
        'ar-SA',
        'tr-TR',
        'he-IL',
        'th-TH',
        'vi-VN',
        'id-ID'
    ];
}

// Helper function to get supported timezones
function getSupportedTimezones(): string[] {
    return [
        'America/New_York',
        'America/Chicago',
        'America/Denver',
        'America/Los_Angeles',
        'America/Toronto',
        'America/Vancouver',
        'America/Mexico_City',
        'America/Sao_Paulo',
        'Europe/London',
        'Europe/Paris',
        'Europe/Berlin',
        'Europe/Rome',
        'Europe/Madrid',
        'Europe/Amsterdam',
        'Europe/Stockholm',
        'Europe/Oslo',
        'Europe/Copenhagen',
        'Europe/Helsinki',
        'Europe/Warsaw',
        'Europe/Moscow',
        'Asia/Tokyo',
        'Asia/Seoul',
        'Asia/Shanghai',
        'Asia/Hong_Kong',
        'Asia/Singapore',
        'Asia/Mumbai',
        'Asia/Dubai',
        'Asia/Bangkok',
        'Asia/Jakarta',
        'Australia/Sydney',
        'Australia/Melbourne',
        'Pacific/Auckland'
    ];
}

// Buy a Bland AI inbound phone number
export const onBuyBlandNumber = onCall(
    {
        cors: true,
        secrets: [vapiAIApiKey, sendGridApiKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;

        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);

        try {
            const purchaseParams: BlandNumberPurchaseParams = data.purchaseParams || {};

            // Support both nested and direct phone number parameter
            const phoneNumber = purchaseParams.phone_number || data.phone_number;

            // Validate required parameters
            if (!phoneNumber) {
                throw new Error('Phone number is required');
            }

            // Validate phone number format
            if (!validatePhoneNumber(phoneNumber)) {
                throw new Error('Invalid phone number format. Must be in E.164 format (e.g., +1234567890)');
            }

            // Validate optional parameters
            if (purchaseParams.webhook && !validateWebhookUrl(purchaseParams.webhook)) {
                throw new Error('Invalid webhook URL format. Must be a valid HTTP/HTTPS URL');
            }

            if (purchaseParams.language) {
                const supportedLanguages = getSupportedLanguages();
                if (!supportedLanguages.includes(purchaseParams.language)) {
                    throw new Error(`Unsupported language: ${purchaseParams.language}. Supported languages: ${supportedLanguages.join(', ')}`);
                }
            }

            if (purchaseParams.timezone) {
                const supportedTimezones = getSupportedTimezones();
                if (!supportedTimezones.includes(purchaseParams.timezone)) {
                    throw new Error(`Unsupported timezone: ${purchaseParams.timezone}. Supported timezones: ${supportedTimezones.join(', ')}`);
                }
            }

            if (purchaseParams.max_duration && (purchaseParams.max_duration < 1 || purchaseParams.max_duration > 60)) {
                throw new Error('Max duration must be between 1 and 60 minutes');
            }

            if (purchaseParams.temperature && (purchaseParams.temperature < 0 || purchaseParams.temperature > 2)) {
                throw new Error('Temperature must be between 0 and 2');
            }

            if (purchaseParams.interruption_threshold && (purchaseParams.interruption_threshold < 50 || purchaseParams.interruption_threshold > 500)) {
                throw new Error('Interruption threshold must be between 50 and 500 milliseconds');
            }

            // Build request payload with defaults for GB/UK
            const requestPayload: any = {
                phone_number: phoneNumber,
                webhook: purchaseParams.webhook || '',
                max_duration: purchaseParams.max_duration || 12,
                voice_id: purchaseParams.voice_id || 'default',
                model: purchaseParams.model || 'base',
                language: purchaseParams.language || 'en-GB', // Default to British English
                timezone: purchaseParams.timezone || 'Europe/London', // Default to London timezone
                temperature: purchaseParams.temperature || 0.7,
                background_track: purchaseParams.background_track || 'office',
                wait_for_greeting: purchaseParams.wait_for_greeting !== undefined ? purchaseParams.wait_for_greeting : true,
                noise_cancellation: purchaseParams.noise_cancellation !== undefined ? purchaseParams.noise_cancellation : true,
                block_interruptions: purchaseParams.block_interruptions !== undefined ? purchaseParams.block_interruptions : false,
                ignore_button_press: purchaseParams.ignore_button_press !== undefined ? purchaseParams.ignore_button_press : false,
                interruption_threshold: purchaseParams.interruption_threshold || 170
            };

            // Add optional parameters if provided
            if (purchaseParams.first_sentence) {
                requestPayload.first_sentence = purchaseParams.first_sentence;
            }

            if (purchaseParams.pronunciation_guide && purchaseParams.pronunciation_guide.length > 0) {
                requestPayload.pronunciation_guide = purchaseParams.pronunciation_guide;
            }

            if (purchaseParams.tools && purchaseParams.tools.length > 0) {
                requestPayload.tools = purchaseParams.tools;
            }

            if (purchaseParams.task) {
                requestPayload.task = purchaseParams.task;
            }

            if (purchaseParams.pathway_id) {
                requestPayload.pathway_id = purchaseParams.pathway_id;
            }

            if (purchaseParams.request_data) {
                requestPayload.request_data = purchaseParams.request_data;
            }

            logger.info('Attempting to purchase Bland AI inbound number:', phoneNumber);
            logger.info('Request payload:', requestPayload);

            // Make API request to Bland AI
            const response = await axios.post('https://api.bland.ai/v1/inbound/purchase', requestPayload, {
                headers: {
                    Authorization: vapiAIApiKey.value(),
                    'Content-Type': 'application/json'
                },
                timeout: 30000 // 30 second timeout
            });

            const responseData: BlandNumberPurchaseResponse = response.data;

            if (responseData.success) {
                logger.info('Successfully purchased Bland AI inbound number:', {
                    phone_number: responseData.phone_number,
                    inbound_id: responseData.inbound_id
                });

                return {
                    success: true,
                    phone_number: responseData.phone_number,
                    inbound_id: responseData.inbound_id,
                    message: responseData.message || 'Number purchased successfully',
                    configuration: {
                        webhook: requestPayload.webhook,
                        max_duration: requestPayload.max_duration,
                        voice_id: requestPayload.voice_id,
                        model: requestPayload.model,
                        language: requestPayload.language,
                        timezone: requestPayload.timezone,
                        temperature: requestPayload.temperature,
                        background_track: requestPayload.background_track,
                        wait_for_greeting: requestPayload.wait_for_greeting,
                        noise_cancellation: requestPayload.noise_cancellation,
                        block_interruptions: requestPayload.block_interruptions,
                        ignore_button_press: requestPayload.ignore_button_press,
                        interruption_threshold: requestPayload.interruption_threshold
                    }
                };
            } else {
                throw new Error(responseData.error || responseData.message || 'Failed to purchase number');
            }
        } catch (error) {
            logger.error('Error purchasing Bland AI number:', error);

            if (axios.isAxiosError(error)) {
                const status = error.response?.status;
                const errorData = error.response?.data;

                // Handle specific Bland AI API errors
                if (status === 400) {
                    throw new HttpsError('invalid-argument', `Invalid request: ${errorData?.error || errorData?.message || 'Bad request'}`);
                } else if (status === 401) {
                    throw new HttpsError('unauthenticated', 'Invalid Bland AI API key');
                } else if (status === 402) {
                    throw new HttpsError('failed-precondition', 'Insufficient credits in Bland AI account');
                } else if (status === 404) {
                    throw new HttpsError('not-found', 'Phone number not available for purchase');
                } else if (status === 409) {
                    throw new HttpsError('already-exists', 'Phone number already purchased or in use');
                } else if (status === 429) {
                    throw new HttpsError('resource-exhausted', 'Rate limit exceeded. Please try again later');
                } else if (status && status >= 500) {
                    throw new HttpsError('internal', 'Bland AI service temporarily unavailable');
                }

                throw new HttpsError('internal', `API request failed: ${errorData?.error || error.message}`);
            }

            if (error instanceof Error) {
                throw new HttpsError('internal', `Error purchasing number: ${error.message}`);
            }

            throw new HttpsError('internal', 'Error purchasing Bland AI number');
        }
    }
);
