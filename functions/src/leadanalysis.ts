import { onSchedule } from "firebase-functions/v2/scheduler";
import { openAIKey, requireAuth } from "./utils";
import * as logger from "firebase-functions/logger";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { handleLeadAnalysis, processLeadsByIds } from "./analysis";

export const onAILeadAnalysis = onSchedule(
  {
    schedule: "0 0 * * *",
    secrets: [openAIKey],
    cpu: 4,
    memory: "16GiB",
    timeoutSeconds: 3600,
  },
  async () => {
    try {
      await handleLeadAnalysis();
      logger.info("Finished processing all leads.");
      return;
    } catch (error) {
      logger.error("Error failed processing all leads:", error);
      throw new HttpsError("internal", "Error failed processing all leads.");
    }
  },
);

export const onAILeadAnalysisByIds = onCall(
  {
    cors: true,
    secrets: [openAIKey],
    cpu: 4,
    memory: "16GiB",
    timeoutSeconds: 3600,
  },
  async (request) => {
    const { data, auth } = request;
    if (!auth) {
      throw new HttpsError("unauthenticated", "Authentication required");
    }

    requireAuth(auth);
    try {
      const leadIds: string[] = data.id ? [data.id] : data.ids;
      if (leadIds.length === 0) {
        logger.warn("No lead IDs provided.");
        throw new Error("No lead IDs provided.");
      }

      return await processLeadsByIds(leadIds);
    } catch (err) {
      logger.error("Error processing lead.", err);
      throw new HttpsError("unknown", "Error processing lead.");
    }
  },
);
