export interface UserFilters {
  name?: string;
  id?: string;
  email?: string;
  role?: string[];
  sort?: {
    col: string;
    order: "asc" | "desc";
  };
  page?: {
    pageSize: number;
    page: number;
  };
}

export interface EmailBody {
  called: any;
  headers: any;
  attachments: any;
  dkim: any;
  subject: any;
  to: any;
  html: any;
  from: any;
  text: any;
  sender_ip: any;
  envelope: any;
  charsets: any;
  SPF: any;
}
