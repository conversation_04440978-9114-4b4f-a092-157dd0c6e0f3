import { invokeOpenAI } from '../utils';
import { HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';
import { ThreadCreateParams } from 'openai/src/resources/beta/threads/threads';
// import { getFollowUpParams } from '../followup';

export async function handleLeadAnalysis() {
    try {
        await processLeads();
    } catch (error) {
        logger.error('Error handling user query:', error);
        throw new HttpsError('internal', 'Failed to handle user query.');
    }
}

export async function processLead(leadData: any, analysisAssistantId: any, leadId: string) {
    try {
        const openai = invokeOpenAI();
        logger.info(`Lead ${leadId} Processing lead with analysisAssistantId`, analysisAssistantId);
        if (analysisAssistantId.id) {
            const chatmessages: ThreadCreateParams = {
                messages: [
                    {
                        role: 'user',
                        content: JSON.stringify(leadData) // this is an array of conversations
                    }
                ]
            };

            // Create a thread for the conversation
            const thread = await openai.beta.threads.create(chatmessages);

            // Run the assistant
            const run = await openai.beta.threads.runs.create(thread.id, {
                assistant_id: analysisAssistantId.id as string
            });

            // Wait for the assistant's response
            let runStatus = await openai.beta.threads.runs.retrieve(thread.id, run.id);
            while (runStatus.status !== 'completed') {
                await new Promise((resolve) => setTimeout(resolve, 1000));
                runStatus = await openai.beta.threads.runs.retrieve(thread.id, run.id);
            }

            // Retrieve the assistant's response
            const messages = await openai.beta.threads.messages.list(thread.id);
            const assistantMessage = messages.data[0].content[0];

            if (assistantMessage.type === 'text') {
                try {
                    // Assuming assistantMessage.text.value contains JSON enclosed in ```json ```
                    const jsonString = assistantMessage.text.value.replace(/```json\s*|\s*```/g, '');
                    logger.info(`Lead ${leadId} Successfully processed user query to OpenAI from assistant`, assistantMessage.text.value);

                    const response = JSON.parse(jsonString);
                    return response;
                } catch (error) {
                    logger.error(`Lead ${leadId} Failed to parse JSON response:`, error);
                    return null;
                }
            } else {
                logger.error(`Lead ${leadId} Failed to handle user query not a text result type.`);
                return null;
            }
        } else if (analysisAssistantId.prompt) {
            const chatmessages = [];
            chatmessages.push({
                role: 'system',
                content:
                    'Customer Data: ' +
                    JSON.stringify({
                        name: leadData?.name || '',
                        email: leadData?.email || '',
                        phone: leadData?.phone || '',
                        address: leadData?.address || '',
                        lead_source: leadData.lead_source
                    })
            });

            analysisAssistantId.prompt.prompt =
                analysisAssistantId.prompt.prompt +
                `## Output Requirements**  
- Format all text in **British English** (e.g., "colour", "programme", "metre").  
- Ensure strict JSON compliance:  
  - No trailing commas.  
  - All keys/values in double quotes (\`"\`).  
  - Empty optional fields as \`""\`.  
- Set \`to_delete: true\` for irrelevant leads (e.g., no contact details, no intent).  

---

## **Final JSON Schema with All Properties**

\`\`\`json
{
  "lead_id": "string",
  "email": "string (optional)",
  "name": "string (optional; see naming rules above)",
  "phone": "string (optional).  If the number starts with '0', remove the '0' and replace it with '+44'.",
  "address": "string (optional)",
  "interaction_type": "'AI', 'Human', or 'AI & Human'",
  "ai_interactions": "number",
  "human_interactions": "number",
  "last_message_time": "number (timestamp)",
  "lead_status": "'cold', 'warm', or 'hot'",
  "lead_source_classification": "'Missed Call', 'High Intent', 'Engaged', 'Unresponsive', or 'Irrelevant'",
  "lead_interest": "'Interested' or 'Not Interested'",
  "is_qualified": "boolean",
  "to_delete": "boolean",
  "spam_detection": {
    "is_spam": "boolean",
    "spam_score": "number (between 0 and 1)",
    "reason": "string"
  },
  "follow_up_recommendation": {
    "to_followup_date": "number (timestamp, 9am–7pm UK Time)",
    "reason": "string",
    "should_pursue_followup": "boolean",
    "recurrence_strategy": {
      "frequency_days": "number",
      "max_followups": "number"
    }
  },
  "ai_conversation_summary": "string (British English)",
  "conversation_summary": "string (British English)",
  "conversation_analysis": [
    {
      "timestamp": "number (interaction timestamp)",
      "mood": "string (Positive/Neutral/Negative)",
      "sentiment_score": "number (-1 to 1)",
      "key_phrases": ["string"],
      "interaction_type": "'AI', 'Human', or 'AI & Human'",
      "engagement_metrics": {
        "response_time_seconds": "number",
        "message_length_words": "number",
        "interaction_duration_seconds": "number"
      },
      "sentiment_trend": {
        "previous_sentiment_score": "number",
        "change_in_sentiment": "number"
      }
    }
  ],
  "sales_advice": {
    "insights_for_conversion": "string (British English)",
    "qualification_strategy": "string (British English)",
    "follow_up_strategies": [
      {
        "channel": "string",
        "strategy": "string (British English)"
      }
    ]
  },
  "has_booking_intent": "boolean (required)",
  "booking_opportunity": {
    "type": "object",
    "properties": {
       "appointmentDate": "number timestamp (required) booking, survey, installation date",
      "location_string": "string (required) customer address",
      "gps_coordinates": ["number", "number"] (required) [latitude, longitude],
      "notes": ["string"] (optional) key installation requirements,
      "doorSize": "string (optional) e.g., 'Double Door', 'Custom Size'",
      "doorColor": "string (optional) e.g., 'White', 'Charcoal Grey'",
      "tradeIn": "boolean (optional) true if replacing old door"
    },
    "required": ["location_string", "gps_coordinates"]
  }
}
\`\`\``;

            chatmessages.push({
                role: 'system',
                content: analysisAssistantId.prompt.prompt
            });
            delete analysisAssistantId.prompt.prompt;

            if (leadData.conversations?.length && leadData.conversations?.length > 0) {
                let convos = [];
                const limitedMessageHistory = leadData.conversations.filter((message: any) => message.role === 'user' || message.role === 'assistant');

                // Ensure the message history is in the correct format
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                if (limitedMessageHistory?.length) {
                    convos = limitedMessageHistory
                        .filter((msg: any) => msg.content)
                        .map((msg: any) => ({
                            role: msg.role,
                            content: msg.message
                        }));
                } else {
                    convos = [];
                }
                if (convos.length > 0) {
                    chatmessages.push({
                        role: 'system',
                        content: 'Conversations: ' + JSON.stringify(convos)
                    });
                }
            }

            analysisAssistantId.prompt.messages = chatmessages;
            analysisAssistantId.prompt.response_format = { type: 'json_object' };

            // Retrieve the assistant's response
            const response = await openai.chat.completions.create(analysisAssistantId.prompt);
            let message = response.choices[0].message.content as string;
            if (message) {
                const jsonString = message.replace(/```json\s*|\s*```/g, '');
                logger.info(`Lead ${leadId} Successfully processed user query to OpenAI from prompt`, message);
                const response = JSON.parse(jsonString);
                return response;
            } else {
                throw new Error('Empty response from OpenAI');
            }
        } else {
            logger.error(`Lead ${leadId} Failed to process from prompt.`);
            return null;
        }
    } catch (error) {
        logger.error(`Lead ${leadId} Failed to process.`);
        return null;
    }
}

export async function getProcessLeadAssistant(companyId: string) {
    const assistantFileDoc = admin.firestore().collection('companies').doc(companyId);
    const assistantFilesSnapshot = await assistantFileDoc.get();
    const { analysis } = assistantFilesSnapshot.data()!;
    return { id: analysis?.assistantId ?? '', prompt: analysis?.prompt ?? '' };
}
async function processLeads() {
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
    const twoHoursAgoTimestamp = Math.floor(twoHoursAgo.getTime() / 1000);

    const leadsCollection = admin.firestore().collection('leads');

    const batchSize = 5; // Number of records per page

    let lastVisibleDoc: FirebaseFirestore.QueryDocumentSnapshot | undefined;
    let hasMoreLeads = true;
    let analysisAssistantId: any;

    while (hasMoreLeads) {
        // Build the query with pagination
        let leadsQuery = leadsCollection
            .where('lead_actions', 'in', ['inquired', 'quoted', 'booked'])
            .where('ai_conversation_summary', '==', '')
            .where('mark_for_deletion', '==', false)
            .where('lastMessageTime', '<', twoHoursAgoTimestamp)
            .orderBy('lastMessageTime', 'desc') // Ensure consistent ordering
            .limit(batchSize);

        if (lastVisibleDoc) {
            leadsQuery = leadsQuery.startAfter(lastVisibleDoc); // Start after the last document of the previous page
        }

        const leadsSnapshot = await leadsQuery.get();

        if (leadsSnapshot.empty ?? leadsSnapshot.docs.length === 0) {
            hasMoreLeads = false; // No more leads to process
            break;
        }

        logger.info(`Processing ${leadsSnapshot.docs.length} leads`);

        const batch = admin.firestore().batch();

        for (const leadDoc of leadsSnapshot.docs) {
            const leadData = leadDoc.data()!;

            analysisAssistantId = await getProcessLeadAssistant(leadData.tenantId);

            const leadDocRef = leadDoc.ref;
            const leadId = leadDoc.id;

            const response = await processLead(leadData, analysisAssistantId, leadId);

            if (response) {
                // Update the lead document with the AI summary
                const phone = response?.phone ?? leadData?.phone ?? '';
                const name = response?.name ?? leadData?.name ?? '';
                const email = response?.email ?? leadData?.email ?? '';
                batch.set(
                    leadDocRef,
                    {
                        phone: leadData?.phone ?? response?.phone ?? '',
                        name: leadData?.name ?? response?.name ?? '',
                        email: leadData?.email ?? response?.email ?? '',
                        address: leadData?.address ?? response?.address ?? '',
                        lead_status: response?.lead_status,
                        is_qualified: response?.is_qualified,
                        ai_conversation_summary: response,
                        follow_up_count: 0,
                        max_followups: response?.follow_up_recommendation?.recurrence_strategy?.max_followups ?? 0,
                        to_followup_date: response?.follow_up_recommendation?.to_followup_date ?? 0,
                        keywords: [...(name ? [name] : []), ...(email ? [email] : []), ...(phone ? [phone] : []), ...(leadData.id ? [leadData.id] : []), ...(leadData.tenantId ? [leadData.tenantId] : [])]
                    },
                    { merge: true }
                );
            } else {
                if (response === null) {
                    // Handle the case where processLead returns null
                    // For example, log an error or set a default value
                    logger.error(`Lead ${leadId} processLead returned null`);

                    batch.set(
                        leadDocRef,
                        {
                            ai_conversation_summary: ''
                        },
                        { merge: true }
                    );
                } else {
                    batch.set(
                        leadDocRef,
                        {
                            ai_conversation_summary: ''
                        },
                        { merge: true }
                    );
                    logger.error(`Lead ${leadId} processLead returned undefined`);
                }
            }
        }

        // Commit the batch updates
        await batch.commit();

        // Update the last visible document for the next iteration
        lastVisibleDoc = leadsSnapshot.docs[leadsSnapshot.docs.length - 1];
    }
}
export async function processLeadsByIds(leadIds: string[]) {
    if (leadIds.length === 0) {
        logger.warn('No lead IDs provided.');
        throw new Error('No lead IDs provided.');
    }

    const batch = admin.firestore().batch();
    const leadsCollection = admin.firestore().collection('leads');

    let analysisAssistantId: any;

    // Loop through each lead ID
    const promises = leadIds.map(async (leadId) => {
        // Use leadId to query for the specific lead
        const leadDocRef = leadsCollection.doc(leadId);
        const leadDoc = await leadDocRef.get();

        if (!leadDoc.exists) {
            logger.warn(`Lead with ID ${leadId} not found.`);
        } else {
            const leadData = leadDoc.data()!;

            analysisAssistantId = await getProcessLeadAssistant(leadData.tenantId);

            const response = await processLead(leadData, analysisAssistantId, leadId);

            if (response) {
                const phone = response?.phone ?? leadData?.phone ?? '';
                const name = response?.name ?? leadData?.name ?? '';
                const email = response?.email ?? leadData?.email ?? '';
                batch.set(
                    leadDocRef,
                    {
                        phone: phone,
                        name: name,
                        email: email,
                        to_followup_date: response?.follow_up_recommendation?.to_followup_date ?? 0,
                        lead_status: response?.lead_status,
                        is_qualified: response?.is_qualified,
                        ai_conversation_summary: response,
                        keywords: [...(name ? [name] : []), ...(email ? [email] : []), ...(phone ? [phone] : []), ...(leadData.id ? [leadData.id] : [])]
                    },
                    { merge: true }
                );
            } else {
                batch.set(
                    leadDocRef,
                    {
                        ai_conversation_summary: ''
                    },
                    { merge: true }
                );
            }
        }
    });

    await Promise.all(promises);
    await batch.commit();
    return true;
}
