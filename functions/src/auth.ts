import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import { ccEmails, getEmailTemplate, getSettingEmailTemplate, noReply, requireAuth, sendEmail, sendGrid<PERSON><PERSON><PERSON>ey } from './utils';
import { logger } from 'firebase-functions';
import * as functions from 'firebase-functions/v1';

export const onSaveUser = onCall({ cors: true }, async (request) => {
    const { data } = request;

    try {
        const createUser = {
            displayName: data.name,
            email: data.email,
            password: data.password,
            emailVerified: true
        };
        if (data.id) {
            data.UpdatedAt = Date.now();
            const user = await admin.auth().updateUser(data.id, createUser);
            admin.firestore().collection('users').doc(user.uid).set(data, { merge: true });
            return user;
        } else {
            data.createdAt = Date.now();
            const user = await admin.auth().createUser(createUser);
            data.id = user.uid;
            admin.firestore().collection('users').doc(user.uid).set(data, { merge: true });
            return user;
        }
    } catch (err) {
        logger.error('Error upserting user.', err);
        throw new HttpsError('unknown', 'Error upserting user.');
    }
});

export const onDeleteUser = onCall({ cors: true }, async (request) => {
    const { data, auth } = request;
    if (!auth) {
        throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);
    try {
        const { userId } = data;
        await admin.auth().deleteUser(userId);
        const ref = admin.firestore().collection('users').doc(userId);
        await ref.delete();
        await admin.firestore().recursiveDelete(ref);
        return true;
    } catch (err) {
        logger.error('Error deleting user.', err);
        throw new HttpsError('unknown', 'Error deleting user.');
    }
});

export const onDeleteUsers = onCall({ cors: true }, async (request) => {
    const { data, auth } = request;
    if (!auth) {
        throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);
    try {
        const { userIds } = data;
        logger.info(`UesrIDS.`, userIds);
        for (const userId of userIds) {
            try {
                await admin.auth().deleteUser(userId);
            } catch (err) {
                logger.error(`Error deleting user ${userId} from auth.`, err);
            }
            try {
                const ref = admin.firestore().collection('users').doc(userId);
                await admin.firestore().recursiveDelete(ref);
            } catch (err) {
                logger.error(`Error deleting user ${userId} from firestore.`, err);
            }
        }

        return true;
    } catch (err) {
        logger.error('Error deleting users.', err);
        throw new HttpsError('unknown', 'Error deleting users.');
    }
});
export const onCreateUserDoc = functions
    .runWith({
        secrets: [sendGridApiKey] // Pass the defined secret here
    })
    .auth.user()
    .onCreate(async (user) => {
        const { email } = user;

        try {
            const message = await getSettingEmailTemplate('account_created');
            await sendEmail({
                to: email,
                from: noReply,
                subject: 'Account Created: Welcome to Liftt!',
                message: await getEmailTemplate(message),
                cc: ccEmails
            });
            const data = {
                id: user.uid,
                name: user.displayName,
                email: user.email,
                updatedAt: Date.now()
            };
            await admin.firestore().collection('users').doc(user.uid).set(data, { merge: true });

            logger.info('User created.');
        } catch (err) {
            logger.error('Error creating user from google.', err);
            throw new HttpsError('unknown', 'Error creating user.');
        }
    });

export const onDeleteUserDoc = functions
    .runWith({
        secrets: [sendGridApiKey] // Pass the defined secret here
    })
    .auth.user()
    .onDelete(async (user) => {
        const { uid, email } = user;
        try {
            const ref = admin.firestore().collection('users').doc(uid);
            await ref.delete();
            // await admin.firestore().recursiveDelete(ref);
            const message = await getSettingEmailTemplate('account_deleted');
            if (email) {
                await sendEmail({
                    to: email,
                    from: noReply,
                    subject: 'Account Deleted',
                    message: await getEmailTemplate(message),
                    cc: ccEmails
                });
            }
            logger.info(`User ${uid} deleted and email sent.`);
        } catch (err) {
            logger.error(`Error deleting user data for ${uid}.`, err);
            throw new HttpsError('unknown', 'Error deleting user data.');
        }
    });
