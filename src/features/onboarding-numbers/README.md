# Communication Services Onboarding

This feature provides a comprehensive onboarding wizard for setting up SMS and call integration using Twilio and Bland.AI services.

## Features

- **Service Configuration**: Select and configure SMS, voice calls, and AI calls
- **Phone Number Management**: Support for Twilio shared numbers (SMS+Voice) and separate Bland.AI numbers
- **Number Purchasing**: Automated purchase from Twilio and Bland.AI with separate number requirements
- **Validation Process**: Real-time validation of service integrations
- **Beautiful UI**: Built with PrimeVue components for excellent UX

## Important: Separate Numbers Required

**Twilio and Bland.AI cannot share the same phone number.** The system enforces:
- **Twilio numbers**: Used for SMS and Voice calls (can be shared between SMS and Voice)
- **Bland.AI numbers**: Used for AI calls (must be separate from <PERSON>wilio numbers)
- **Minimum requirement**: If you enable AI calls along with SMS/Voice, you need at least 2 phone numbers

## Components

### OnboardingWizard
Main wizard component that orchestrates the entire onboarding process.

#### Props
- `autoComplete?: boolean` - Whether to auto-emit complete event (default: true)
- `completionDelay?: number` - Delay before emitting complete event in ms (default: 2000)

#### Events
- `@complete` - Fired when all onboarding steps are completed
- `@step-change` - Fired when user moves between steps
- `@error` - Fired when any error occurs during the process

#### Complete Event Data
```typescript
{
  tenant: Tenant;                                    // Updated tenant object
  serviceConfig: ServiceConfiguration;               // Final service configuration
  purchaseResults: Record<string, NumberPurchaseResponse>; // Purchase results
  validationStatus: Record<string, boolean>;         // Validation status for each service
}
```

```vue
<template>
  <OnboardingWizard
    @complete="onComplete"
    @step-change="onStepChange"
    @error="onError"
  />
</template>

<script setup>
import { OnboardingWizard } from '@/features/onboarding-numbers';
</script>
```

### PhoneNumberInput
Reusable phone number input component with validation.

```vue
<template>
  <PhoneNumberInput
    v-model="phoneNumber"
    label="Phone Number"
    :required="true"
    @validate="onPhoneValidate"
  />
</template>
```

### ServiceConfiguration
Component for selecting and configuring communication services.

### NumberPurchase
Handles the automated purchase of phone numbers from service providers.

### ValidationProcess
Manages the validation of service integrations with real-time updates.

## Usage

1. **Add to Router** (if using as a standalone page):
```typescript
{
  path: '/onboarding-numbers/communication',
  name: 'CommunicationOnboarding',
  component: () => import('@/features/onboarding/OnboardingWizard.vue'),
  meta: { requiresAuth: true }
}
```

2. **Use as Component**:
```vue
<template>
  <div>
    <OnboardingWizard
      :auto-complete="true"
      :completion-delay="2000"
      @complete="onOnboardingComplete"
      @step-change="onStepChange"
      @error="onError"
    />
  </div>
</template>

<script setup>
import { OnboardingWizard } from '@/features/onboarding-numbers';

const onOnboardingComplete = (data) => {
  console.log('Onboarding completed!', data);
  // data contains: tenant, serviceConfig, purchaseResults, validationStatus

  // Redirect to dashboard or next step
  router.push('/dashboard');
};

const onStepChange = (step) => {
  console.log('Step changed to:', step);
  // Track progress or update UI
};

const onError = (error) => {
  console.error('Onboarding error:', error);
  // Handle errors globally
};
</script>
```

## Data Flow

1. **Step 1 - Configuration**: User selects services and configures phone numbers
   - If only SMS/Voice: Can use shared Twilio number
   - If AI calls enabled: Requires separate numbers for Twilio and Bland.AI
2. **Step 2 - Purchase**: System purchases numbers from appropriate providers
   - Twilio: SMS and Voice calls
   - Bland.AI: AI calls (separate number required)
3. **Step 3 - Validation**: System validates integrations and updates tenant

## Tenant Data Structure

The feature updates the tenant with the following structure:

```typescript
{
  sms: {
    defaultNumber: "+44...",
    numbers: ["+44..."],
    validated: false
  },
  call: {
    defaultNumber: "+44...",
    numbers: ["+44..."],
    validated: false
  },
  ai_call: {
    defaultNumber: "+44...",
    numbers: ["+44..."],
    validated: false
  },
  lead_sources: {
    sms: { status: 'enabled' },
    call: { status: 'enabled' },
    ai_call: { status: 'enabled' }
  }
}
```

## Error Handling

- **Purchase Failures**: Retry mechanism with detailed error messages
- **Validation Timeouts**: 5-minute timeout with support contact information
- **Network Issues**: Graceful error handling with user feedback

## Dependencies

- VueTelInput for phone number input
- PrimeVue for UI components
- Tenant store for data management
- Auth store for user authentication

## Styling

The feature uses PrimeVue's design system and supports both light and dark themes. Custom CSS variables ensure consistency with the application's theme.
