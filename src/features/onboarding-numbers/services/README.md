# Onboarding Services

This directory contains services that handle the business logic for the onboarding feature.

## NumberConfigService

The `NumberConfigService` serves as a middleware between the onboarding components and the auth/tenant stores. It centralizes all store interactions and provides a clean API for managing phone number configuration.

### Features

- **Centralized Store Management**: Handles both authStore and tenantStore interactions
- **Automatic Initialization**: Loads user data and tenant information automatically
- **Real-time Updates**: Subscribes to tenant changes and updates service configuration
- **Error Handling**: Provides comprehensive error handling and state management
- **Type Safety**: Fully typed with TypeScript interfaces

### Usage

```typescript
import { useNumberConfigService } from './services/numberConfigService';

// In your component
const numberConfigService = useNumberConfigService();

// Initialize the service
await numberConfigService.initialize();

// Access reactive state
const isReady = numberConfigService.isReady;
const tenant = numberConfigService.tenant;
const serviceConfig = numberConfigService.serviceConfig;

// Update service configuration
numberConfigService.updateServiceConfig(newConfig);

// Update tenant with purchased numbers
await numberConfigService.updateTenantWithNumbers(config, purchaseResults);
```

### State Management

The service maintains the following state:

```typescript
interface NumberConfigState {
    isLoading: boolean;
    error: string | null;
    userData: any;
    tenant: Tenant | null;
    tenantId: string | null;
    serviceConfig: ServiceConfiguration;
}
```

### Methods

#### Core Methods
- `initialize()`: Loads user data, tenant data, and sets up subscriptions
- `updateServiceConfig(config)`: Updates the service configuration
- `updateTenantWithNumbers(config, results)`: Updates tenant with purchased phone numbers
- `refresh()`: Reloads tenant data
- `destroy()`: Cleans up subscriptions and resets state

#### Utility Methods
- `isServiceEnabledInLeadSources(service)`: Checks if a service is enabled in lead sources
- `getValidationStatus()`: Returns validation status for all services
- `clearError()`: Clears the current error state

### Computed Properties
- `isReady`: True when service is initialized and data is loaded
- `hasError`: True when there's an error
- `tenantId`: Current tenant ID
- `tenant`: Current tenant object
- `serviceConfig`: Current service configuration

### Error Handling

The service provides comprehensive error handling:

```typescript
try {
    await numberConfigService.initialize();
} catch (error) {
    // Handle initialization errors
    console.error('Service initialization failed:', error);
}

// Check for errors reactively
if (numberConfigService.hasError.value) {
    console.error('Service error:', numberConfigService.state.value.error);
}
```

### Integration with OnboardingWizard

The `OnboardingWizard` component uses this service to:

1. **Initialize**: Load user and tenant data on component mount
2. **Configure**: Update service configuration as user makes selections
3. **Purchase**: Update tenant with purchased phone numbers
4. **Validate**: Provide tenant data for validation process
5. **Cleanup**: Destroy service on component unmount

### Benefits

1. **Separation of Concerns**: Business logic is separated from UI components
2. **Reusability**: Service can be used by other components that need similar functionality
3. **Maintainability**: Centralized store interactions make the code easier to maintain
4. **Testing**: Service can be easily unit tested in isolation
5. **Type Safety**: Full TypeScript support with proper interfaces

### Example: Using in Other Components

```typescript
// In any component that needs auth + tenant functionality
import { useNumberConfigService } from '@/features/onboarding-numbers/services/numberConfigService';

export default {
    setup() {
        const service = useNumberConfigService();
        
        onMounted(async () => {
            await service.initialize();
        });
        
        onUnmounted(() => {
            service.destroy();
        });
        
        return {
            isReady: service.isReady,
            tenant: service.tenant,
            serviceConfig: service.serviceConfig
        };
    }
};
```

## NumberPurchaseService

Handles the actual purchasing of phone numbers from Twilio and Bland.AI providers.

### Features
- **Provider Abstraction**: Unified interface for different providers
- **Type Safety**: Proper TypeScript interfaces for all API interactions
- **Error Handling**: Comprehensive error handling for purchase failures
- **Search Functionality**: Search for available numbers before purchasing

### Usage

```typescript
import { NumberPurchaseService } from './services/numberPurchaseService';

// Search for available numbers
const numbers = await NumberPurchaseService.searchTwilioNumbers({
    countryCode: 'GB',
    smsEnabled: true,
    voiceEnabled: true
});

// Purchase a number
const result = await NumberPurchaseService.purchaseNumbers({
    phoneNumber: '+441234567890',
    service: 'twilio',
    countryCode: 'GB'
});
```

This service architecture provides a clean, maintainable, and type-safe way to handle complex store interactions in the onboarding feature.
