<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted, onUnmounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useTenantStore } from '@/entities/tenant';
import { validateFeatures } from '@/shared/api/tenant';
import type { ServiceConfiguration, ValidationStatus, NumberPurchaseResponse } from '../types';
import type { Tenant } from '@/entities/tenant/model';

// Props
const props = defineProps<{
    serviceConfig: ServiceConfiguration;
    purchaseResults: Record<string, NumberPurchaseResponse>;
    tenantId: string;
    disabled?: boolean;
}>();

// Emits
const emit = defineEmits<{
    'validation-complete': [];
    'validation-error': [error: string];
}>();

// Local state
const toast = useToast();
const tenantStore = useTenantStore();
const validationStatus = ref<ValidationStatus>({});
const isValidating = ref<Record<string, boolean>>({});
const validationErrors = ref<Record<string, string>>({});
const validationTimeouts = ref<Record<string, NodeJS.Timeout>>({});
const tenantUnsubscribe = ref<(() => void) | null>(null);

// Computed
const servicesToValidate = computed(() => {
    const services: Array<{ key: 'sms' | 'call' | 'ai_call'; label: string; icon: string }> = [];
    
    if (props.serviceConfig.sms.enabled) {
        services.push({ key: 'sms', label: 'SMS', icon: 'pi-comment' });
    }
    if (props.serviceConfig.call.enabled) {
        services.push({ key: 'call', label: 'Voice Calls', icon: 'pi-phone' });
    }
    if (props.serviceConfig.ai_call.enabled) {
        services.push({ key: 'ai_call', label: 'AI Calls', icon: 'pi-android' });
    }
    
    return services;
});

const allValidated = computed(() => {
    return servicesToValidate.value.every(service => 
        validationStatus.value[service.key] === true
    );
});

const hasValidationErrors = computed(() => {
    return Object.keys(validationErrors.value).length > 0;
});

// Methods
const validateService = async (service: 'sms' | 'call' | 'ai_call') => {
    if (isValidating.value[service]) return;
    
    try {
        isValidating.value[service] = true;
        validationErrors.value[service] = '';
        
        // Call validation API
        await validateFeatures({
            feature: service,
            toValidate: {
                tenantId: props.tenantId,
                service: service
            }
        });
        
        // Set timeout for validation (5 minutes)
        validationTimeouts.value[service] = setTimeout(() => {
            if (!validationStatus.value[service]) {
                validationErrors.value[service] = 'Validation timeout. <NAME_EMAIL> for assistance.';
                isValidating.value[service] = false;
                
                toast.add({
                    severity: 'error',
                    summary: 'Validation Timeout',
                    detail: `${service.toUpperCase()} validation timed out after 5 minutes. Please contact support.`,
                    life: 10000
                });
            }
        }, 5 * 60 * 1000); // 5 minutes
        
        toast.add({
            severity: 'info',
            summary: 'Validation Started',
            detail: `${service.toUpperCase()} validation process initiated. Please wait...`,
            life: 3000
        });
        
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        validationErrors.value[service] = errorMessage;
        isValidating.value[service] = false;
        
        toast.add({
            severity: 'error',
            summary: 'Validation Error',
            detail: `Failed to start ${service} validation: ${errorMessage}`,
            life: 5000
        });
    }
};

const onTenantUpdate = (tenant: Tenant | null) => {
    if (!tenant) return;
    
    // Check validation status for each service
    servicesToValidate.value.forEach(service => {
        const serviceData = tenant[service.key];
        const isValidated = serviceData?.validated === true;
        
        if (isValidated && !validationStatus.value[service.key]) {
            // Service just became validated
            validationStatus.value[service.key] = true;
            isValidating.value[service.key] = false;
            
            // Clear timeout
            if (validationTimeouts.value[service.key]) {
                clearTimeout(validationTimeouts.value[service.key]);
                delete validationTimeouts.value[service.key];
            }
            
            // Clear error
            delete validationErrors.value[service.key];
            
            toast.add({
                severity: 'success',
                summary: 'Validation Complete',
                detail: `${service.label} integration validated successfully!`,
                life: 3000
            });
            
            // Check if all services are validated
            if (allValidated.value) {
                setTimeout(() => {
                    emit('validation-complete');
                }, 1000);
            }
        }
    });
};

const retryValidation = (service: 'sms' | 'call' | 'ai_call') => {
    // Clear previous error and timeout
    delete validationErrors.value[service];
    if (validationTimeouts.value[service]) {
        clearTimeout(validationTimeouts.value[service]);
        delete validationTimeouts.value[service];
    }
    
    // Retry validation
    validateService(service);
};

const contactSupport = () => {
    window.open('mailto:<EMAIL>?subject=CRM Integration Validation Issue', '_blank');
};

// Lifecycle
onMounted(() => {
    // Subscribe to tenant changes
    tenantUnsubscribe.value = tenantStore.subscribeToTenantChanges(
        props.tenantId,
        onTenantUpdate
    );
    
    // Auto-start validation for all services
    servicesToValidate.value.forEach(service => {
        validateService(service.key);
    });
});

onUnmounted(() => {
    // Clean up subscription
    if (tenantUnsubscribe.value) {
        tenantUnsubscribe.value();
    }
    
    // Clear all timeouts
    Object.values(validationTimeouts.value).forEach(timeout => {
        clearTimeout(timeout);
    });
});
</script>

<template>
    <div class="validation-process">
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
                Validate Service Integration
            </h3>
            <p class="text-surface-600 dark:text-surface-400">
                We're validating your service integrations. This process may take a few minutes.
            </p>
        </div>

        <!-- Validation Progress -->
        <Card class="mb-6">
            <template #title>
                <div class="flex items-center gap-2">
                    <i class="pi pi-shield text-primary"></i>
                    Integration Validation
                </div>
            </template>
            <template #content>
                <div class="space-y-4">
                    <div v-for="service in servicesToValidate" :key="service.key" class="validation-item">
                        <div class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="validation-icon">
                                    <i :class="`pi ${service.icon} text-primary`"></i>
                                </div>
                                <div>
                                    <div class="font-medium">{{ service.label }} Integration</div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">
                                        Validating service connectivity and configuration
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center gap-2">
                                <!-- Validating State -->
                                <div v-if="isValidating[service.key] && !validationStatus[service.key]" class="flex items-center gap-2">
                                    <ProgressSpinner style="width: 20px; height: 20px" strokeWidth="4" />
                                    <span class="text-sm text-blue-600">Validating...</span>
                                </div>
                                
                                <!-- Success State -->
                                <div v-else-if="validationStatus[service.key]" class="flex items-center gap-2 text-green-600">
                                    <i class="pi pi-check-circle"></i>
                                    <span class="text-sm font-medium">Validated</span>
                                </div>
                                
                                <!-- Error State -->
                                <div v-else-if="validationErrors[service.key]" class="flex items-center gap-2">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <i class="pi pi-times-circle"></i>
                                        <span class="text-sm font-medium">Failed</span>
                                    </div>
                                    <Button
                                        label="Retry"
                                        icon="pi pi-refresh"
                                        size="small"
                                        outlined
                                        @click="retryValidation(service.key)"
                                        :disabled="isValidating[service.key]"
                                    />
                                </div>
                                
                                <!-- Pending State -->
                                <div v-else class="flex items-center gap-2 text-surface-500">
                                    <Button
                                        :label="`Validate ${service.label}`"
                                        icon="pi pi-play"
                                        size="small"
                                        @click="validateService(service.key)"
                                        :disabled="disabled"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Error Details -->
                        <div v-if="validationErrors[service.key]" class="mt-2 ml-4">
                            <small class="text-red-500">
                                <i class="pi pi-exclamation-triangle mr-1"></i>
                                {{ validationErrors[service.key] }}
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Overall Status -->
                <div v-if="servicesToValidate.length > 0" class="mt-6 pt-4 border-t border-surface-200 dark:border-surface-700">
                    <div v-if="allValidated" class="flex items-center gap-2 text-green-600">
                        <i class="pi pi-check-circle text-lg"></i>
                        <span class="font-medium">All services validated successfully!</span>
                    </div>
                    <div v-else-if="hasValidationErrors" class="flex items-center gap-2 text-orange-600">
                        <i class="pi pi-exclamation-triangle text-lg"></i>
                        <span class="font-medium">Some validations failed. Please retry or contact support.</span>
                    </div>
                    <div v-else class="flex items-center gap-2 text-blue-600">
                        <ProgressSpinner style="width: 20px; height: 20px" strokeWidth="4" />
                        <span class="font-medium">Validating integrations...</span>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Validation Summary -->
        <Card v-if="servicesToValidate.length > 0" class="mb-6">
            <template #title>
                <div class="flex items-center gap-2">
                    <i class="pi pi-chart-bar text-primary"></i>
                    Validation Summary
                </div>
            </template>
            <template #content>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">
                            {{ Object.values(validationStatus).filter(Boolean).length }}
                        </div>
                        <div class="text-sm text-green-700 dark:text-green-300">Validated</div>
                    </div>
                    <div class="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <div class="text-2xl font-bold text-red-600">
                            {{ Object.keys(validationErrors).length }}
                        </div>
                        <div class="text-sm text-red-700 dark:text-red-300">Failed</div>
                    </div>
                    <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">
                            {{ servicesToValidate.length }}
                        </div>
                        <div class="text-sm text-blue-700 dark:text-blue-300">Total</div>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Help Section -->
        <Card v-if="hasValidationErrors" class="mb-6">
            <template #title>
                <div class="flex items-center gap-2">
                    <i class="pi pi-question-circle text-primary"></i>
                    Need Help?
                </div>
            </template>
            <template #content>
                <div class="text-center">
                    <p class="text-surface-600 dark:text-surface-400 mb-4">
                        If validation continues to fail, please contact our support team for assistance.
                    </p>
                    <Button
                        label="Contact Support"
                        icon="pi pi-envelope"
                        @click="contactSupport"
                        class="px-6 py-3"
                    />
                </div>
            </template>
        </Card>

        <!-- Completion Message -->
        <div v-if="allValidated" class="text-center">
            <div class="mb-4">
                <i class="pi pi-check-circle text-6xl text-green-500"></i>
            </div>
            <h4 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
                Integration Complete!
            </h4>
            <p class="text-surface-600 dark:text-surface-400">
                Your communication services have been successfully configured and validated.
                You can now start using SMS, voice calls, and AI calls in your CRM.
            </p>
        </div>
    </div>
</template>

<style scoped>
.validation-item {
    transition: all 0.3s ease;
}

.validation-item:hover {
    transform: translateY(-1px);
}

.validation-icon {
    @apply w-8 h-8 flex items-center justify-center rounded-full bg-surface-100 dark:bg-surface-800;
}
</style>
