<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { NumberPurchaseService } from '../services/numberPurchaseService';
import type { ServiceConfiguration, NumberPurchaseResponse } from '../types';

// Props
const props = defineProps<{
    serviceConfig: ServiceConfiguration;
    disabled?: boolean;
}>();

// Emits
const emit = defineEmits<{
    'purchase-complete': [results: Record<string, NumberPurchaseResponse>];
    'next': [];
}>();

// Local state
const toast = useToast();
const isLoading = ref(false);
const purchaseResults = ref<Record<string, NumberPurchaseResponse>>({});
const currentPurchase = ref<string>('');

// Computed
const numbersToPurchase = computed(() => {
    const numbers: Array<{ service: string; number: string; provider: 'twilio' | 'bland' }> = [];

    // Legacy shared number (only when AI calls not enabled)
    if (props.serviceConfig.useSharedNumber && props.serviceConfig.sharedNumber && !props.serviceConfig.ai_call.enabled) {
        numbers.push({
            service: 'twilio_shared',
            number: props.serviceConfig.sharedNumber,
            provider: 'twilio'
        });
    }
    // Twilio shared number (SMS + Voice)
    else if (props.serviceConfig.useTwilioSharedNumber && props.serviceConfig.twilioSharedNumber) {
        numbers.push({
            service: 'twilio_shared',
            number: props.serviceConfig.twilioSharedNumber,
            provider: 'twilio'
        });
    }
    // Individual Twilio numbers
    else {
        if (props.serviceConfig.sms.enabled && props.serviceConfig.sms.number) {
            numbers.push({
                service: 'sms',
                number: props.serviceConfig.sms.number,
                provider: 'twilio'
            });
        }
        if (props.serviceConfig.call.enabled && props.serviceConfig.call.number) {
            numbers.push({
                service: 'call',
                number: props.serviceConfig.call.number,
                provider: 'twilio'
            });
        }
    }

    // AI Call number (always separate)
    if (props.serviceConfig.ai_call.enabled && props.serviceConfig.ai_call.number) {
        numbers.push({
            service: 'ai_call',
            number: props.serviceConfig.ai_call.number,
            provider: 'bland'
        });
    }

    return numbers;
});

const allPurchased = computed(() => {
    return numbersToPurchase.value.every(item =>
        purchaseResults.value[item.service]?.success
    );
});

const hasErrors = computed(() => {
    return Object.values(purchaseResults.value).some(result => !result.success);
});

// Methods
const purchaseNumber = async (service: string, number: string, provider: 'twilio' | 'bland') => {
    try {
        currentPurchase.value = service;

        const response = await NumberPurchaseService.purchaseNumbers({
            phoneNumber: number,
            service: provider,
            countryCode: 'GB',
            friendlyName: `CRM ${service.toUpperCase()} Number`
        });

        purchaseResults.value[service] = response;

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Purchase Successful',
                detail: `${service.toUpperCase()} number ${number} purchased successfully`,
                life: 3000
            });
        } else {
            toast.add({
                severity: 'error',
                summary: 'Purchase Failed',
                detail: response.error || `Failed to purchase ${service} number`,
                life: 5000
            });
        }
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        purchaseResults.value[service] = {
            success: false,
            error: errorMessage
        };

        toast.add({
            severity: 'error',
            summary: 'Purchase Error',
            detail: errorMessage,
            life: 5000
        });
    } finally {
        currentPurchase.value = '';
    }
};

const purchaseAllNumbers = async () => {
    if (isLoading.value) return;

    isLoading.value = true;
    purchaseResults.value = {};

    try {
        // Purchase numbers sequentially to avoid rate limits
        for (const item of numbersToPurchase.value) {
            await purchaseNumber(item.service, item.number, item.provider);

            // Small delay between purchases
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Emit results
        emit('purchase-complete', purchaseResults.value);

        if (allPurchased.value) {
            toast.add({
                severity: 'success',
                summary: 'All Numbers Purchased',
                detail: 'All phone numbers have been purchased successfully',
                life: 3000
            });
        }
    } catch (error) {
        toast.add({
            severity: 'error',
            summary: 'Purchase Error',
            detail: 'An error occurred during the purchase process',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
};

const retryPurchase = async (service: string) => {
    const item = numbersToPurchase.value.find(n => n.service === service);
    if (item) {
        await purchaseNumber(item.service, item.number, item.provider);
    }
};

const getServiceDisplayName = (service: string) => {
    switch (service) {
        case 'twilio_shared':
            return 'Twilio Shared';
        case 'sms':
            return 'SMS';
        case 'call':
            return 'Voice Call';
        case 'ai_call':
            return 'AI Call';
        default:
            return service.toUpperCase();
    }
};

const onNext = () => {
    if (allPurchased.value) {
        emit('next');
    }
};

// Auto-start purchase when component mounts
onMounted(() => {
    if (numbersToPurchase.value.length > 0) {
        purchaseAllNumbers();
    }
});
</script>

<template>
    <div class="number-purchase">
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
                Purchase Phone Numbers
            </h3>
            <p class="text-surface-600 dark:text-surface-400">
                We're purchasing your phone numbers from our service providers. This may take a few moments.
            </p>
        </div>

        <!-- Purchase Progress -->
        <Card class="mb-6">
            <template #title>
                <div class="flex items-center gap-2">
                    <i class="pi pi-shopping-cart text-primary"></i>
                    Purchase Progress
                </div>
            </template>
            <template #content>
                <div class="space-y-4">
                    <div v-for="item in numbersToPurchase" :key="item.service" class="purchase-item">
                        <div class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="purchase-icon">
                                    <i v-if="item.provider === 'twilio'" class="pi pi-phone text-blue-500"></i>
                                    <i v-else class="pi pi-android text-purple-500"></i>
                                </div>
                                <div>
                                    <div class="font-medium">
                                        {{ getServiceDisplayName(item.service) }} - {{ item.number }}
                                    </div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">
                                        Provider: {{ item.provider === 'twilio' ? 'Twilio' : 'Bland AI' }}
                                        <span v-if="item.service === 'twilio_shared'" class="ml-2 text-blue-600">(SMS + Voice)</span>
                                        <span v-else-if="item.service === 'ai_call'" class="ml-2 text-purple-600">(AI Calls Only)</span>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center gap-2">
                                <!-- Loading State -->
                                <div v-if="currentPurchase === item.service" class="flex items-center gap-2">
                                    <ProgressSpinner style="width: 20px; height: 20px" strokeWidth="4" />
                                    <span class="text-sm">Purchasing...</span>
                                </div>

                                <!-- Success State -->
                                <div v-else-if="purchaseResults[item.service]?.success" class="flex items-center gap-2 text-green-600">
                                    <i class="pi pi-check-circle"></i>
                                    <span class="text-sm font-medium">Purchased</span>
                                </div>

                                <!-- Error State -->
                                <div v-else-if="purchaseResults[item.service] && !purchaseResults[item.service].success" class="flex items-center gap-2">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <i class="pi pi-times-circle"></i>
                                        <span class="text-sm font-medium">Failed</span>
                                    </div>
                                    <Button
                                        label="Retry"
                                        icon="pi pi-refresh"
                                        size="small"
                                        outlined
                                        @click="retryPurchase(item.service)"
                                        :disabled="isLoading"
                                    />
                                </div>

                                <!-- Pending State -->
                                <div v-else class="flex items-center gap-2 text-surface-500">
                                    <i class="pi pi-clock"></i>
                                    <span class="text-sm">Pending</span>
                                </div>
                            </div>
                        </div>

                        <!-- Error Details -->
                        <div v-if="purchaseResults[item.service] && !purchaseResults[item.service].success" class="mt-2 ml-4">
                            <small class="text-red-500">
                                <i class="pi pi-exclamation-triangle mr-1"></i>
                                {{ purchaseResults[item.service].error }}
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Overall Status -->
                <div v-if="Object.keys(purchaseResults).length > 0" class="mt-6 pt-4 border-t border-surface-200 dark:border-surface-700">
                    <div v-if="allPurchased" class="flex items-center gap-2 text-green-600">
                        <i class="pi pi-check-circle text-lg"></i>
                        <span class="font-medium">All numbers purchased successfully!</span>
                    </div>
                    <div v-else-if="hasErrors" class="flex items-center gap-2 text-orange-600">
                        <i class="pi pi-exclamation-triangle text-lg"></i>
                        <span class="font-medium">Some purchases failed. Please retry or contact support.</span>
                    </div>
                    <div v-else-if="isLoading" class="flex items-center gap-2 text-blue-600">
                        <ProgressSpinner style="width: 20px; height: 20px" strokeWidth="4" />
                        <span class="font-medium">Purchasing numbers...</span>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Purchase Summary -->
        <Card v-if="Object.keys(purchaseResults).length > 0" class="mb-6">
            <template #title>
                <div class="flex items-center gap-2">
                    <i class="pi pi-list text-primary"></i>
                    Purchase Summary
                </div>
            </template>
            <template #content>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">
                            {{ Object.values(purchaseResults).filter(r => r.success).length }}
                        </div>
                        <div class="text-sm text-green-700 dark:text-green-300">Successful</div>
                    </div>
                    <div class="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <div class="text-2xl font-bold text-red-600">
                            {{ Object.values(purchaseResults).filter(r => !r.success).length }}
                        </div>
                        <div class="text-sm text-red-700 dark:text-red-300">Failed</div>
                    </div>
                    <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">
                            {{ numbersToPurchase.length }}
                        </div>
                        <div class="text-sm text-blue-700 dark:text-blue-300">Total</div>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Action Buttons -->
        <div class="flex justify-between">
            <Button
                label="Retry All Failed"
                icon="pi pi-refresh"
                outlined
                :disabled="!hasErrors || isLoading"
                @click="purchaseAllNumbers"
            />

            <Button
                label="Continue to Validation"
                icon="pi pi-arrow-right"
                iconPos="right"
                :disabled="!allPurchased || isLoading"
                @click="onNext"
                class="px-6 py-3"
            />
        </div>
    </div>
</template>

<style scoped>
.purchase-item {
    transition: all 0.3s ease;
}

.purchase-item:hover {
    transform: translateY(-1px);
}

.purchase-icon {
    @apply w-8 h-8 flex items-center justify-center rounded-full bg-surface-100 dark:bg-surface-800;
}
</style>
