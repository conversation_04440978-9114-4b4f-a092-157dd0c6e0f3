<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import { VueTelInput } from 'vue-tel-input';
import 'vue-tel-input/vue-tel-input.css';
import type { PhoneNumberInput } from '../types';

// Props
const props = defineProps<{
    modelValue: string;
    label?: string;
    placeholder?: string;
    required?: boolean;
    error?: string;
    disabled?: boolean;
}>();

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: string];
    'validate': [phoneObject: PhoneNumberInput];
}>();

// Local state
const phoneNumber = ref(props.modelValue);
const isPhoneValid = ref(false);
const phoneError = ref(props.error || '');

// Computed
const hasError = computed(() => !!phoneError.value);

// Methods
const onPhoneValidate = (phoneObject: { valid: boolean; country?: { iso2: string; name: string } }) => {
    isPhoneValid.value = phoneObject?.valid || false;
    
    if (phoneNumber.value && !isPhoneValid.value) {
        phoneError.value = 'Please enter a valid phone number for the selected country.';
    } else {
        phoneError.value = '';
    }

    // Emit validation result
    emit('validate', {
        number: phoneNumber.value,
        isValid: isPhoneValid.value,
        country: phoneObject.country
    });
};

const onPhoneInput = (value: string) => {
    phoneNumber.value = value;
    emit('update:modelValue', value);
    
    // Clear error when user starts typing
    if (phoneError.value && value) {
        phoneError.value = '';
    }
};

// Watch for external error changes
const updateError = (newError: string) => {
    phoneError.value = newError;
};

// Expose methods for parent component
defineExpose({
    updateError,
    isValid: computed(() => isPhoneValid.value),
    hasError: computed(() => hasError.value)
});
</script>

<template>
    <div class="field">
        <label v-if="label" :for="`phone-${$attrs.id || 'input'}`" class="block text-surface-900 dark:text-surface-0 text-lg mb-2">
            {{ label }}
            <span v-if="required" class="text-red-500 ml-1">*</span>
        </label>
        
        <vue-tel-input
            :id="`phone-${$attrs.id || 'input'}`"
            v-model="phoneNumber"
            :onlyCountries="['GB', 'AU', 'US', 'CA']"
            :required="required"
            :disabled="disabled"
            mode="international"
            @validate="onPhoneValidate"
            @input="onPhoneInput"
            :inputOptions="{
                placeholder: placeholder || 'Enter phone number',
                maxlength: 25,
                type: 'tel'
            }"
            :dropdownOptions="{
                showDialCodeInSelection: true,
                showFlags: true,
                showSearchBox: true
            }"
            :validCharactersOnly="true"
            :autoDefaultCountry="false"
            :ignoredCountries="[]"
            :class="['vue-tel-input-custom w-full', { 
                error: hasError,
                'opacity-60 cursor-not-allowed': disabled 
            }]"
        />
        
        <small v-if="hasError" class="text-red-500 mt-1 block">
            {{ phoneError }}
        </small>
        
        <small v-else-if="!hasError && phoneNumber && isPhoneValid" class="text-green-500 mt-1 block">
            <i class="pi pi-check mr-1"></i>
            Valid phone number
        </small>
    </div>
</template>

<style scoped>
/* Vue Tel Input Custom Styling for Light and Dark Mode */
:deep(.vue-tel-input-custom) {
    width: 100%;
}

:deep(.vue-tel-input-custom .vti__dropdown) {
    background-color: var(--p-inputtext-background);
    border: 1px solid var(--p-inputtext-border-color);
    border-radius: var(--p-inputtext-border-radius);
    color: var(--p-inputtext-color);
}

:deep(.vue-tel-input-custom .vti__dropdown:hover) {
    border-color: var(--p-inputtext-hover-border-color);
}

:deep(.vue-tel-input-custom .vti__dropdown:focus-within) {
    border-color: var(--p-inputtext-focus-border-color);
    box-shadow: var(--p-inputtext-focus-ring);
}

:deep(.vue-tel-input-custom .vti__input) {
    background-color: transparent;
    border: none;
    color: var(--p-inputtext-color);
    font-size: var(--p-inputtext-font-size);
    padding: var(--p-inputtext-padding-y) var(--p-inputtext-padding-x);
    outline: none;
    width: 100%;
}

:deep(.vue-tel-input-custom .vti__input::placeholder) {
    color: var(--p-inputtext-placeholder-color);
}

:deep(.vue-tel-input-custom .vti__selection) {
    background-color: transparent;
    border-right: 1px solid var(--p-inputtext-border-color);
    color: var(--p-inputtext-color);
    padding: var(--p-inputtext-padding-y) 8px;
}

:deep(.vue-tel-input-custom .vti__flag) {
    margin-right: 8px;
}

:deep(.vue-tel-input-custom .vti__dropdown-list) {
    background-color: var(--p-inputtext-background);
    border: 1px solid var(--p-inputtext-border-color);
    border-radius: var(--p-inputtext-border-radius);
    box-shadow: var(--p-overlay-shadow);
    z-index: 1000;
}

:deep(.vue-tel-input-custom .vti__dropdown-item) {
    color: var(--p-inputtext-color);
    padding: 8px 12px;
}

:deep(.vue-tel-input-custom .vti__dropdown-item:hover) {
    background-color: var(--p-list-option-background-hover);
}

:deep(.vue-tel-input-custom .vti__dropdown-item.highlighted) {
    background-color: var(--p-list-option-background-selected);
}

:deep(.vue-tel-input-custom .vti__search_box) {
    background-color: var(--p-inputtext-background);
    border: 1px solid var(--p-inputtext-border-color);
    color: var(--p-inputtext-color);
    padding: 8px 12px;
    margin: 8px;
    border-radius: var(--p-inputtext-border-radius);
}

/* Error state */
:deep(.vue-tel-input-custom.error .vti__dropdown) {
    border-color: var(--p-inputtext-invalid-border-color);
}

:deep(.vue-tel-input-custom.error .vti__dropdown:focus-within) {
    border-color: var(--p-inputtext-invalid-border-color);
    box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.2);
}

/* Disabled state */
:deep(.vue-tel-input-custom .vti__dropdown:disabled) {
    opacity: 0.6;
    cursor: not-allowed;
}

:deep(.vue-tel-input-custom .vti__input:disabled) {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>
