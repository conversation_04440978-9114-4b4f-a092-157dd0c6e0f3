// Onboarding feature types

export interface PhoneNumberInput {
    number: string;
    isValid: boolean;
    country?: {
        iso2: string;
        name: string;
    };
}

export interface ServiceConfiguration {
    sms: {
        enabled: boolean;
        number?: string;
    };
    call: {
        enabled: boolean;
        number?: string;
    };
    ai_call: {
        enabled: boolean;
        number?: string;
    };
    // Legacy shared number (only when AI calls not enabled)
    useSharedNumber: boolean;
    sharedNumber?: string;
    // Twilio shared number (SMS + Voice only)
    useTwilioSharedNumber: boolean;
    twilioSharedNumber?: string;
}

export interface NumberPurchaseRequest {
    phoneNumber: string;
    service: 'twilio' | 'bland';
    countryCode?: string;
    friendlyName?: string;
}

export interface NumberPurchaseResponse {
    success: boolean;
    phoneNumber?: string;
    sid?: string;
    inbound_id?: string;
    message?: string;
    error?: string;
}

export interface ValidationStatus {
    sms?: boolean;
    call?: boolean;
    ai_call?: boolean;
}

export interface OnboardingStep {
    id: string;
    title: string;
    description: string;
    completed: boolean;
    active: boolean;
}

export const ONBOARDING_STEPS: OnboardingStep[] = [
    {
        id: 'configure',
        title: 'Configure Services',
        description: 'Select and configure your communication services',
        completed: false,
        active: true
    },
    {
        id: 'purchase',
        title: 'Purchase Numbers',
        description: 'Buy phone numbers for your services',
        completed: false,
        active: false
    },
    {
        id: 'validate',
        title: 'Validate Integration',
        description: 'Verify your service integrations',
        completed: false,
        active: false
    }
];

export interface OnboardingState {
    currentStep: number;
    steps: OnboardingStep[];
    serviceConfig: ServiceConfiguration;
    validationStatus: ValidationStatus;
    isLoading: boolean;
    error: string | null;
}
