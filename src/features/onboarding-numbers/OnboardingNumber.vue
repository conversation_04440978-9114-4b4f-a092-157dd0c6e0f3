<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useNumberConfigService } from './services/numberConfigService';
import ServiceConfiguration from './components/ServiceConfiguration.vue';
import NumberPurchase from './components/NumberPurchase.vue';
import ValidationProcess from './components/ValidationProcess.vue';
import type { OnboardingState, NumberPurchaseResponse } from './types';
import { ONBOARDING_STEPS } from './types';

// Props
const props = defineProps<{
    autoComplete?: boolean; // Whether to auto-emit complete event (default: true)
    completionDelay?: number; // Delay before emitting complete event in ms (default: 2000)
}>();

// Emits
const emit = defineEmits<{
    complete: [
        data: {
            tenant: any;
            serviceConfig: any;
            purchaseResults: Record<string, NumberPurchaseResponse>;
            validationStatus: Record<string, boolean>;
        }
    ];
    'step-change': [step: number];
    error: [error: string];
}>();

// Services
const toast = useToast();
const numberConfigService = useNumberConfigService();

// Local state
const onboardingState = ref<OnboardingState>({
    currentStep: 0,
    steps: [...ONBOARDING_STEPS],
    serviceConfig: {
        sms: { enabled: false },
        call: { enabled: false },
        ai_call: { enabled: false },
        useSharedNumber: false,
        useTwilioSharedNumber: false
    },
    validationStatus: {},
    isLoading: false,
    error: null
});

const purchaseResults = ref<Record<string, NumberPurchaseResponse>>({});

// Computed
const currentStep = computed(() => onboardingState.value.steps[onboardingState.value.currentStep]);
const isFirstStep = computed(() => onboardingState.value.currentStep === 0);
const isLastStep = computed(() => onboardingState.value.currentStep === onboardingState.value.steps.length - 1);

// Use service computed properties
const isServiceReady = computed(() => numberConfigService.isReady.value);
const serviceError = computed(() => numberConfigService.hasError.value);
const tenantId = computed(() => numberConfigService.tenantId.value);
const tenant = computed(() => numberConfigService.tenant.value);
const serviceConfig = computed(() => numberConfigService.serviceConfig.value);

// Methods
const initializeService = async () => {
    try {
        onboardingState.value.isLoading = true;
        onboardingState.value.error = null;

        await numberConfigService.initialize();

        // Sync service config with onboarding-numbers state
        onboardingState.value.serviceConfig = { ...serviceConfig.value };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize service';
        onboardingState.value.error = errorMessage;

        // Emit error event
        emit('error', errorMessage);

        toast.add({
            severity: 'error',
            summary: 'Initialization Error',
            detail: errorMessage,
            life: 5000
        });
    } finally {
        onboardingState.value.isLoading = false;
    }
};

const updateStepStatus = (stepIndex: number, completed: boolean, active: boolean = false) => {
    onboardingState.value.steps[stepIndex].completed = completed;
    onboardingState.value.steps[stepIndex].active = active;
};

const nextStep = () => {
    if (isLastStep.value) return;

    // Mark current step as completed
    updateStepStatus(onboardingState.value.currentStep, true, false);

    // Move to next step
    onboardingState.value.currentStep++;

    // Mark new step as active
    updateStepStatus(onboardingState.value.currentStep, false, true);

    // Emit step change event
    emit('step-change', onboardingState.value.currentStep);
};

const previousStep = () => {
    if (isFirstStep.value) return;

    // Mark current step as inactive
    updateStepStatus(onboardingState.value.currentStep, false, false);

    // Move to previous step
    onboardingState.value.currentStep--;

    // Mark previous step as active
    updateStepStatus(onboardingState.value.currentStep, false, true);

    // Emit step change event
    emit('step-change', onboardingState.value.currentStep);
};

const onServiceConfigurationNext = () => {
    // Update service with the current configuration
    numberConfigService.updateServiceConfig(onboardingState.value.serviceConfig);
    nextStep();
};

const onPurchaseComplete = async (results: Record<string, NumberPurchaseResponse>) => {
    purchaseResults.value = results;

    try {
        // Update tenant with purchased numbers using the service
        await numberConfigService.updateTenantWithNumbers(onboardingState.value.serviceConfig, results);

        toast.add({
            severity: 'success',
            summary: 'Configuration Updated',
            detail: 'Phone numbers have been configured successfully',
            life: 3000
        });
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update configuration';
        toast.add({
            severity: 'error',
            summary: 'Update Failed',
            detail: errorMessage,
            life: 5000
        });
    }
};

const onPurchaseNext = () => {
    nextStep();
};

const onValidationComplete = () => {
    updateStepStatus(onboardingState.value.currentStep, true, false);

    toast.add({
        severity: 'success',
        summary: 'Onboarding Complete!',
        detail: 'Your communication services are now ready to use',
        life: 5000
    });

    // Emit completion event with all relevant data
    const completionDelay = props.completionDelay ?? 2000;
    const shouldAutoComplete = props.autoComplete ?? true;

    if (shouldAutoComplete) {
        setTimeout(() => {
            emit('complete', {
                tenant: tenant.value,
                serviceConfig: onboardingState.value.serviceConfig,
                purchaseResults: purchaseResults.value,
                validationStatus: numberConfigService.getValidationStatus()
            });

            console.log('Onboarding completed successfully');
        }, completionDelay);
    } else {
        // Emit immediately if auto-complete is disabled
        emit('complete', {
            tenant: tenant.value,
            serviceConfig: onboardingState.value.serviceConfig,
            purchaseResults: purchaseResults.value,
            validationStatus: numberConfigService.getValidationStatus()
        });
    }
};

const onValidationError = (error: string) => {
    onboardingState.value.error = error;

    // Emit error event
    emit('error', error);

    toast.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: error,
        life: 5000
    });
};

// Lifecycle
onMounted(async () => {
    await initializeService();
});

onUnmounted(() => {
    numberConfigService.destroy();
});
</script>

<template>
    <div class="onboarding-wizard min-h-screen bg-surface-50 dark:bg-surface-900">
        <!-- Header -->
        <div class="bg-white dark:bg-surface-800 border-b border-surface-200 dark:border-surface-700">
            <div class="max-w-6xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">Communication Setup</h1>
                        <p class="text-surface-600 dark:text-surface-400 mt-1">Configure your SMS and call integration</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <i class="pi pi-phone text-primary text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="onboardingState.isLoading" class="flex items-center justify-center min-h-96">
            <div class="text-center">
                <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
                <p class="mt-4 text-surface-600 dark:text-surface-400">Loading...</p>
            </div>
        </div>

        <!-- Error State -->
        <div v-else-if="onboardingState.error" class="max-w-4xl mx-auto px-6 py-8">
            <Message severity="error" :closable="false">
                <div class="flex items-center gap-2">
                    <i class="pi pi-exclamation-triangle"></i>
                    <span>{{ onboardingState.error }}</span>
                </div>
            </Message>
        </div>

        <!-- Main Content -->
        <div v-else class="max-w-6xl mx-auto px-6 py-8">
            <!-- Progress Steps -->
            <div class="mb-8">
                <Steps :model="onboardingState.steps" :activeStep="onboardingState.currentStep" class="mb-6">
                    <template #item="{ item, active, index }">
                        <div class="flex flex-col items-center gap-2">
                            <div
                                :class="[
                                    'w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium',
                                    active ? 'bg-primary text-white' : item.completed ? 'bg-green-500 text-white' : 'bg-surface-200 dark:bg-surface-700 text-surface-600 dark:text-surface-400'
                                ]"
                            >
                                <i v-if="item.completed" class="pi pi-check"></i>
                                <span v-else>{{ index + 1 }}</span>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-sm">{{ item.title }}</div>
                                <div class="text-xs text-surface-500 dark:text-surface-400 max-w-32">
                                    {{ item.description }}
                                </div>
                            </div>
                        </div>
                    </template>
                </Steps>
            </div>

            <!-- Step Content -->
            <div class="bg-white dark:bg-surface-800 rounded-lg shadow-sm border border-surface-200 dark:border-surface-700 p-6">
                <!-- Step 1: Service Configuration -->
                <ServiceConfiguration v-if="onboardingState.currentStep === 0" v-model="onboardingState.serviceConfig" :lead-sources="tenant?.lead_sources" @next="onServiceConfigurationNext" />

                <!-- Step 2: Number Purchase -->
                <NumberPurchase v-else-if="onboardingState.currentStep === 1" :service-config="onboardingState.serviceConfig" @purchase-complete="onPurchaseComplete" @next="onPurchaseNext" />

                <!-- Step 3: Validation -->
                <ValidationProcess
                    v-else-if="onboardingState.currentStep === 2"
                    :service-config="onboardingState.serviceConfig"
                    :purchase-results="purchaseResults"
                    :tenant-id="tenantId"
                    @validation-complete="onValidationComplete"
                    @validation-error="onValidationError"
                />
            </div>

            <!-- Navigation -->
            <div v-if="onboardingState.currentStep < 2" class="flex justify-between mt-6">
                <Button label="Previous" icon="pi pi-arrow-left" outlined :disabled="isFirstStep" @click="previousStep" />

                <div class="text-sm text-surface-500 dark:text-surface-400 flex items-center">Step {{ onboardingState.currentStep + 1 }} of {{ onboardingState.steps.length }}</div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.onboarding-wizard {
    min-height: 100vh;
}

:deep(.p-steps .p-steps-item.p-highlight .p-steps-number) {
    background-color: var(--primary-color);
    color: var(--primary-color-text);
}

:deep(.p-steps .p-steps-item.p-disabled .p-steps-number) {
    background-color: var(--surface-200);
    color: var(--text-color-secondary);
}
</style>
