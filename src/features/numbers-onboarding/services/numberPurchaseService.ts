import { buyTwilioNumber, buyBlandNumber, searchTwilioNumbers } from '@/shared/api/buyNumbers';
import type { NumberPurchaseRequest, NumberPurchaseResponse } from '../types';

// Twilio number search parameters
export interface TwilioSearchParams {
    countryCode?: string;
    areaCode?: string;
    contains?: string;
    smsEnabled?: boolean;
    voiceEnabled?: boolean;
    limit?: number;
}

// Twilio number purchase parameters
export interface TwilioNumberParams {
    phoneNumber: string;
    friendlyName?: string;
    voiceUrl?: string;
    smsUrl?: string;
    countryCode?: string;
}

// Bland AI number purchase parameters
export interface BlandNumberParams {
    phone_number: string;
    webhook?: string;
    max_duration?: number;
    voice_id?: string;
    first_sentence?: string;
    model?: 'base' | 'turbo';
    language?: string;
    timezone?: string;
}

// Twilio purchase response
export interface TwilioPurchaseResponse {
    success: boolean;
    countryCode?: string;
    extractedCountry?: string;
    purchasedNumber?: {
        sid: string;
        phoneNumber: string;
        friendlyName: string;
        accountSid: string;
        capabilities: {
            voice: boolean;
            sms: boolean;
            mms: boolean;
            fax: boolean;
        };
        voiceUrl?: string;
        smsUrl?: string;
        dateCreated: string;
        dateUpdated: string;
    };
    error?: string;
}

// Bland purchase response
export interface BlandPurchaseResponse {
    success: boolean;
    phone_number?: string;
    inbound_id?: string;
    message?: string;
    error?: string;
}

export class NumberPurchaseService {
    /**
     * Search for available Twilio numbers
     */
    static async searchTwilioNumbers(params: TwilioSearchParams): Promise<any[]> {
        try {
            const response = await searchTwilioNumbers({
                countryCode: params.countryCode || 'GB',
                searchParams: {
                    areaCode: params.areaCode,
                    contains: params.contains,
                    smsEnabled: params.smsEnabled ?? true,
                    voiceEnabled: params.voiceEnabled ?? true,
                    limit: params.limit || 10
                }
            });
            
            return response.numbers || [];
        } catch (error) {
            console.error('Error searching Twilio numbers:', error);
            throw new Error('Failed to search for available numbers');
        }
    }

    /**
     * Purchase a Twilio number for SMS and voice calls
     */
    static async purchaseTwilioNumber(params: TwilioNumberParams): Promise<TwilioPurchaseResponse> {
        try {
            const response = await buyTwilioNumber({
                phoneNumber: params.phoneNumber,
                friendlyName: params.friendlyName || 'CRM Integration Number',
                voiceUrl: params.voiceUrl,
                smsUrl: params.smsUrl,
                countryCode: params.countryCode || 'GB'
            });
            
            return response as TwilioPurchaseResponse;
        } catch (error) {
            console.error('Error purchasing Twilio number:', error);
            throw new Error('Failed to purchase Twilio number');
        }
    }

    /**
     * Purchase a Bland AI number for AI calls
     */
    static async purchaseBlandNumber(params: BlandNumberParams): Promise<BlandPurchaseResponse> {
        try {
            const response = await buyBlandNumber({
                phone_number: params.phone_number,
                webhook: params.webhook,
                max_duration: params.max_duration || 300,
                voice_id: params.voice_id,
                first_sentence: params.first_sentence || 'Hello, thank you for calling. How can I help you today?',
                model: params.model || 'base',
                language: params.language || 'en',
                timezone: params.timezone || 'Europe/London'
            });
            
            return response as BlandPurchaseResponse;
        } catch (error) {
            console.error('Error purchasing Bland number:', error);
            throw new Error('Failed to purchase Bland AI number');
        }
    }

    /**
     * Purchase numbers based on service configuration
     */
    static async purchaseNumbers(request: NumberPurchaseRequest): Promise<NumberPurchaseResponse> {
        try {
            if (request.service === 'twilio') {
                const response = await this.purchaseTwilioNumber({
                    phoneNumber: request.phoneNumber,
                    friendlyName: request.friendlyName,
                    countryCode: request.countryCode
                });

                return {
                    success: response.success,
                    phoneNumber: response.purchasedNumber?.phoneNumber,
                    sid: response.purchasedNumber?.sid,
                    message: response.success ? 'Twilio number purchased successfully' : 'Failed to purchase Twilio number',
                    error: response.error
                };
            } else if (request.service === 'bland') {
                const response = await this.purchaseBlandNumber({
                    phone_number: request.phoneNumber
                });

                return {
                    success: response.success,
                    phoneNumber: response.phone_number,
                    inbound_id: response.inbound_id,
                    message: response.message || (response.success ? 'Bland AI number purchased successfully' : 'Failed to purchase Bland AI number'),
                    error: response.error
                };
            } else {
                throw new Error('Invalid service type');
            }
        } catch (error) {
            console.error('Error in purchaseNumbers:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
}
