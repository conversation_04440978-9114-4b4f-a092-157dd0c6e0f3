/**
 * Calendar onboarding feature types and interfaces
 */

/**
 * Calendar configuration interface for tenant
 */
export interface CalendarConfig {
    calendarId?: string;
    validated?: boolean;
    calendarName?: string;
}

/**
 * Onboarding step interface
 */
export interface OnboardingStep {
    id: string;
    title: string;
    description: string;
    icon: string;
    completed: boolean;
    active: boolean;
}

/**
 * Calendar creation form data
 */
export interface CalendarCreationForm {
    calendarName: string;
    timezone?: string;
    isValid: boolean;
    error?: string;
}

/**
 * Calendar onboarding state interface
 */
export interface CalendarOnboardingState {
    currentStep: number;
    steps: OnboardingStep[];
    formData: CalendarCreationForm;
    isLoading: boolean;
    error: string | null;
    calendarId: string | null;
    isValidated: boolean;
    tenantId: string | null;
}

/**
 * Calendar creation response
 */
export interface CalendarCreationResponse {
    id: string;
    success: boolean;
    error?: string;
}

/**
 * Default onboarding steps
 */
export const CALENDAR_ONBOARDING_STEPS: OnboardingStep[] = [
    {
        id: 'setup',
        title: 'Setup Calendar',
        description: 'Enter your desired calendar name and create your Google Calendar',
        icon: 'pi pi-calendar-plus',
        completed: false,
        active: true
    },
    {
        id: 'validation',
        title: 'Validation',
        description: 'Waiting for calendar validation and integration setup',
        icon: 'pi pi-check-circle',
        completed: false,
        active: false
    },
    {
        id: 'complete',
        title: 'Complete',
        description: 'Your Google Calendar integration is ready to use',
        icon: 'pi pi-verified',
        completed: false,
        active: false
    }
];

/**
 * Default form data
 */
export const DEFAULT_CALENDAR_FORM: CalendarCreationForm = {
    calendarName: '',
    timezone: 'Europe/London',
    isValid: false,
    error: undefined
};

/**
 * Validation rules
 */
export const VALIDATION_RULES = {
    calendarName: {
        required: true,
        minLength: 3,
        maxLength: 100,
        pattern: /^[a-zA-Z0-9\s\-_]+$/
    }
};

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
    CALENDAR_NAME_REQUIRED: 'Calendar name is required',
    CALENDAR_NAME_TOO_SHORT: 'Calendar name must be at least 3 characters',
    CALENDAR_NAME_TOO_LONG: 'Calendar name must be less than 100 characters',
    CALENDAR_NAME_INVALID: 'Calendar name can only contain letters, numbers, spaces, hyphens, and underscores',
    CREATION_FAILED: 'Failed to create calendar. Please try again.',
    TENANT_NOT_FOUND: 'User tenant information not found',
    VALIDATION_TIMEOUT: 'Calendar validation is taking longer than expected'
};

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
    CALENDAR_CREATED: 'Calendar created successfully!',
    CALENDAR_VALIDATED: 'Calendar has been validated and is ready to use!',
    INTEGRATION_COMPLETE: 'Google Calendar integration is now complete!'
};
