<template>
    <div class="calendar-onboarding">
        <!-- Header -->
        <div class="onboarding-header text-center mb-6">
            <div class="header-icon mb-3">
                <i class="pi pi-google text-6xl text-primary"></i>
            </div>
            <h1 class="text-4xl font-bold text-900 mb-2">Google Calendar Integration</h1>
            <p class="text-xl text-600 max-w-2xl mx-auto">Connect your Google Calendar to enable seamless appointment booking and management for your business.</p>
        </div>

        <!-- Progress Steps -->
        <div class="mb-6">
            <OnboardingSteps :steps="service.state.value.steps" :current-step="service.state.value.currentStep" :is-loading="service.state.value.isLoading" :error="service.state.value.error" />
        </div>

        <!-- Main Content -->
        <Card class="onboarding-card">
            <template #content>
                <!-- Step 1: Setup Calendar -->
                <div v-if="service.state.value.currentStep === 0" class="setup-step">
                    <div class="step-header mb-4">
                        <h2 class="text-2xl font-semibold text-900 mb-2">Create Your Calendar</h2>
                        <p class="text-600">Enter a name for your business calendar. This will be used for all your appointment bookings.</p>
                    </div>

                    <CalendarNameInput
                        :calendar-name="service.state.value.formData.calendarName"
                        :timezone="service.state.value.formData.timezone"
                        :error="service.state.value.formData.error"
                        :disabled="service.state.value.isLoading"
                        @update:calendar-name="handleCalendarNameUpdate"
                        @update:timezone="handleTimezoneUpdate"
                    />

                    <div class="step-actions mt-6">
                        <Button label="Create Calendar" icon="pi pi-plus" :loading="service.state.value.isLoading" :disabled="!service.isFormValid.value || service.state.value.isLoading" @click="handleCreateCalendar" class="p-button-lg" />
                    </div>
                </div>

                <!-- Step 2: Validation -->
                <div v-else-if="service.state.value.currentStep === 1" class="validation-step">
                    <div class="step-header mb-4">
                        <h2 class="text-2xl font-semibold text-900 mb-2">Setting Up Integration</h2>
                        <p class="text-600">We're configuring your Google Calendar integration. This process is automatic and may take a few moments.</p>
                    </div>

                    <CalendarStatusDisplay
                        :calendar-id="service.state.value.calendarId"
                        :calendar-name="service.state.value.formData.calendarName"
                        :is-validated="service.state.value.isValidated"
                        :is-validating="!service.state.value.isValidated && !!service.state.value.calendarId"
                        :error="service.state.value.error"
                    />
                </div>

                <!-- Step 3: Complete -->
                <div v-else-if="service.state.value.currentStep === 2" class="complete-step">
                    <div class="step-header mb-4">
                        <h2 class="text-2xl font-semibold text-900 mb-2">Integration Complete!</h2>
                        <p class="text-600">Your Google Calendar is now connected and ready for appointment bookings.</p>
                    </div>

                    <CalendarStatusDisplay
                        :calendar-id="service.state.value.calendarId"
                        :calendar-name="service.state.value.formData.calendarName"
                        :is-validated="service.state.value.isValidated"
                        :is-validating="false"
                        :error="service.state.value.error"
                    />

                    <div class="completion-actions mt-6">
                        <div class="flex gap-3 justify-content-center">
                            <Button label="View Calendar" icon="pi pi-external-link" class="p-button-outlined" @click="openGoogleCalendar" />
                            <Button label="Start Booking Appointments" icon="pi pi-calendar-plus" @click="navigateToAppointments" />
                        </div>
                    </div>
                </div>

                <!-- Error State -->
                <div v-if="service.state.value.error && service.state.value.currentStep === 0" class="error-section mt-4">
                    <Message severity="error" :closable="false">
                        <div class="flex align-items-center">
                            <i class="pi pi-exclamation-triangle mr-2"></i>
                            <div>
                                <strong>Error:</strong> {{ service.state.value.error }}
                                <div class="mt-2">
                                    <Button label="Try Again" icon="pi pi-refresh" size="small" @click="handleRetry" />
                                </div>
                            </div>
                        </div>
                    </Message>
                </div>
            </template>
        </Card>

        <!-- Help Section -->
        <div class="help-section mt-6 text-center">
            <h3 class="text-lg font-semibold text-900 mb-2">Need Help?</h3>
            <p class="text-600 mb-3">If you're experiencing issues with the calendar integration, please contact our support team.</p>
            <Button label="Contact Support" icon="pi pi-question-circle" class="p-button-text" @click="contactSupport" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import Card from 'primevue/card';
import Button from 'primevue/button';
import Message from 'primevue/message';

import { useCalendarOnboardingService } from './services/calendarOnboardingService';
import CalendarNameInput from './components/CalendarNameInput.vue';
import CalendarStatusDisplay from './components/CalendarStatusDisplay.vue';
import OnboardingSteps from './components/OnboardingSteps.vue';
import { SUCCESS_MESSAGES } from './types';

// Composables
const service = useCalendarOnboardingService();
const router = useRouter();
const toast = useToast();

// Emits
interface Emits {
    (e: 'complete', data: { calendarId: string; calendarName: string }): void;
}

const emit = defineEmits<Emits>();

// Methods
const handleCalendarNameUpdate = (value: string) => {
    service.updateFormData({ calendarName: value });
};

const handleTimezoneUpdate = (value: string) => {
    service.updateFormData({ timezone: value });
};

const handleCreateCalendar = async () => {
    const result = await service.createCalendar();

    if (result.success) {
        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: SUCCESS_MESSAGES.CALENDAR_CREATED,
            life: 5000
        });
    } else {
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: result.error || 'Failed to create calendar',
            life: 5000
        });
    }
};

const handleRetry = () => {
    service.setError(null);
};

const openGoogleCalendar = () => {
    if (service.state.value.calendarId) {
        const url = `https://calendar.google.com/calendar/embed?src=${service.state.value.calendarId}`;
        window.open(url, '_blank');
    }
};

const navigateToAppointments = () => {
    router.push('/appointments');
};

const contactSupport = () => {
    // Implement support contact logic
    window.open('mailto:<EMAIL>?subject=Calendar Integration Help', '_blank');
};

// Watch for completion
watch(
    () => service.state.value.currentStep,
    (newStep) => {
        if (newStep === 2 && service.state.value.isValidated && service.state.value.calendarId) {
            // Emit complete event when prompts-onboarding is finished
            emit('complete', {
                calendarId: service.state.value.calendarId,
                calendarName: service.state.value.formData.calendarName
            });
        }
    }
);

// Lifecycle
onMounted(async () => {
    await service.initialize();
});

onUnmounted(() => {
    service.cleanup();
});
</script>

<style scoped>
.calendar-onboarding {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.onboarding-header {
    margin-bottom: 3rem;
}

.header-icon {
    display: flex;
    justify-content: center;
}

.onboarding-card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--surface-border);
}

.step-header {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.step-actions {
    display: flex;
    justify-content: center;
}

.completion-actions {
    text-align: center;
}

.help-section {
    background: var(--surface-50);
    border-radius: 8px;
    padding: 2rem;
    border: 1px solid var(--surface-border);
}

.error-section {
    max-width: 500px;
    margin: 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
    .calendar-onboarding {
        padding: 1rem 0.5rem;
    }

    .onboarding-header h1 {
        font-size: 2rem;
    }

    .onboarding-header p {
        font-size: 1rem;
    }

    .step-header h2 {
        font-size: 1.5rem;
    }

    .completion-actions .flex {
        flex-direction: column;
        align-items: center;
    }

    .completion-actions .flex .p-button {
        width: 100%;
        max-width: 300px;
    }
}

.text-primary {
    color: var(--primary-color);
}

.text-900 {
    color: var(--text-color);
}

.text-600 {
    color: var(--text-color-secondary);
}
</style>
