# Google Calendar Onboarding Feature

A comprehensive Vue.js feature for onboarding users to Google Calendar integration in a multi-tenant CRM application.

## Features

- **Calendar Creation**: Create a Google Calendar with custom name and timezone
- **Real-time Validation**: Uses Firestore onSnapshot for live calendar validation updates
- **Step-by-Step UI**: Beautiful, guided onboarding flow with progress indicators
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Responsive Design**: Mobile-friendly interface using PrimeVue components
- **Tenant Integration**: Seamlessly integrates with tenant store and authentication

## Components

### CalendarOnboarding.vue
Main orchestrating component that manages the entire onboarding flow.

### CalendarNameInput.vue
Form component for calendar name and timezone selection with validation.

### CalendarStatusDisplay.vue
Status display component showing calendar creation and validation progress.

### OnboardingSteps.vue
Progress indicator component showing the current step in the onboarding process.

## Service

### CalendarOnboardingService
Service class that manages:
- Onboarding state and form data
- Calendar creation API calls
- Real-time tenant subscription for validation updates
- Form validation and error handling

### useCalendarOnboardingService
Composable function that provides access to the service with reactive state.

## Usage

```vue
<template>
  <CalendarOnboarding @complete="handleComplete" />
</template>

<script setup lang="ts">
import { CalendarOnboarding } from '@/features/calendar-prompts-onboarding';

const handleComplete = (data: { calendarId: string; calendarName: string }) => {
  console.log('Calendar prompts-onboarding completed:', data);
  // Handle completion logic
};
</script>
```

## Data Flow

1. **Initialization**:
   - Gets user data and tenant ID from auth store
   - Checks existing calendar configuration
   - Subscribes to tenant changes for real-time updates
   - Emits 'complete' event when onboarding finishes

2. **Step 1 - Setup**: 
   - User enters calendar name and selects timezone
   - Form validation ensures proper input format
   - Creates Google Calendar via Firebase function

3. **Step 2 - Validation**: 
   - Automatically monitors tenant updates via onSnapshot
   - Shows progress while calendar is being validated
   - Moves to completion when `tenant.calendar.validated === true`

4. **Step 3 - Complete**: 
   - Shows success state with integration features
   - Provides links to view calendar and start booking

## Tenant Data Structure

The feature updates and monitors the tenant with the following structure:

```typescript
{
  calendar: {
    calendarId: "calendar_id_from_google",
    validated: true
  }
}
```

## API Integration

Uses the existing `requestCreateGoogleCalendar` function from `@/shared/api/appointment.ts` which:
- Gets user tenant ID automatically
- Calls Firebase function `onCreateCalendar`
- Returns the created calendar ID

## Error Handling

Comprehensive error handling for:
- Form validation errors
- API call failures
- Tenant data loading issues
- Calendar creation failures

## Styling

- Uses PrimeVue design system
- Responsive design with mobile breakpoints
- Consistent with existing application styling
- Custom status indicators and progress animations

## Dependencies

- Vue 3 Composition API
- Service-based architecture (following project patterns)
- PrimeVue UI components
- Firebase Firestore for real-time updates
- Vue Router for navigation

## Configuration

The feature includes configurable:
- Validation rules for calendar names
- Error and success messages
- Timezone options
- Onboarding step definitions

## Real-time Updates

The feature uses Firestore `onSnapshot` to listen for tenant changes:
- Automatically detects when calendar is created
- Monitors validation status changes
- Updates UI in real-time without user interaction

This ensures users see immediate feedback when the backend processes complete.
