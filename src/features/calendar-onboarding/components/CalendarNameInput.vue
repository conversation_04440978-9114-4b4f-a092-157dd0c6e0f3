<template>
    <div class="calendar-name-input">
        <div class="field">
            <label for="calendarName" class="block text-900 font-medium mb-2">
                Calendar Name <span class="text-red-500">*</span>
            </label>
            <div class="p-inputgroup">
                <span class="p-inputgroup-addon">
                    <i class="pi pi-calendar"></i>
                </span>
                <InputText
                    id="calendarName"
                    v-model="localCalendarName"
                    :class="{ 'p-invalid': hasError }"
                    placeholder="Enter your calendar name (e.g., My Business Calendar)"
                    :disabled="disabled"
                    @input="handleInput"
                    @blur="handleBlur"
                />
            </div>
            <small v-if="hasError" class="p-error block mt-1">
                {{ errorMessage }}
            </small>
            <small v-else class="text-600 block mt-1">
                This will be the name of your Google Calendar for appointments
            </small>
        </div>

        <div class="field">
            <label for="timezone" class="block text-900 font-medium mb-2">
                Timezone
            </label>
            <Dropdown
                id="timezone"
                v-model="localTimezone"
                :options="timezoneOptions"
                option-label="label"
                option-value="value"
                placeholder="Select timezone"
                :disabled="disabled"
                class="w-full"
                @change="handleTimezoneChange"
            />
            <small class="text-600 block mt-1">
                Select your business timezone for appointment scheduling
            </small>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';

// Props
interface Props {
    calendarName: string;
    timezone?: string;
    error?: string;
    disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    timezone: 'Europe/London',
    error: undefined,
    disabled: false
});

// Emits
interface Emits {
    (e: 'update:calendarName', value: string): void;
    (e: 'update:timezone', value: string): void;
    (e: 'validate', isValid: boolean): void;
}

const emit = defineEmits<Emits>();

// Local state
const localCalendarName = ref(props.calendarName);
const localTimezone = ref(props.timezone);

// Computed
const hasError = computed(() => !!props.error);
const errorMessage = computed(() => props.error);

// Timezone options
const timezoneOptions = ref([
    { label: 'London (GMT)', value: 'Europe/London' },
    { label: 'New York (EST)', value: 'America/New_York' },
    { label: 'Los Angeles (PST)', value: 'America/Los_Angeles' },
    { label: 'Chicago (CST)', value: 'America/Chicago' },
    { label: 'Denver (MST)', value: 'America/Denver' },
    { label: 'Paris (CET)', value: 'Europe/Paris' },
    { label: 'Berlin (CET)', value: 'Europe/Berlin' },
    { label: 'Tokyo (JST)', value: 'Asia/Tokyo' },
    { label: 'Sydney (AEST)', value: 'Australia/Sydney' },
    { label: 'Dubai (GST)', value: 'Asia/Dubai' },
    { label: 'Singapore (SGT)', value: 'Asia/Singapore' },
    { label: 'Hong Kong (HKT)', value: 'Asia/Hong_Kong' },
    { label: 'Mumbai (IST)', value: 'Asia/Kolkata' },
    { label: 'Toronto (EST)', value: 'America/Toronto' },
    { label: 'Vancouver (PST)', value: 'America/Vancouver' }
]);

// Methods
const handleInput = () => {
    emit('update:calendarName', localCalendarName.value);
};

const handleBlur = () => {
    // Trim whitespace on blur
    localCalendarName.value = localCalendarName.value.trim();
    emit('update:calendarName', localCalendarName.value);
};

const handleTimezoneChange = () => {
    emit('update:timezone', localTimezone.value);
};

// Validation
const validateInput = () => {
    const name = localCalendarName.value.trim();
    const isValid = name.length >= 3 && name.length <= 100 && /^[a-zA-Z0-9\s\-_]+$/.test(name);
    emit('validate', isValid);
};

// Watchers
watch(() => props.calendarName, (newValue) => {
    localCalendarName.value = newValue;
});

watch(() => props.timezone, (newValue) => {
    localTimezone.value = newValue;
});

watch(localCalendarName, () => {
    validateInput();
});

// Lifecycle
onMounted(() => {
    validateInput();
});
</script>

<style scoped>
.calendar-name-input {
    max-width: 500px;
}

.field {
    margin-bottom: 1.5rem;
}

.p-inputgroup {
    width: 100%;
}

.p-inputgroup-addon {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.p-invalid {
    border-color: var(--red-500);
}

.p-error {
    color: var(--red-500);
    font-size: 0.875rem;
}

.text-600 {
    color: var(--text-color-secondary);
    font-size: 0.875rem;
}

.text-900 {
    color: var(--text-color);
}

.text-red-500 {
    color: var(--red-500);
}
</style>
