<template>
    <div class="calendar-status-display">
        <!-- Calendar Creation Status -->
        <div v-if="calendarId" class="status-card mb-4">
            <div class="flex align-items-center mb-3">
                <div class="status-icon success mr-3">
                    <i class="pi pi-check"></i>
                </div>
                <div>
                    <h4 class="m-0 text-900">Calendar Created</h4>
                    <p class="m-0 text-600">Your Google Calendar has been successfully created</p>
                </div>
            </div>
            <div class="calendar-info">
                <div class="info-item">
                    <span class="label">Calendar ID:</span>
                    <span class="value">{{ calendarId }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Name:</span>
                    <span class="value">{{ calendarName || 'N/A' }}</span>
                </div>
            </div>
        </div>

        <!-- Validation Status -->
        <div class="status-card mb-4">
            <div class="flex align-items-center mb-3">
                <div :class="['status-icon', validationStatusClass, 'mr-3']">
                    <i :class="validationIconClass"></i>
                </div>
                <div>
                    <h4 class="m-0 text-900">{{ validationTitle }}</h4>
                    <p class="m-0 text-600">{{ validationDescription }}</p>
                </div>
            </div>
            
            <!-- Progress indicator for validation -->
            <div v-if="isValidating" class="validation-progress">
                <ProgressBar mode="indeterminate" style="height: 6px" />
                <small class="text-600 mt-2 block">
                    This may take a few moments...
                </small>
            </div>
        </div>

        <!-- Integration Status -->
        <div v-if="isValidated" class="status-card success-card">
            <div class="flex align-items-center mb-3">
                <div class="status-icon success mr-3">
                    <i class="pi pi-verified"></i>
                </div>
                <div>
                    <h4 class="m-0 text-900">Integration Complete</h4>
                    <p class="m-0 text-600">Your Google Calendar is ready for appointment booking</p>
                </div>
            </div>
            
            <div class="integration-features">
                <div class="feature-item">
                    <i class="pi pi-check-circle text-green-500 mr-2"></i>
                    <span>Automatic appointment synchronization</span>
                </div>
                <div class="feature-item">
                    <i class="pi pi-check-circle text-green-500 mr-2"></i>
                    <span>Real-time availability checking</span>
                </div>
                <div class="feature-item">
                    <i class="pi pi-check-circle text-green-500 mr-2"></i>
                    <span>Customer booking notifications</span>
                </div>
            </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="status-card error-card">
            <div class="flex align-items-center mb-3">
                <div class="status-icon error mr-3">
                    <i class="pi pi-exclamation-triangle"></i>
                </div>
                <div>
                    <h4 class="m-0 text-900">Error</h4>
                    <p class="m-0 text-600">{{ error }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ProgressBar from 'primevue/progressbar';

// Props
interface Props {
    calendarId?: string | null;
    calendarName?: string;
    isValidated?: boolean;
    isValidating?: boolean;
    error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
    calendarId: null,
    calendarName: '',
    isValidated: false,
    isValidating: false,
    error: null
});

// Computed
const validationStatusClass = computed(() => {
    if (props.error) return 'error';
    if (props.isValidated) return 'success';
    if (props.isValidating) return 'warning';
    return 'pending';
});

const validationIconClass = computed(() => {
    if (props.error) return 'pi pi-times';
    if (props.isValidated) return 'pi pi-check';
    if (props.isValidating) return 'pi pi-spin pi-spinner';
    return 'pi pi-clock';
});

const validationTitle = computed(() => {
    if (props.error) return 'Validation Failed';
    if (props.isValidated) return 'Calendar Validated';
    if (props.isValidating) return 'Validating Calendar';
    return 'Waiting for Validation';
});

const validationDescription = computed(() => {
    if (props.error) return 'There was an error validating your calendar';
    if (props.isValidated) return 'Your calendar has been successfully validated and integrated';
    if (props.isValidating) return 'We are setting up your calendar integration';
    return 'Calendar validation will begin automatically';
});
</script>

<style scoped>
.calendar-status-display {
    max-width: 600px;
}

.status-card {
    background: white;
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.success-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-color: var(--green-200);
}

.error-card {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border-color: var(--red-200);
}

.status-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: bold;
}

.status-icon.success {
    background: var(--green-100);
    color: var(--green-600);
}

.status-icon.warning {
    background: var(--yellow-100);
    color: var(--yellow-600);
}

.status-icon.error {
    background: var(--red-100);
    color: var(--red-600);
}

.status-icon.pending {
    background: var(--blue-100);
    color: var(--blue-600);
}

.calendar-info {
    background: var(--surface-50);
    border-radius: 6px;
    padding: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.label {
    font-weight: 600;
    color: var(--text-color-secondary);
}

.value {
    color: var(--text-color);
    font-family: monospace;
    font-size: 0.9rem;
}

.validation-progress {
    margin-top: 1rem;
}

.integration-features {
    margin-top: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--text-color);
}

.feature-item:last-child {
    margin-bottom: 0;
}

.text-green-500 {
    color: var(--green-500);
}

.text-900 {
    color: var(--text-color);
}

.text-600 {
    color: var(--text-color-secondary);
}
</style>
