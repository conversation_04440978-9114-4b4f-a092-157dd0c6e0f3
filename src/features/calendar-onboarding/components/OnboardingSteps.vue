<template>
    <div class="onboarding-steps">
        <div class="steps-container">
            <div 
                v-for="(step, index) in steps" 
                :key="step.id"
                :class="['step-item', getStepClass(step, index)]"
            >
                <!-- Step Circle -->
                <div class="step-circle">
                    <div class="step-number">
                        <i v-if="step.completed" class="pi pi-check"></i>
                        <i v-else-if="step.active && isLoading" class="pi pi-spin pi-spinner"></i>
                        <span v-else>{{ index + 1 }}</span>
                    </div>
                </div>

                <!-- Step Content -->
                <div class="step-content">
                    <h4 class="step-title">{{ step.title }}</h4>
                    <p class="step-description">{{ step.description }}</p>
                    
                    <!-- Step Status -->
                    <div v-if="step.active && !step.completed" class="step-status">
                        <div v-if="isLoading" class="status-loading">
                            <ProgressBar mode="indeterminate" style="height: 4px" />
                            <small class="text-600 mt-1">Processing...</small>
                        </div>
                        <div v-else-if="error && step.id === 'setup'" class="status-error">
                            <small class="text-red-500">{{ error }}</small>
                        </div>
                    </div>
                </div>

                <!-- Connector Line -->
                <div 
                    v-if="index < steps.length - 1" 
                    :class="['step-connector', { 'completed': step.completed }]"
                ></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ProgressBar from 'primevue/progressbar';
import type { OnboardingStep } from '../types';

// Props
interface Props {
    steps: OnboardingStep[];
    currentStep: number;
    isLoading?: boolean;
    error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
    isLoading: false,
    error: null
});

// Methods
const getStepClass = (step: OnboardingStep, index: number) => {
    const classes = [];
    
    if (step.completed) {
        classes.push('completed');
    } else if (step.active) {
        classes.push('active');
    } else if (index < props.currentStep) {
        classes.push('completed');
    } else {
        classes.push('pending');
    }
    
    return classes.join(' ');
};
</script>

<style scoped>
.onboarding-steps {
    max-width: 600px;
    margin: 0 auto;
}

.steps-container {
    position: relative;
}

.step-item {
    display: flex;
    align-items: flex-start;
    position: relative;
    padding-bottom: 2rem;
}

.step-item:last-child {
    padding-bottom: 0;
}

.step-circle {
    flex-shrink: 0;
    margin-right: 1rem;
    z-index: 2;
}

.step-number {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 2px solid;
}

.step-content {
    flex: 1;
    padding-top: 0.25rem;
}

.step-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.step-description {
    margin: 0 0 0.75rem 0;
    color: var(--text-color-secondary);
    line-height: 1.5;
}

.step-status {
    margin-top: 0.5rem;
}

.status-loading {
    max-width: 200px;
}

.status-error {
    color: var(--red-500);
}

.step-connector {
    position: absolute;
    left: 1.5rem;
    top: 3rem;
    width: 2px;
    height: calc(100% - 1rem);
    background: var(--surface-border);
    transition: background-color 0.3s ease;
    z-index: 1;
}

.step-connector.completed {
    background: var(--primary-color);
}

/* Step States */
.step-item.pending .step-number {
    background: var(--surface-100);
    border-color: var(--surface-border);
    color: var(--text-color-secondary);
}

.step-item.pending .step-title {
    color: var(--text-color-secondary);
}

.step-item.active .step-number {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.2);
}

.step-item.active .step-title {
    color: var(--primary-color);
}

.step-item.completed .step-number {
    background: var(--green-500);
    border-color: var(--green-500);
    color: white;
}

.step-item.completed .step-title {
    color: var(--text-color);
}

/* Responsive */
@media (max-width: 768px) {
    .step-item {
        padding-bottom: 1.5rem;
    }
    
    .step-number {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 0.875rem;
    }
    
    .step-connector {
        left: 1.25rem;
        top: 2.5rem;
    }
    
    .step-title {
        font-size: 1rem;
    }
    
    .step-description {
        font-size: 0.875rem;
    }
}

.text-600 {
    color: var(--text-color-secondary);
}

.text-red-500 {
    color: var(--red-500);
}
</style>
