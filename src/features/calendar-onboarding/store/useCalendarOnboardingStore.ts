import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant';
import { requestCreateGoogleCalendar } from '@/shared/api/appointment';
import type { 
    CalendarOnboardingState, 
    CalendarCreationForm, 
    OnboardingStep,
    CalendarCreationResponse 
} from '../types';
import { 
    CALENDAR_ONBOARDING_STEPS, 
    DEFAULT_CALENDAR_FORM, 
    VALIDATION_RULES, 
    ERROR_MESSAGES,
    SUCCESS_MESSAGES 
} from '../types';
import type { Tenant } from '@/entities/tenant/model';

export const useCalendarOnboardingStore = defineStore('calendarOnboarding', () => {
    // State
    const state = ref<CalendarOnboardingState>({
        currentStep: 0,
        steps: [...CALENDAR_ONBOARDING_STEPS],
        formData: { ...DEFAULT_CALENDAR_FORM },
        isLoading: false,
        error: null,
        calendarId: null,
        isValidated: false,
        tenantId: null
    });

    // Store dependencies
    const authStore = useAuthStore();
    const tenantStore = useTenantStore();

    // Tenant subscription
    let tenantUnsubscribe: (() => void) | null = null;

    // Computed
    const currentStep = computed(() => state.value.steps[state.value.currentStep]);
    const isFormValid = computed(() => validateCalendarName(state.value.formData.calendarName).isValid);
    const canProceed = computed(() => {
        if (state.value.currentStep === 0) {
            return isFormValid.value && !state.value.isLoading;
        }
        return state.value.isValidated;
    });

    // Validation functions
    const validateCalendarName = (name: string) => {
        const rules = VALIDATION_RULES.calendarName;
        
        if (!name || name.trim().length === 0) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_REQUIRED };
        }
        
        if (name.length < rules.minLength) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_TOO_SHORT };
        }
        
        if (name.length > rules.maxLength) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_TOO_LONG };
        }
        
        if (!rules.pattern.test(name)) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_INVALID };
        }
        
        return { isValid: true, error: undefined };
    };

    // Actions
    const updateFormData = (updates: Partial<CalendarCreationForm>) => {
        state.value.formData = { ...state.value.formData, ...updates };
        
        // Validate calendar name if it's being updated
        if (updates.calendarName !== undefined) {
            const validation = validateCalendarName(updates.calendarName);
            state.value.formData.isValid = validation.isValid;
            state.value.formData.error = validation.error;
        }
    };

    const setError = (error: string | null) => {
        state.value.error = error;
    };

    const setLoading = (loading: boolean) => {
        state.value.isLoading = loading;
    };

    const updateStepStatus = (stepIndex: number, completed: boolean, active: boolean = false) => {
        if (stepIndex >= 0 && stepIndex < state.value.steps.length) {
            state.value.steps[stepIndex].completed = completed;
            state.value.steps[stepIndex].active = active;
        }
    };

    const nextStep = () => {
        if (state.value.currentStep < state.value.steps.length - 1) {
            // Mark current step as completed
            updateStepStatus(state.value.currentStep, true, false);
            
            // Move to next step
            state.value.currentStep++;
            updateStepStatus(state.value.currentStep, false, true);
        }
    };

    const initializeOnboarding = async () => {
        try {
            setLoading(true);
            setError(null);

            // Get user data and tenant ID
            const userData = await authStore.getUserData();
            if (!userData?.tenantId) {
                throw new Error(ERROR_MESSAGES.TENANT_NOT_FOUND);
            }

            state.value.tenantId = userData.tenantId;

            // Get tenant data to check existing calendar
            const tenant = await tenantStore.getTenant(userData.tenantId);
            if (tenant?.calendar) {
                state.value.calendarId = tenant.calendar.calendarId || null;
                state.value.isValidated = tenant.calendar.validated || false;
                
                // If calendar already exists and is validated, skip to completion
                if (tenant.calendar.calendarId && tenant.calendar.validated) {
                    state.value.currentStep = 2; // Complete step
                    updateStepStatus(0, true, false);
                    updateStepStatus(1, true, false);
                    updateStepStatus(2, true, true);
                }
            }

            // Subscribe to tenant changes for real-time updates
            subscribeToTenantChanges(userData.tenantId);

        } catch (error) {
            console.error('Error initializing calendar onboarding:', error);
            setError(error instanceof Error ? error.message : 'Failed to initialize onboarding');
        } finally {
            setLoading(false);
        }
    };

    const subscribeToTenantChanges = (tenantId: string) => {
        // Clean up existing subscription
        if (tenantUnsubscribe) {
            tenantUnsubscribe();
        }

        tenantUnsubscribe = tenantStore.subscribeToTenantChanges(tenantId, (tenant: Tenant | null) => {
            if (tenant?.calendar) {
                const previousCalendarId = state.value.calendarId;
                const previousValidated = state.value.isValidated;

                state.value.calendarId = tenant.calendar.calendarId || null;
                state.value.isValidated = tenant.calendar.validated || false;

                // If calendar was just created (new calendar ID)
                if (tenant.calendar.calendarId && !previousCalendarId) {
                    nextStep(); // Move to validation step
                }

                // If calendar was just validated
                if (tenant.calendar.validated && !previousValidated) {
                    nextStep(); // Move to complete step
                }
            }
        });
    };

    const createCalendar = async (): Promise<CalendarCreationResponse> => {
        try {
            setLoading(true);
            setError(null);

            if (!isFormValid.value) {
                throw new Error(state.value.formData.error || 'Form validation failed');
            }

            if (!state.value.tenantId) {
                throw new Error(ERROR_MESSAGES.TENANT_NOT_FOUND);
            }

            const calendarId = await requestCreateGoogleCalendar(
                state.value.formData.calendarName,
                state.value.formData.timezone
            );

            if (calendarId) {
                state.value.calendarId = calendarId;
                return { id: calendarId, success: true };
            } else {
                throw new Error('No calendar ID returned from server');
            }

        } catch (error) {
            console.error('Error creating calendar:', error);
            const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.CREATION_FAILED;
            setError(errorMessage);
            return { id: '', success: false, error: errorMessage };
        } finally {
            setLoading(false);
        }
    };

    const resetOnboarding = () => {
        state.value = {
            currentStep: 0,
            steps: [...CALENDAR_ONBOARDING_STEPS],
            formData: { ...DEFAULT_CALENDAR_FORM },
            isLoading: false,
            error: null,
            calendarId: null,
            isValidated: false,
            tenantId: null
        };

        // Clean up subscription
        if (tenantUnsubscribe) {
            tenantUnsubscribe();
            tenantUnsubscribe = null;
        }
    };

    const cleanup = () => {
        if (tenantUnsubscribe) {
            tenantUnsubscribe();
            tenantUnsubscribe = null;
        }
    };

    return {
        // State
        state: computed(() => state.value),
        currentStep,
        isFormValid,
        canProceed,

        // Actions
        updateFormData,
        setError,
        setLoading,
        initializeOnboarding,
        createCalendar,
        resetOnboarding,
        cleanup,
        validateCalendarName
    };
});
