<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useTenantStore } from '@/entities/tenant/store';
import { useAuthStore } from '@/entities/auth';
import { storeToRefs } from 'pinia';
import { validateEmail, validateRequired, validatePhoneNumber, validateUrl } from '@/utils';
import { INDUSTRY_OPTIONS, COMPANY_SIZE_OPTIONS, COUNTRY_OPTIONS } from './types';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import Textarea from 'primevue/textarea';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import { CompanyInfoService } from './services/companyInfoService';
import type { Tenant } from '@/entities/tenant/model';

// Props
const props = defineProps<{
    tenantId?: string;
    readonly?: boolean;
    showHeader?: boolean;
    showSaveButton?: boolean; // Control whether to show save buttons
}>();

// Stores
const toast = useToast();
const tenantStore = useTenantStore();
const authStore = useAuthStore();
const { isLoading } = storeToRefs(tenantStore);

// Local state
const companyInfo = ref<Partial<Tenant>>({});
const isFormValid = ref(false);
const isSaving = ref(false);
const hasUnsavedChanges = ref(false);
const originalData = ref<Partial<Tenant>>({});
const submitted = ref(false);
const currentStep = ref(0);

// Form errors - simplified for essential fields only
const errors = ref({
    name: '',
    emailAddress: '',
    phone: '',
    website: '',
    industry: '',
    description: '',
    city: '',
    country: ''
});

// Computed properties
const currentTenantId = computed(() => {
    return props.tenantId || authStore.user?.tenantId || '';
});

const isReadonly = computed(() => {
    return props.readonly || false;
});

const showHeaderSection = computed(() => {
    return props.showHeader !== false;
});

const showSaveButtons = computed(() => {
    return props.showSaveButton !== false;
});

// Validation functions
const validateField = (field: keyof typeof errors.value, value: string, validator: (val: string) => string) => {
    const error = validator(value);
    errors.value[field] = error;
    return !error;
};

const validateRequiredField = (field: keyof typeof errors.value, value: string) => {
    return validateField(field, value, validateRequired);
};

const validateEmailField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateEmail);
};

const validatePhoneField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validatePhoneNumber);
};

const validateUrlField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateUrl);
};

const validateFormFields = () => {
    const validations = [
        validateRequiredField('name', companyInfo.value.name || ''),
        validateRequiredField('emailAddress', companyInfo.value.emailAddress || ''),
        validateEmailField('emailAddress', companyInfo.value.emailAddress || ''),
        validatePhoneField('phone', companyInfo.value.phone || ''),
        validateUrlField('website', companyInfo.value.website || '')
    ];

    // Validate postal code if provided
    if (companyInfo.value.postalCode) {
        validations.push(validateField('postalCode', companyInfo.value.postalCode, validatePostalCode));
    }

    // Validate company registration number if provided
    if (companyInfo.value.companyRegistrationNumber) {
        validations.push(validateField('companyRegistrationNumber', companyInfo.value.companyRegistrationNumber, validateCompanyNumber));
    }

    // Validate VAT number if provided
    if (companyInfo.value.vatNumber) {
        validations.push(validateField('vatNumber', companyInfo.value.vatNumber, validateVatNumber));
    }

    // Validate founded year if provided
    if (companyInfo.value.foundedYear) {
        const currentYear = new Date().getFullYear();
        const yearStr = companyInfo.value.foundedYear.toString();
        const yearError = validateNumber(yearStr);
        if (yearError) {
            errors.value.foundedYear = yearError;
            validations.push(false);
        } else {
            const year = parseInt(yearStr);
            if (year < 1800 || year > currentYear) {
                errors.value.foundedYear = `Please enter a year between 1800 and ${currentYear}.`;
                validations.push(false);
            } else {
                errors.value.foundedYear = '';
                validations.push(true);
            }
        }
    }

    const isValid = validations.every(Boolean);
    isFormValid.value = isValid;
    return isValid;
};

// Methods
const loadCompanyInfo = async () => {
    if (!currentTenantId.value) {
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'No tenant ID available',
            life: 3000
        });
        return;
    }

    try {
        const data = await CompanyInfoService.getCompanyInfo(currentTenantId.value);
        companyInfo.value = { ...data };
        originalData.value = { ...data };
        hasUnsavedChanges.value = false;
    } catch (error) {
        console.error('Error loading company info:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load company information',
            life: 3000
        });
    }
};

const saveCompanyInfo = async (data?: Partial<Tenant>) => {
    submitted.value = true;
    const dataToSave = data || companyInfo.value;

    if (!validateFormFields()) {
        toast.add({
            severity: 'error',
            summary: 'Validation Error',
            detail: 'Please fix the errors in the form',
            life: 3000
        });
        return;
    }

    isSaving.value = true;

    try {
        // Convert to tenant format and save using existing updateTenant method
        const tenantData = CompanyInfoService.convertToTenantData(dataToSave);

        if (!currentTenantId.value) {
            currentTenantId.value = await tenantStore.createTenant(tenantData);
        } else {
            await tenantStore.updateTenant(currentTenantId.value, tenantData);
        }
        return;
        // Update local state
        originalData.value = { ...dataToSave };
        hasUnsavedChanges.value = false;
        submitted.value = false;

        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Company information updated successfully',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving company info:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save company information',
            life: 3000
        });
    } finally {
        isSaving.value = false;
    }
};

const handleFormUpdate = () => {
    // Check if there are unsaved changes
    const hasChanges = JSON.stringify(companyInfo.value) !== JSON.stringify(originalData.value);
    hasUnsavedChanges.value = hasChanges;

    // Validate form if submitted
    if (submitted.value) {
        validateFormFields();
    }
};

const resetForm = () => {
    companyInfo.value = { ...originalData.value };
    hasUnsavedChanges.value = false;
};

const confirmReset = () => {
    if (hasUnsavedChanges.value) {
        // Show confirmation dialog
        // For now, just reset directly
        resetForm();
        toast.add({
            severity: 'info',
            summary: 'Reset',
            detail: 'Form has been reset to original values',
            life: 3000
        });
    }
};

// Computed property to check if form has any errors
const hasErrors = computed(() => {
    return Object.values(errors.value).some((error) => error !== '');
});

// Step configuration for modern step-by-step form
const steps = [
    {
        id: 1,
        title: 'Company Basics',
        description: 'Essential company information',
        icon: 'pi pi-building',
        fields: ['name', 'industry', 'description']
    },
    {
        id: 2,
        title: 'Contact Information',
        description: 'How to reach your company',
        icon: 'pi pi-phone',
        fields: ['emailAddress', 'phone', 'website']
    },
    {
        id: 3,
        title: 'Location',
        description: 'Where your company is located',
        icon: 'pi pi-map-marker',
        fields: ['city', 'country']
    }
];

// Navigation functions
const nextStep = () => {
    if (currentStep.value < steps.length - 1) {
        currentStep.value++;
    }
};

const prevStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
    }
};

const goToStep = (stepIndex: number) => {
    currentStep.value = stepIndex;
};

// Check if current step is valid
const isCurrentStepValid = computed(() => {
    const currentStepFields = steps[currentStep.value].fields;
    return currentStepFields.every((field) => {
        const value = companyInfo.value[field as keyof typeof companyInfo.value];
        if (field === 'name' || field === 'emailAddress') {
            return value && value.toString().trim() !== '';
        }
        return true; // Optional fields
    });
});

// Check if step has been completed
const isStepCompleted = (stepIndex: number) => {
    const stepFields = steps[stepIndex].fields;
    return stepFields.every((field) => {
        const value = companyInfo.value[field as keyof typeof companyInfo.value];
        if (field === 'name' || field === 'emailAddress') {
            return value && value.toString().trim() !== '';
        }
        return true;
    });
};

// Lifecycle
onMounted(() => {
    loadCompanyInfo();
});

// Expose methods for parent components
defineExpose({
    loadCompanyInfo,
    saveCompanyInfo,
    resetForm,
    hasUnsavedChanges: computed(() => hasUnsavedChanges.value),
    isFormValid: computed(() => isFormValid.value)
});
</script>

<template>
    <div class="company-info-update">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
            <ProgressSpinner />
        </div>

        <!-- Main Content -->
        <div v-else class="max-w-4xl mx-auto">
            <!-- Header Section (optional) -->
            <div v-if="showHeaderSection" class="text-center mb-8">
                <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">Company Information</h1>
                <p class="text-surface-600 dark:text-surface-400 text-lg">Let's set up your company profile in a few simple steps</p>
            </div>

            <!-- Step Progress Indicator -->
            <div class="mb-8">
                <div class="flex items-center justify-between relative">
                    <!-- Progress Line -->
                    <div class="absolute top-6 left-0 w-full h-0.5 bg-surface-200 dark:bg-surface-700 z-0"></div>
                    <div class="absolute top-6 left-0 h-0.5 bg-primary-500 z-10 transition-all duration-300" :style="{ width: `${(currentStep / (steps.length - 1)) * 100}%` }"></div>

                    <!-- Step Indicators -->
                    <div v-for="(step, index) in steps" :key="step.id" class="relative z-20 flex flex-col items-center cursor-pointer group" @click="goToStep(index)">
                        <!-- Step Circle -->
                        <div
                            class="w-12 h-12 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-200"
                            :class="{
                                'bg-primary-500 text-white': index === currentStep,
                                'bg-green-500 text-white': index < currentStep && isStepCompleted(index),
                                'bg-surface-200 dark:bg-surface-700 text-surface-600 dark:text-surface-400': index > currentStep,
                                'hover:bg-primary-100 dark:hover:bg-primary-900': index !== currentStep
                            }"
                        >
                            <i v-if="index < currentStep && isStepCompleted(index)" class="pi pi-check text-lg"></i>
                            <i v-else :class="step.icon" class="text-lg"></i>
                        </div>

                        <!-- Step Label -->
                        <div class="mt-2 text-center">
                            <div
                                class="text-sm font-medium transition-colors"
                                :class="{
                                    'text-primary-600 dark:text-primary-400': index === currentStep,
                                    'text-green-600 dark:text-green-400': index < currentStep && isStepCompleted(index),
                                    'text-surface-600 dark:text-surface-400': index > currentStep
                                }"
                            >
                                {{ step.title }}
                            </div>
                            <div class="text-xs text-surface-500 dark:text-surface-400 mt-1 max-w-24">
                                {{ step.description }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step Content -->
            <div class="bg-surface-card rounded-xl shadow-lg border border-surface-200 dark:border-surface-700">
                <!-- Step Header -->
                <div class="p-6 border-b border-surface-200 dark:border-surface-700">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 rounded-lg bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                            <i :class="steps[currentStep].icon" class="text-primary-600 dark:text-primary-400 text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                                {{ steps[currentStep].title }}
                            </h2>
                            <p class="text-surface-600 dark:text-surface-400 text-sm">
                                {{ steps[currentStep].description }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Step Content -->
                <div class="p-6">
                    <!-- Step 1: Company Basics -->
                    <div v-if="currentStep === 0" class="space-y-6">
                        <!-- Company Name -->
                        <div class="field">
                            <label for="name" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Company Name <span class="text-red-500">*</span> </label>
                            <IconField>
                                <InputIcon class="pi pi-building" />
                                <InputText id="name" v-model.trim="companyInfo.name" :class="{ 'p-invalid': errors.name && submitted }" :readonly="isReadonly" placeholder="Enter your company name" @input="handleFormUpdate" fluid size="large" />
                            </IconField>
                            <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
                        </div>

                        <!-- Industry -->
                        <div class="field">
                            <label for="industry" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Industry </label>
                            <IconField>
                                <InputIcon class="pi pi-briefcase" />
                                <Dropdown
                                    id="industry"
                                    v-model="companyInfo.industry"
                                    :options="INDUSTRY_OPTIONS"
                                    optionLabel="label"
                                    optionValue="value"
                                    :readonly="isReadonly"
                                    placeholder="Select your industry"
                                    @change="handleFormUpdate"
                                    fluid
                                    size="large"
                                />
                            </IconField>
                        </div>

                        <!-- Company Description -->
                        <div class="field">
                            <label for="description" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Company Description </label>
                            <Textarea id="description" v-model="companyInfo.description" :readonly="isReadonly" placeholder="Tell us about your company..." rows="4" @input="handleFormUpdate" fluid class="resize-none" />
                            <small class="text-surface-500 dark:text-surface-400 text-xs mt-1"> Optional: Brief description of what your company does </small>
                        </div>
                    </div>

                    <!-- Step 2: Contact Information -->
                    <div v-if="currentStep === 1" class="space-y-6">
                        <!-- Email Address -->
                        <div class="field">
                            <label for="email" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Primary Email <span class="text-red-500">*</span> </label>
                            <IconField>
                                <InputIcon class="pi pi-envelope" />
                                <InputText
                                    id="email"
                                    v-model.trim="companyInfo.emailAddress"
                                    :class="{ 'p-invalid': errors.emailAddress && submitted }"
                                    :readonly="isReadonly"
                                    placeholder="<EMAIL>"
                                    type="email"
                                    @input="handleFormUpdate"
                                    fluid
                                    size="large"
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.emailAddress && submitted">{{ errors.emailAddress }}</small>
                        </div>

                        <!-- Phone Number -->
                        <div class="field">
                            <label for="phone" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Phone Number </label>
                            <IconField>
                                <InputIcon class="pi pi-phone" />
                                <InputText id="phone" v-model.trim="companyInfo.phone" :class="{ 'p-invalid': errors.phone && submitted }" :readonly="isReadonly" placeholder="+****************" @input="handleFormUpdate" fluid size="large" />
                            </IconField>
                            <small class="p-error" v-if="errors.phone && submitted">{{ errors.phone }}</small>
                        </div>

                        <!-- Website -->
                        <div class="field">
                            <label for="website" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Website </label>
                            <IconField>
                                <InputIcon class="pi pi-globe" />
                                <InputText
                                    id="website"
                                    v-model.trim="companyInfo.website"
                                    :class="{ 'p-invalid': errors.website && submitted }"
                                    :readonly="isReadonly"
                                    placeholder="https://www.example.com"
                                    type="url"
                                    @input="handleFormUpdate"
                                    fluid
                                    size="large"
                                />
                            </IconField>
                            <small class="p-error" v-if="errors.website && submitted">{{ errors.website }}</small>
                            <small class="text-surface-500 dark:text-surface-400 text-xs mt-1"> Optional: Your company website URL </small>
                        </div>
                    </div>

                    <!-- Step 3: Location -->
                    <div v-if="currentStep === 2" class="space-y-6">
                        <!-- City -->
                        <div class="field">
                            <label for="city" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> City </label>
                            <IconField>
                                <InputIcon class="pi pi-map-marker" />
                                <InputText id="city" v-model.trim="companyInfo.city" :readonly="isReadonly" placeholder="Enter your city" @input="handleFormUpdate" fluid size="large" />
                            </IconField>
                        </div>

                        <!-- Country -->
                        <div class="field">
                            <label for="country" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Country </label>
                            <IconField>
                                <InputIcon class="pi pi-flag" />
                                <Dropdown
                                    id="country"
                                    v-model="companyInfo.country"
                                    :options="COUNTRY_OPTIONS"
                                    optionLabel="label"
                                    optionValue="value"
                                    :readonly="isReadonly"
                                    placeholder="Select your country"
                                    @change="handleFormUpdate"
                                    fluid
                                    size="large"
                                />
                            </IconField>
                        </div>

                        <!-- Completion Message -->
                        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mt-6">
                            <div class="flex items-center gap-3">
                                <i class="pi pi-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                                <div>
                                    <h3 class="font-semibold text-green-800 dark:text-green-200">Almost Done!</h3>
                                    <p class="text-green-700 dark:text-green-300 text-sm">Review your information and save your company profile.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Navigation Footer -->
                    <div class="px-6 py-4 bg-surface-50 dark:bg-surface-800 border-t border-surface-200 dark:border-surface-700 rounded-b-xl">
                        <div class="flex justify-between items-center">
                            <!-- Previous Button -->
                            <Button v-if="currentStep > 0" label="Previous" icon="pi pi-chevron-left" severity="secondary" outlined @click="prevStep" :disabled="isSaving" />
                            <div v-else></div>

                            <!-- Step Counter -->
                            <div class="text-sm text-surface-600 dark:text-surface-400">Step {{ currentStep + 1 }} of {{ steps.length }}</div>

                            <!-- Next/Save Button -->
                            <div class="flex gap-2">
                                <!-- Unsaved Changes Indicator -->
                                <div v-if="hasUnsavedChanges && !isReadonly" class="flex items-center gap-2 mr-4">
                                    <i class="pi pi-exclamation-triangle text-orange-500 text-sm"></i>
                                    <span class="text-orange-600 dark:text-orange-400 text-sm">Unsaved changes</span>
                                </div>

                                <!-- Reset Button -->
                                <Button v-if="hasUnsavedChanges && !isReadonly" label="Reset" icon="pi pi-refresh" severity="secondary" outlined @click="confirmReset" :disabled="isSaving" size="small" />

                                <!-- Next/Save Button -->
                                <Button v-if="currentStep < steps.length - 1" label="Next" icon="pi pi-chevron-right" iconPos="right" @click="nextStep" :disabled="!isCurrentStepValid || isSaving" />
                                <Button v-else label="Save Company Info" icon="pi pi-save" severity="success" @click="saveCompanyInfo(companyInfo)" :loading="isSaving" :disabled="!isFormValid || !hasUnsavedChanges" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Actions (for widget mode) -->
                <div v-if="!showHeaderSection && !isReadonly && showSaveButtons" class="flex justify-end gap-2 pt-4">
                    <Button v-if="hasUnsavedChanges" label="Reset" icon="pi pi-refresh" severity="secondary" outlined @click="confirmReset" :disabled="isSaving" />
                    <Button label="Save Changes" icon="pi pi-save" severity="success" @click="saveCompanyInfo(companyInfo)" :loading="isSaving" :disabled="!isFormValid || !hasUnsavedChanges" />
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.company-info-update {
    max-width: 1000px;
    margin: 0 auto;
    padding: 1rem;
}

/* Step progress styling */
.step-indicator {
    transition: all 0.3s ease;
}

.step-indicator.active {
    transform: scale(1.1);
}

.step-indicator.completed {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Form field styling */
.field {
    margin-bottom: 1.5rem;
}

.field label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: block;
    color: var(--text-color);
}

/* Input styling */
:deep(.p-inputtext),
:deep(.p-dropdown),
:deep(.p-textarea) {
    border-radius: 0.75rem;
    border: 2px solid var(--surface-border);
    transition: all 0.2s ease;
}

:deep(.p-inputtext:focus),
:deep(.p-dropdown:focus),
:deep(.p-textarea:focus) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

:deep(.p-inputtext.p-invalid),
:deep(.p-dropdown.p-invalid),
:deep(.p-textarea.p-invalid) {
    border-color: var(--red-500);
}

/* Button styling */
:deep(.p-button) {
    border-radius: 0.75rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
}

:deep(.p-button:hover) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Card styling */
.bg-surface-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.dark .bg-surface-card {
    background: rgba(30, 30, 30, 0.95);
}

@media (max-width: 768px) {
    .company-info-update {
        padding: 0.5rem;
    }

    .step-indicator {
        margin: 0 0.5rem;
    }

    .step-indicator .text-center {
        max-width: 60px;
    }

    .step-indicator .text-sm {
        font-size: 0.7rem;
    }

    .step-indicator .text-xs {
        display: none;
    }
}
</style>
