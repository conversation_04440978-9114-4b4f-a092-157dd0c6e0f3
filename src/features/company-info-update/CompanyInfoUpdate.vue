<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useTenantStore } from '@/entities/tenant/store';
import { useAuthStore } from '@/entities/auth';
import { storeToRefs } from 'pinia';
import { validateEmail, validateRequired, validatePhoneNumber, validateUrl, validatePostalCode, validateCompanyNumber, validateVatNumber, validateNumber } from '@/utils';
import { INDUSTRY_OPTIONS, COMPANY_SIZE_OPTIONS, COUNTRY_OPTIONS, STATUS_OPTIONS, SUBSCRIPTION_PLAN_OPTIONS } from './types';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import { CompanyInfoService } from './services/companyInfoService';

// Props
const props = defineProps<{
    tenantId?: string;
    readonly?: boolean;
    showHeader?: boolean;
    showSaveButton?: boolean; // Control whether to show save buttons
}>();

// Stores
const toast = useToast();
const tenantStore = useTenantStore();
const authStore = useAuthStore();
const { isLoading } = storeToRefs(tenantStore);

// Local state
const companyInfo = ref<Partial<Tenant>>({});
const isFormValid = ref(false);
const isSaving = ref(false);
const hasUnsavedChanges = ref(false);
const originalData = ref<Partial<Tenant>>({});
const submitted = ref(false);
const activeTab = ref(0);

// Form errors
const errors = ref({
    name: '',
    emailAddress: '',
    phone: '',
    website: '',
    industry: '',
    description: '',
    companyRegistrationNumber: '',
    vatNumber: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    alternativePhone: '',
    supportEmail: '',
    linkedinUrl: '',
    twitterUrl: '',
    facebookUrl: '',
    foundedYear: ''
});

// Computed properties
const currentTenantId = computed(() => {
    return props.tenantId || authStore.user?.tenantId || '';
});

const isReadonly = computed(() => {
    return props.readonly || false;
});

const showHeaderSection = computed(() => {
    return props.showHeader !== false;
});

const showSaveButtons = computed(() => {
    return props.showSaveButton !== false;
});

// Validation functions
const validateField = (field: keyof typeof errors.value, value: string, validator: (val: string) => string) => {
    const error = validator(value);
    errors.value[field] = error;
    return !error;
};

const validateRequiredField = (field: keyof typeof errors.value, value: string) => {
    return validateField(field, value, validateRequired);
};

const validateEmailField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateEmail);
};

const validatePhoneField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validatePhoneNumber);
};

const validateUrlField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateUrl);
};

const validateFormFields = () => {
    const validations = [
        validateRequiredField('name', companyInfo.value.name || ''),
        validateRequiredField('emailAddress', companyInfo.value.emailAddress || ''),
        validateEmailField('emailAddress', companyInfo.value.emailAddress || ''),
        validatePhoneField('phone', companyInfo.value.phone || ''),
        validateUrlField('website', companyInfo.value.website || ''),
        validateUrlField('linkedinUrl', companyInfo.value.linkedinUrl || ''),
        validateUrlField('twitterUrl', companyInfo.value.twitterUrl || ''),
        validateUrlField('facebookUrl', companyInfo.value.facebookUrl || ''),
        validateEmailField('supportEmail', companyInfo.value.supportEmail || ''),
        validatePhoneField('alternativePhone', companyInfo.value.alternativePhone || '')
    ];

    // Validate postal code if provided
    if (companyInfo.value.postalCode) {
        validations.push(validateField('postalCode', companyInfo.value.postalCode, validatePostalCode));
    }

    // Validate company registration number if provided
    if (companyInfo.value.companyRegistrationNumber) {
        validations.push(validateField('companyRegistrationNumber', companyInfo.value.companyRegistrationNumber, validateCompanyNumber));
    }

    // Validate VAT number if provided
    if (companyInfo.value.vatNumber) {
        validations.push(validateField('vatNumber', companyInfo.value.vatNumber, validateVatNumber));
    }

    // Validate founded year if provided
    if (companyInfo.value.foundedYear) {
        const currentYear = new Date().getFullYear();
        const yearStr = companyInfo.value.foundedYear.toString();
        const yearError = validateNumber(yearStr);
        if (yearError) {
            errors.value.foundedYear = yearError;
            validations.push(false);
        } else {
            const year = parseInt(yearStr);
            if (year < 1800 || year > currentYear) {
                errors.value.foundedYear = `Please enter a year between 1800 and ${currentYear}.`;
                validations.push(false);
            } else {
                errors.value.foundedYear = '';
                validations.push(true);
            }
        }
    }

    const isValid = validations.every(Boolean);
    isFormValid.value = isValid;
    return isValid;
};

// Methods
const loadCompanyInfo = async () => {
    if (!currentTenantId.value) {
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'No tenant ID available',
            life: 3000
        });
        return;
    }

    try {
        const data = await CompanyInfoService.getCompanyInfo(currentTenantId.value);
        companyInfo.value = { ...data };
        originalData.value = { ...data };
        hasUnsavedChanges.value = false;
    } catch (error) {
        console.error('Error loading company info:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load company information',
            life: 3000
        });
    }
};

const saveCompanyInfo = async (data?: Partial<Tenant>) => {
    submitted.value = true;
    const dataToSave = data || companyInfo.value;

    if (!validateFormFields()) {
        toast.add({
            severity: 'error',
            summary: 'Validation Error',
            detail: 'Please fix the errors in the form',
            life: 3000
        });
        return;
    }

    isSaving.value = true;

    try {
        // Convert to tenant format and save using existing updateTenant method
        const tenantData = CompanyInfoService.convertToTenantData(dataToSave);

        if (!currentTenantId.value) {
            currentTenantId.value = await tenantStore.createTenant(tenantData);
        } else {
            await tenantStore.updateTenant(currentTenantId.value, tenantData);
        }
        return;
        // Update local state
        originalData.value = { ...dataToSave };
        hasUnsavedChanges.value = false;
        submitted.value = false;

        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Company information updated successfully',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving company info:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save company information',
            life: 3000
        });
    } finally {
        isSaving.value = false;
    }
};

const handleFormUpdate = () => {
    // Check if there are unsaved changes
    const hasChanges = JSON.stringify(companyInfo.value) !== JSON.stringify(originalData.value);
    hasUnsavedChanges.value = hasChanges;

    // Validate form if submitted
    if (submitted.value) {
        validateFormFields();
    }
};

const resetForm = () => {
    companyInfo.value = { ...originalData.value };
    hasUnsavedChanges.value = false;
};

const confirmReset = () => {
    if (hasUnsavedChanges.value) {
        // Show confirmation dialog
        // For now, just reset directly
        resetForm();
        toast.add({
            severity: 'info',
            summary: 'Reset',
            detail: 'Form has been reset to original values',
            life: 3000
        });
    }
};

// Computed property to check if form has any errors
const hasErrors = computed(() => {
    return Object.values(errors.value).some((error) => error !== '');
});

// Tab items for organizing the form
const tabItems = [
    { label: 'Basic Information', icon: 'pi pi-building' },
    { label: 'Contact Details', icon: 'pi pi-phone' },
    { label: 'Business Details', icon: 'pi pi-briefcase' },
    { label: 'Online Presence', icon: 'pi pi-globe' }
];

// Lifecycle
onMounted(() => {
    loadCompanyInfo();
});

// Expose methods for parent components
defineExpose({
    loadCompanyInfo,
    saveCompanyInfo,
    resetForm,
    hasUnsavedChanges: computed(() => hasUnsavedChanges.value),
    isFormValid: computed(() => isFormValid.value)
});
</script>

<template>
    <div class="company-info-update">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
            <ProgressSpinner />
        </div>

        <!-- Main Content -->
        <div v-else class="space-y-6">
            <!-- Header Section (optional) -->
            <div v-if="showHeaderSection" class="bg-surface-card rounded-lg p-6 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">Company Settings</h1>
                        <p class="text-surface-600 dark:text-surface-400 text-lg">Manage your company information and business details</p>
                    </div>

                    <!-- Action Buttons -->
                    <div v-if="!isReadonly && showSaveButtons" class="flex gap-2">
                        <Button v-if="hasUnsavedChanges" label="Reset" icon="pi pi-refresh" severity="secondary" outlined @click="confirmReset" :disabled="isSaving" />
                        <Button label="Save Changes" icon="pi pi-save" severity="success" @click="saveCompanyInfo(companyInfo)" :loading="isSaving" :disabled="!isFormValid || !hasUnsavedChanges" />
                    </div>
                </div>

                <!-- Unsaved Changes Indicator -->
                <div v-if="hasUnsavedChanges && !isReadonly" class="mt-4">
                    <Message severity="warn" :closable="false">
                        <div class="flex items-center gap-2">
                            <i class="pi pi-exclamation-triangle"></i>
                            <span>You have unsaved changes</span>
                        </div>
                    </Message>
                </div>
            </div>

            <!-- Company Info Form -->
            <div class="bg-surface-card rounded-lg shadow-sm">
                <div class="company-info-form">
                    <!-- Header -->
                    <div class="flex justify-between items-center mb-6 p-6 pb-0">
                        <div>
                            <h2 class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">Company Information</h2>
                            <p class="text-surface-600 dark:text-surface-400">Update your company details and business information</p>
                        </div>
                    </div>

                    <!-- Tab Navigation -->
                    <TabView v-model:activeIndex="activeTab" class="company-info-tabs">
                        <!-- Basic Information Tab -->
                        <TabPanel>
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <i class="pi pi-building"></i>
                                    <span>Basic Information</span>
                                </div>
                            </template>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Company Name -->
                                <div class="field">
                                    <label for="name" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Company Name <span class="text-red-500">*</span> </label>
                                    <IconField>
                                        <InputIcon class="pi pi-building" />
                                        <InputText id="name" v-model.trim="companyInfo.name" :class="{ 'p-invalid': errors.name && submitted }" :readonly="isReadonly" placeholder="Enter company name" @input="handleFormUpdate" fluid />
                                    </IconField>
                                    <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
                                </div>

                                <!-- Industry -->
                                <div class="field">
                                    <label for="industry" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Industry </label>
                                    <IconField>
                                        <InputIcon class="pi pi-briefcase" />
                                        <Dropdown
                                            id="industry"
                                            v-model="companyInfo.industry"
                                            :options="INDUSTRY_OPTIONS"
                                            optionLabel="label"
                                            optionValue="value"
                                            :readonly="isReadonly"
                                            placeholder="Select industry"
                                            @change="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                </div>

                                <!-- Company Size -->
                                <div class="field">
                                    <label for="companySize" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Company Size </label>
                                    <IconField>
                                        <InputIcon class="pi pi-users" />
                                        <Dropdown
                                            id="companySize"
                                            v-model="companyInfo.companySize"
                                            :options="COMPANY_SIZE_OPTIONS"
                                            optionLabel="label"
                                            optionValue="value"
                                            :readonly="isReadonly"
                                            placeholder="Select company size"
                                            @change="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                </div>

                                <!-- Founded Year -->
                                <div class="field">
                                    <label for="foundedYear" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Founded Year </label>
                                    <IconField>
                                        <InputIcon class="pi pi-calendar" />
                                        <InputNumber
                                            id="foundedYear"
                                            v-model="companyInfo.foundedYear"
                                            :class="{ 'p-invalid': errors.foundedYear && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="e.g., 2020"
                                            :useGrouping="false"
                                            :min="1800"
                                            :max="new Date().getFullYear()"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.foundedYear && submitted">{{ errors.foundedYear }}</small>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="field mt-6">
                                <label for="description" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Company Description </label>
                                <Textarea id="description" v-model="companyInfo.description" :readonly="isReadonly" placeholder="Brief description of your company..." rows="4" @input="handleFormUpdate" fluid />
                            </div>
                        </TabPanel>

                        <!-- Contact Details Tab -->
                        <TabPanel>
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <i class="pi pi-phone"></i>
                                    <span>Contact Details</span>
                                </div>
                            </template>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Email -->
                                <div class="field">
                                    <label for="email" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Primary Email <span class="text-red-500">*</span> </label>
                                    <IconField>
                                        <InputIcon class="pi pi-envelope" />
                                        <InputText
                                            id="email"
                                            v-model.trim="companyInfo.emailAddress"
                                            :class="{ 'p-invalid': errors.emailAddress && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="<EMAIL>"
                                            type="email"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.emailAddress && submitted">{{ errors.emailAddress }}</small>
                                </div>

                                <!-- Support Email -->
                                <div class="field">
                                    <label for="supportEmail" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Support Email </label>
                                    <IconField>
                                        <InputIcon class="pi pi-headphones" />
                                        <InputText
                                            id="supportEmail"
                                            v-model.trim="companyInfo.supportEmail"
                                            :class="{ 'p-invalid': errors.supportEmail && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="<EMAIL>"
                                            type="email"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.supportEmail && submitted">{{ errors.supportEmail }}</small>
                                </div>

                                <!-- Phone -->
                                <div class="field">
                                    <label for="phone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Primary Phone </label>
                                    <IconField>
                                        <InputIcon class="pi pi-phone" />
                                        <InputText id="phone" v-model.trim="companyInfo.phone" :class="{ 'p-invalid': errors.phone && submitted }" :readonly="isReadonly" placeholder="+44 20 1234 5678" @input="handleFormUpdate" fluid />
                                    </IconField>
                                    <small class="p-error" v-if="errors.phone && submitted">{{ errors.phone }}</small>
                                </div>

                                <!-- Alternative Phone -->
                                <div class="field">
                                    <label for="alternativePhone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Alternative Phone </label>
                                    <IconField>
                                        <InputIcon class="pi pi-mobile" />
                                        <InputText
                                            id="alternativePhone"
                                            v-model.trim="companyInfo.alternativePhone"
                                            :class="{ 'p-invalid': errors.alternativePhone && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="+44 7123 456789"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.alternativePhone && submitted">{{ errors.alternativePhone }}</small>
                                </div>
                            </div>

                            <!-- Address Section -->
                            <Divider align="left">
                                <div class="inline-flex items-center">
                                    <i class="pi pi-map-marker mr-2"></i>
                                    <b>Address Information</b>
                                </div>
                            </Divider>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Address Line 1 -->
                                <div class="field md:col-span-2">
                                    <label for="address" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Address Line 1 </label>
                                    <IconField>
                                        <InputIcon class="pi pi-map-marker" />
                                        <InputText id="address" v-model.trim="companyInfo.address" :readonly="isReadonly" placeholder="Street address" @input="handleFormUpdate" fluid />
                                    </IconField>
                                </div>

                                <!-- Address Line 2 -->
                                <div class="field md:col-span-2">
                                    <label for="addressLine2" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Address Line 2 </label>
                                    <InputText id="addressLine2" v-model.trim="companyInfo.addressLine2" :readonly="isReadonly" placeholder="Apartment, suite, etc. (optional)" @input="handleFormUpdate" fluid />
                                </div>

                                <!-- City -->
                                <div class="field">
                                    <label for="city" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> City </label>
                                    <InputText id="city" v-model.trim="companyInfo.city" :readonly="isReadonly" placeholder="City" @input="handleFormUpdate" fluid />
                                </div>

                                <!-- State/Province -->
                                <div class="field">
                                    <label for="state" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> State/Province </label>
                                    <InputText id="state" v-model.trim="companyInfo.state" :readonly="isReadonly" placeholder="State or Province" @input="handleFormUpdate" fluid />
                                </div>

                                <!-- Postal Code -->
                                <div class="field">
                                    <label for="postalCode" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Postal Code </label>
                                    <InputText id="postalCode" v-model.trim="companyInfo.postalCode" :class="{ 'p-invalid': errors.postalCode && submitted }" :readonly="isReadonly" placeholder="Postal code" @input="handleFormUpdate" fluid />
                                    <small class="p-error" v-if="errors.postalCode && submitted">{{ errors.postalCode }}</small>
                                </div>

                                <!-- Country -->
                                <div class="field">
                                    <label for="country" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Country </label>
                                    <Dropdown id="country" v-model="companyInfo.country" :options="COUNTRY_OPTIONS" optionLabel="label" optionValue="value" :readonly="isReadonly" placeholder="Select country" @change="handleFormUpdate" fluid />
                                </div>
                            </div>
                        </TabPanel>

                        <!-- Business Details Tab -->
                        <TabPanel>
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <i class="pi pi-briefcase"></i>
                                    <span>Business Details</span>
                                </div>
                            </template>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Company Registration Number -->
                                <div class="field">
                                    <label for="companyRegistrationNumber" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Company Registration Number </label>
                                    <IconField>
                                        <InputIcon class="pi pi-id-card" />
                                        <InputText
                                            id="companyRegistrationNumber"
                                            v-model.trim="companyInfo.companyRegistrationNumber"
                                            :class="{ 'p-invalid': errors.companyRegistrationNumber && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="e.g., 12345678"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.companyRegistrationNumber && submitted">{{ errors.companyRegistrationNumber }}</small>
                                </div>

                                <!-- VAT Number -->
                                <div class="field">
                                    <label for="vatNumber" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> VAT Number </label>
                                    <IconField>
                                        <InputIcon class="pi pi-percentage" />
                                        <InputText id="vatNumber" v-model.trim="companyInfo.vatNumber" :class="{ 'p-invalid': errors.vatNumber && submitted }" :readonly="isReadonly" placeholder="e.g., GB123456789" @input="handleFormUpdate" fluid />
                                    </IconField>
                                    <small class="p-error" v-if="errors.vatNumber && submitted">{{ errors.vatNumber }}</small>
                                </div>

                                <!-- Status -->
                                <div class="field">
                                    <label for="status" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Status </label>
                                    <IconField>
                                        <InputIcon class="pi pi-circle" />
                                        <Dropdown id="status" v-model="companyInfo.status" :options="STATUS_OPTIONS" optionLabel="label" optionValue="value" :readonly="isReadonly" placeholder="Select status" @change="handleFormUpdate" fluid />
                                    </IconField>
                                </div>

                                <!-- Subscription Plan -->
                                <div class="field">
                                    <label for="subscriptionPlan" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Subscription Plan </label>
                                    <IconField>
                                        <InputIcon class="pi pi-credit-card" />
                                        <Dropdown
                                            id="subscriptionPlan"
                                            v-model="companyInfo.subscriptionPlan"
                                            :options="SUBSCRIPTION_PLAN_OPTIONS"
                                            optionLabel="label"
                                            optionValue="value"
                                            :readonly="isReadonly"
                                            placeholder="Select plan"
                                            @change="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                </div>

                                <!-- Timezone -->
                                <div class="field md:col-span-2">
                                    <label for="timezone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Timezone </label>
                                    <IconField>
                                        <InputIcon class="pi pi-clock" />
                                        <InputText id="timezone" v-model.trim="companyInfo.timezone" :readonly="isReadonly" placeholder="e.g., Europe/London" @input="handleFormUpdate" fluid />
                                    </IconField>
                                </div>
                            </div>
                        </TabPanel>

                        <!-- Online Presence Tab -->
                        <TabPanel>
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <i class="pi pi-globe"></i>
                                    <span>Online Presence</span>
                                </div>
                            </template>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Website -->
                                <div class="field md:col-span-2">
                                    <label for="website" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Website </label>
                                    <IconField>
                                        <InputIcon class="pi pi-globe" />
                                        <InputText
                                            id="website"
                                            v-model.trim="companyInfo.website"
                                            :class="{ 'p-invalid': errors.website && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="https://www.example.com"
                                            type="url"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.website && submitted">{{ errors.website }}</small>
                                </div>

                                <!-- LinkedIn URL -->
                                <div class="field">
                                    <label for="linkedinUrl" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> LinkedIn Profile </label>
                                    <IconField>
                                        <InputIcon class="pi pi-linkedin" />
                                        <InputText
                                            id="linkedinUrl"
                                            v-model.trim="companyInfo.linkedinUrl"
                                            :class="{ 'p-invalid': errors.linkedinUrl && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="https://linkedin.com/company/example"
                                            type="url"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.linkedinUrl && submitted">{{ errors.linkedinUrl }}</small>
                                </div>

                                <!-- Twitter URL -->
                                <div class="field">
                                    <label for="twitterUrl" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Twitter Profile </label>
                                    <IconField>
                                        <InputIcon class="pi pi-twitter" />
                                        <InputText
                                            id="twitterUrl"
                                            v-model.trim="companyInfo.twitterUrl"
                                            :class="{ 'p-invalid': errors.twitterUrl && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="https://twitter.com/example"
                                            type="url"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.twitterUrl && submitted">{{ errors.twitterUrl }}</small>
                                </div>

                                <!-- Facebook URL -->
                                <div class="field md:col-span-2">
                                    <label for="facebookUrl" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Facebook Page </label>
                                    <IconField>
                                        <InputIcon class="pi pi-facebook" />
                                        <InputText
                                            id="facebookUrl"
                                            v-model.trim="companyInfo.facebookUrl"
                                            :class="{ 'p-invalid': errors.facebookUrl && submitted }"
                                            :readonly="isReadonly"
                                            placeholder="https://facebook.com/example"
                                            type="url"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.facebookUrl && submitted">{{ errors.facebookUrl }}</small>
                                </div>
                            </div>
                        </TabPanel>
                    </TabView>
                </div>
            </div>

            <!-- Footer Actions (for widget mode) -->
            <div v-if="!showHeaderSection && !isReadonly && showSaveButtons" class="flex justify-end gap-2 pt-4">
                <Button v-if="hasUnsavedChanges" label="Reset" icon="pi pi-refresh" severity="secondary" outlined @click="confirmReset" :disabled="isSaving" />
                <Button label="Save Changes" icon="pi pi-save" severity="success" @click="saveCompanyInfo(companyInfo)" :loading="isSaving" :disabled="!isFormValid || !hasUnsavedChanges" />
            </div>
        </div>
    </div>
</template>

<style scoped>
.company-info-update {
    max-width: 1200px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .company-info-update {
        padding: 0 1rem;
    }
}
</style>
