import { updateTenantAPI, fetchTenantAPI } from '@/entities/tenant/api';
import type { Tenant } from '@/entities/tenant/model';

/**
 * Service for handling company information operations
 */
export class CompanyInfoService {
    /**
     * Convert CompanyInfo to Tenant format for API updates
     * @param companyInfo - The company information to convert
     * @returns Partial<Tenant> - Converted tenant data
     */
    static convertToTenantData(companyInfo: Partial<Tenant>): Partial<Tenant> {
        // Convert CompanyInfo to Tenant format for API
        const tenantData: Partial<Tenant> = {
            name: companyInfo.name,
            emailAddress: companyInfo.emailAddress,
            phone: companyInfo.phone || '',
            address: companyInfo.address || '',
            website: companyInfo.website,
            status: companyInfo.status || 'active',
            subscriptionPlan: companyInfo.subscriptionPlan || 'free',
            updatedAt: new Date()
        };

        // Add custom fields to the tenant data
        const customFields = {
            industry: companyInfo.industry,
            companySize: companyInfo.companySize,
            description: companyInfo.description,
            companyRegistrationNumber: companyInfo.companyRegistrationNumber,
            vatNumber: companyInfo.vatNumber,
            addressLine2: companyInfo.addressLine2,
            city: companyInfo.city,
            state: companyInfo.state,
            postalCode: companyInfo.postalCode,
            country: companyInfo.country,
            alternativePhone: companyInfo.alternativePhone,
            supportEmail: companyInfo.supportEmail,
            linkedinUrl: companyInfo.linkedinUrl,
            twitterUrl: companyInfo.twitterUrl,
            facebookUrl: companyInfo.facebookUrl,
            foundedYear: companyInfo.foundedYear,
            timezone: companyInfo.timezone
        };

        // Merge tenant data with custom fields
        const updateData = { ...tenantData, ...customFields };

        // Remove undefined values
        Object.keys(updateData).forEach((key) => {
            if (updateData[key as keyof typeof updateData] === undefined) {
                delete updateData[key as keyof typeof updateData];
            }
        });

        return updateData;
    }

    /**
     * Fetch company information from Firestore
     * @param tenantId - The tenant ID
     * @returns Promise<Tenant>
     */
    static async getCompanyInfo(tenantId: string): Promise<Tenant> {
        try {
            return await fetchTenantAPI(tenantId);
        } catch (error) {
            console.error('Error fetching company information:', error);
            throw new Error('Failed to fetch company information. Please try again.');
        }
    }

    /**
     * Validate company information before saving
     * @param companyInfo - The company information to validate
     * @returns Promise<{ isValid: boolean; errors: string[] }>
     */
    static async validateCompanyInfo(companyInfo: Partial<Tenant>): Promise<{ isValid: boolean; errors: string[] }> {
        const errors: string[] = [];

        // Required field validations
        if (!companyInfo.name?.trim()) {
            errors.push('Company name is required');
        }

        if (!companyInfo.emailAddress?.trim()) {
            errors.push('Email is required');
        } else {
            // Email format validation
            const emailAddressRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailAddressRegex.test(companyInfo.emailAddress)) {
                errors.push('Please enter a valid emailAddress address');
            }
        }

        // URL validations
        const urlFields = [
            { field: companyInfo.website, name: 'Website' },
            { field: companyInfo.linkedinUrl, name: 'LinkedIn URL' },
            { field: companyInfo.twitterUrl, name: 'Twitter URL' },
            { field: companyInfo.facebookUrl, name: 'Facebook URL' }
        ];

        urlFields.forEach(({ field, name }) => {
            if (field && field.trim()) {
                try {
                    new URL(field);
                } catch {
                    errors.push(`${name} must be a valid URL`);
                }
            }
        });

        // Phone number validations
        const phoneFields = [
            { field: companyInfo.phone, name: 'Phone number' },
            { field: companyInfo.alternativePhone, name: 'Alternative phone number' }
        ];

        phoneFields.forEach(({ field, name }) => {
            if (field && field.trim()) {
                const phoneRegex = /^\+?([0-9]{1,3})?[-. ]?(\([0-9]{3}\)|[0-9]{3})[-. ]?[0-9]{3}[-. ]?[0-9]{4}$/;
                if (!phoneRegex.test(field)) {
                    errors.push(`${name} must be a valid phone number`);
                }
            }
        });

        // Founded year validation
        if (companyInfo.foundedYear) {
            const currentYear = new Date().getFullYear();
            if (companyInfo.foundedYear < 1800 || companyInfo.foundedYear > currentYear) {
                errors.push(`Founded year must be between 1800 and ${currentYear}`);
            }
        }

        // VAT number validation
        if (companyInfo.vatNumber && companyInfo.vatNumber.trim()) {
            const vatRegex = /^[A-Za-z]{2}[A-Za-z0-9]{8,12}$/;
            if (!vatRegex.test(companyInfo.vatNumber)) {
                errors.push('VAT number must be in the format: Country code + 8-12 alphanumeric characters');
            }
        }

        // Company registration number validation
        if (companyInfo.companyRegistrationNumber && companyInfo.companyRegistrationNumber.trim()) {
            const companyRegex = /^[A-Za-z0-9]{6,12}$/;
            if (!companyRegex.test(companyInfo.companyRegistrationNumber)) {
                errors.push('Company registration number must be 6-12 alphanumeric characters');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
