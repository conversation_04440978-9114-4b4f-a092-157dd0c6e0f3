import { validateFeatures } from '@/shared/api/tenant';
import { useTenantStore } from '@/entities/tenant/store';
import type { EmailConfig, TemplateValidation, EmailValidationForm } from '../types';
import type { Tenant } from '@/entities/tenant/model';

/**
 * Service class for email numbers-prompts-onboarding functionality
 */
export class EmailOnboardingService {
    private tenantStore = useTenantStore();

    /**
     * Validates an email address format
     */
    static validateEmailFormat(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validates email template for required placeholders
     */
    static validateEmailTemplate(template: string): TemplateValidation {
        const hasContentPlaceholder = template.includes('{{content}}');
        const errors: string[] = [];

        if (!template.trim()) {
            errors.push('Template cannot be empty');
        }

        if (!hasContentPlaceholder) {
            errors.push('Template must include {{content}} placeholder for chatbot responses');
        }

        return {
            isValid: errors.length === 0,
            hasContentPlaceholder,
            errors
        };
    }

    /**
     * Checks if email exists in tenant's email list
     */
    static isEmailInList(email: string, emails: string[] = []): boolean {
        return emails.includes(email.toLowerCase());
    }

    /**
     * Adds email to tenant's email list if not already present
     */
    static addEmailToList(email: string, emails: string[] = []): string[] {
        const normalizedEmail = email.toLowerCase();
        if (!this.isEmailInList(normalizedEmail, emails)) {
            return [...emails, normalizedEmail];
        }
        return emails;
    }

    /**
     * Updates tenant email configuration
     */
    async updateTenantEmailConfig(tenantId: string, emailConfig: Partial<EmailConfig>): Promise<void> {
        try {
            const currentTenant = await this.tenantStore.getTenant(tenantId);
            if (!currentTenant) {
                throw new Error('Tenant not found');
            }

            const updatedEmailConfig = {
                ...currentTenant.email,
                ...emailConfig
            };

            await this.tenantStore.updateTenant(tenantId, {
                email: updatedEmailConfig
            });
        } catch (error) {
            console.error('Error updating tenant email config:', error);
            throw error;
        }
    }

    /**
     * Saves email address to tenant configuration
     */
    async saveEmailAddress(tenantId: string, email: string): Promise<void> {
        try {
            const currentTenant = await this.tenantStore.getTenant(tenantId);
            if (!currentTenant) {
                throw new Error('Tenant not found');
            }

            const currentEmails = currentTenant.email?.emails || [];
            const updatedEmails = EmailOnboardingService.addEmailToList(email, currentEmails);

            const emailConfig: Partial<EmailConfig> = {
                defaultEmail: email,
                emails: updatedEmails,
                validated: false // Reset validation when email changes
            };

            await this.updateTenantEmailConfig(tenantId, emailConfig);
        } catch (error) {
            console.error('Error saving email address:', error);
            throw error;
        }
    }

    /**
     * Triggers email validation process
     */
    async validateEmail(email: string): Promise<void> {
        try {
            await validateFeatures({
                feature: 'email',
                toValidate: email
            });
        } catch (error) {
            console.error('Error validating email:', error);
            throw error;
        }
    }

    /**
     * Saves email template to tenant configuration
     */
    async saveEmailTemplate(tenantId: string, template: string): Promise<void> {
        try {
            const validation = EmailOnboardingService.validateEmailTemplate(template);
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            const emailConfig: Partial<EmailConfig> = {
                template
            };

            await this.updateTenantEmailConfig(tenantId, emailConfig);
        } catch (error) {
            console.error('Error saving email template:', error);
            throw error;
        }
    }

    /**
     * Gets the forwarding email address for a tenant
     */
    static getForwardingEmail(tenantId: string): string {
        return `${tenantId}@lifttai.liftt.co.uk`;
    }

    /**
     * Validates email form data
     */
    static validateEmailForm(email: string): EmailValidationForm {
        const isValid = this.validateEmailFormat(email);
        return {
            email,
            isValid,
            error: isValid ? undefined : 'Please enter a valid email address'
        };
    }

    /**
     * Checks if numbers-prompts-onboarding is complete
     */
    static isOnboardingComplete(tenant: Tenant): boolean {
        const email = tenant.email;
        return !!(email?.defaultEmail && email?.emails?.length && email?.validated && email?.template);
    }

    /**
     * Gets the current numbers-prompts-onboarding step based on tenant data
     */
    static getCurrentStep(tenant: Tenant): number {
        const email = tenant.email;

        if (!email?.defaultEmail) {
            return 1; // Email input step
        }

        if (!email?.validated) {
            return 3; // Validation step (skip instructions if email is set)
        }

        if (!email?.template) {
            return 4; // Template step
        }

        return 5; // Complete
    }
}
