// Main feature component
export { default as EmailOnboarding } from './EmailOnboarding.vue';

// Example component
export { default as EmailOnboardingExample } from './examples/EmailOnboardingExample.vue';

// Services
export { EmailOnboardingService } from './services/emailOnboardingService';

// Types
export type {
    EmailConfig,
    OnboardingStep,
    EmailValidationForm,
    EmailProvider,
    TemplateValidation,
    OnboardingState
} from './types';

export {
    EMAIL_PROVIDERS,
    DEFAULT_EMAIL_TEMPLATE
} from './types';
