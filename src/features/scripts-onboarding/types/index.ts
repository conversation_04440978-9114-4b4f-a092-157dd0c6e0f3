import type { Timestamp } from 'firebase/firestore';
import type { LeadConfigItem } from '@/features/lead-configuration-onboarding/types';

// Script Configuration Interface
export interface ScriptConfig {
    content: string;
    variables: string[];
    lastUpdated: Timestamp | Date;
    isValid?: boolean;
    validationErrors?: string[];
}

// Lead Action Script Interface
export interface LeadActionScript {
    actionKey: string;
    actionConfig: LeadConfigItem;
    script: ScriptConfig;
    isRequired: boolean;
    isCompleted: boolean;
}

// Scripts Collection Interface
export interface ScriptsCollection {
    [actionKey: string]: ScriptConfig;
}

// Extended Tenant Properties for Scripts
export interface TenantScriptConfigs {
    scripts?: ScriptsCollection;
}

// Onboarding Step Interface
export interface ScriptOnboardingStep {
    id: number;
    title: string;
    description: string;
    icon: string;
    completed: boolean;
    active: boolean;
    actionKey?: string; // For script-specific steps
}

// Auto-save Configuration
export interface ScriptAutoSaveConfig {
    enabled: boolean;
    debounceMs: number;
    tenantId: string;
    maxRetries: number;
    retryDelayMs: number;
}

// Auto-save Status
export type ScriptAutoSaveStatus = 'idle' | 'saving' | 'saved' | 'error';

// Script Editor Props
export interface ScriptEditorProps {
    modelValue: string;
    actionKey: string;
    actionConfig: LeadConfigItem;
    placeholder?: string;
    disabled?: boolean;
    autoSave?: ScriptAutoSaveConfig;
    showVariableHelp?: boolean;
}

// Script Variables Help Props
export interface ScriptVariablesHelpProps {
    actionKey: string;
    visible: boolean;
}

// Script Validation Result
export interface ScriptValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    variables: string[];
}

// Available Script Variables
export interface ScriptVariable {
    key: string;
    label: string;
    description: string;
    example: string;
    category: 'customer' | 'agent' | 'business' | 'action' | 'system';
}

// Script Template
export interface ScriptTemplate {
    actionKey: string;
    title: string;
    description: string;
    content: string;
    variables: string[];
}

// Component Events
export interface ScriptEditorEvents {
    'update:modelValue': [value: string];
    'script-change': [script: ScriptConfig];
    'validation-change': [result: ScriptValidationResult];
    'save-status-change': [status: ScriptAutoSaveStatus];
}

// Onboarding Events
export interface ScriptOnboardingEvents {
    complete: [scripts: ScriptsCollection];
    'step-change': [step: ScriptOnboardingStep];
    'progress-change': [progress: number];
}

// Default Script Variables
export const DEFAULT_SCRIPT_VARIABLES: ScriptVariable[] = [
    {
        key: 'customer_name',
        label: 'Customer Name',
        description: 'The name of the customer/lead',
        example: 'John Smith',
        category: 'customer'
    },
    {
        key: 'agent_name',
        label: 'Agent Name',
        description: 'The name of the AI agent or representative',
        example: 'Sarah',
        category: 'agent'
    },
    {
        key: 'company_name',
        label: 'Company Name',
        description: 'Your business/company name',
        example: 'ABC Services Ltd',
        category: 'business'
    },
    {
        key: 'customer_phone',
        label: 'Customer Phone',
        description: 'Customer phone number',
        example: '+44 7123 456789',
        category: 'customer'
    },
    {
        key: 'customer_email',
        label: 'Customer Email',
        description: 'Customer email address',
        example: '<EMAIL>',
        category: 'customer'
    },
    {
        key: 'conversation_summary',
        label: 'Conversation Summary',
        description: 'Summary of previous conversations',
        example: 'Customer inquired about kitchen installation',
        category: 'system'
    },
    {
        key: 'appointment_date',
        label: 'Appointment Date',
        description: 'Scheduled appointment date and time',
        example: 'Monday, March 15th at 2:00 PM',
        category: 'action'
    },
    {
        key: 'quote_amount',
        label: 'Quote Amount',
        description: 'Quoted price or estimate',
        example: '£2,500',
        category: 'action'
    },
    {
        key: 'service_type',
        label: 'Service Type',
        description: 'Type of service or product',
        example: 'Kitchen Installation',
        category: 'business'
    }
];

// Default Script Templates
export const DEFAULT_SCRIPT_TEMPLATES: ScriptTemplate[] = [
    {
        actionKey: 'inquired',
        title: 'Initial Inquiry Response',
        description: 'Script for responding to initial customer inquiries',
        content: `Hello [customer_name],

Thank you for your inquiry about our services at [company_name]. I'm [agent_name], and I'm here to help you with your [service_type] needs.

Based on our conversation: [conversation_summary]

I'd be happy to provide you with more information and discuss how we can help. Would you like to schedule a consultation to discuss your requirements in detail?

Best regards,
[agent_name]
[company_name]`,
        variables: ['customer_name', 'company_name', 'agent_name', 'service_type', 'conversation_summary']
    },
    {
        actionKey: 'quoted',
        title: 'Quote Follow-up',
        description: 'Script for following up after providing a quote',
        content: `Hi [customer_name],

I hope you're well. I wanted to follow up on the quote I provided for your [service_type] project.

Quote Summary: [quote_amount]

Do you have any questions about the quote or would you like to discuss any aspects of the project? I'm here to help clarify anything and ensure you have all the information you need to make a decision.

Please feel free to reach out if you'd like to move forward or if you need any adjustments to the proposal.

Best regards,
[agent_name]
[company_name]`,
        variables: ['customer_name', 'service_type', 'quote_amount', 'agent_name', 'company_name']
    },
    {
        actionKey: 'booked',
        title: 'Appointment Confirmation',
        description: 'Script for confirming booked appointments',
        content: `Hello [customer_name],

Great news! Your appointment has been confirmed for [appointment_date].

Appointment Details:
- Service: [service_type]
- Date & Time: [appointment_date]
- Contact: [customer_phone]

Please let me know if you need to reschedule or if you have any questions before our meeting. I'm looking forward to working with you!

Best regards,
[agent_name]
[company_name]`,
        variables: ['customer_name', 'appointment_date', 'service_type', 'customer_phone', 'agent_name', 'company_name']
    },
    {
        actionKey: 'converted',
        title: 'Conversion Confirmation',
        description: 'Script for confirming successful conversions',
        content: `Dear [customer_name],

Thank you for choosing [company_name] for your [service_type] needs! We're excited to work with you.

Next Steps:
- We'll be in touch within 24 hours to confirm project details
- Our team will contact you at [customer_phone] to schedule the work
- You'll receive a confirmation email at [customer_email]

If you have any immediate questions, please don't hesitate to reach out.

Welcome to the [company_name] family!

Best regards,
[agent_name]
[company_name]`,
        variables: ['customer_name', 'company_name', 'service_type', 'customer_phone', 'customer_email', 'agent_name']
    }
];

// Onboarding Steps Configuration
export const SCRIPT_ONBOARDING_STEPS: ScriptOnboardingStep[] = [
    {
        id: 1,
        title: 'Welcome',
        description: 'Learn about AI scripts and their benefits',
        icon: 'pi pi-info-circle',
        completed: false,
        active: true
    },
    {
        id: 2,
        title: 'Script Creation',
        description: 'Create scripts for your lead actions',
        icon: 'pi pi-file-edit',
        completed: false,
        active: false
    },
    {
        id: 3,
        title: 'Review & Test',
        description: 'Review your scripts and test variables',
        icon: 'pi pi-check-circle',
        completed: false,
        active: false
    },
    {
        id: 4,
        title: 'Complete',
        description: 'Your AI scripts are ready to use',
        icon: 'pi pi-verified',
        completed: false,
        active: false
    }
];

// Validation Rules
export const SCRIPT_VALIDATION_RULES = {
    minLength: 10,
    maxLength: 5000,
    requiredVariables: ['customer_name', 'agent_name'],
    forbiddenPatterns: [/\[(?!customer_name|agent_name|company_name|customer_phone|customer_email|conversation_summary|appointment_date|quote_amount|service_type)\w+\]/g]
};
