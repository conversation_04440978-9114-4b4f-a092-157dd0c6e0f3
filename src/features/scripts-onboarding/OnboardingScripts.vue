<template>
    <div class="scripts-onboarding">
        <!-- Header -->
        <div class="onboarding-header mb-4">
            <div class="flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-3xl font-bold text-900 mb-2">AI Scripts Setup</h1>
                    <p class="text-600 text-lg">
                        Create personalized scripts for your AI to use when communicating with leads
                    </p>
                </div>
                <div class="flex align-items-center gap-2">
                    <Button
                        v-if="currentStep > 0"
                        icon="pi pi-arrow-left"
                        severity="secondary"
                        @click="previousStep"
                        :disabled="isLoading"
                    />
                    <Button
                        icon="pi pi-times"
                        severity="secondary"
                        text
                        @click="$emit('close')"
                        v-tooltip.left="'Close onboarding'"
                    />
                </div>
            </div>
        </div>

        <!-- Progress Steps -->
        <div class="progress-steps mb-6">
            <div class="steps-container">
                <div
                    v-for="(step, index) in onboardingSteps"
                    :key="step.id"
                    class="step-item"
                    :class="{
                        'active': index === currentStep,
                        'completed': step.completed,
                        'disabled': index > currentStep && !step.completed
                    }"
                >
                    <div class="step-indicator">
                        <div class="step-circle">
                            <i v-if="step.completed" class="pi pi-check"></i>
                            <span v-else>{{ index + 1 }}</span>
                        </div>
                        <div v-if="index < onboardingSteps.length - 1" class="step-connector"></div>
                    </div>
                    <div class="step-content">
                        <h3 class="step-title">{{ step.title }}</h3>
                        <p class="step-description">{{ step.description }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step Content -->
        <div class="step-content-area">
            <!-- Step 1: Welcome -->
            <div v-if="currentStep === 0" class="welcome-step">
                <Card class="text-center">
                    <template #content>
                        <div class="welcome-content py-6">
                            <div class="welcome-icon mb-4">
                                <i class="pi pi-sparkles text-6xl text-primary"></i>
                            </div>
                            <h2 class="text-2xl font-semibold mb-3">Welcome to AI Scripts!</h2>
                            <p class="text-600 text-lg mb-4 max-w-30rem mx-auto">
                                Let's create personalized scripts that your AI will use to communicate 
                                with leads based on their actions and status.
                            </p>
                            
                            <div class="benefits-preview grid max-w-40rem mx-auto">
                                <div class="col-4">
                                    <div class="benefit-item">
                                        <i class="pi pi-clock text-2xl text-blue-500 mb-2"></i>
                                        <h4 class="font-semibold text-sm">Save Time</h4>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="benefit-item">
                                        <i class="pi pi-heart text-2xl text-red-500 mb-2"></i>
                                        <h4 class="font-semibold text-sm">Personal Touch</h4>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="benefit-item">
                                        <i class="pi pi-chart-line text-2xl text-green-500 mb-2"></i>
                                        <h4 class="font-semibold text-sm">Better Results</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </Card>
            </div>

            <!-- Step 2: Script Creation -->
            <div v-else-if="currentStep === 1" class="script-creation-step">
                <LeadActionsScriptManager
                    :disabled="isLoading"
                    :auto-save="true"
                    @scripts-change="onScriptsChange"
                    @progress-change="onProgressChange"
                    @configure-lead-actions="onConfigureLeadActions"
                    @continue="nextStep"
                />
            </div>

            <!-- Step 3: Review & Test -->
            <div v-else-if="currentStep === 2" class="review-step">
                <Card>
                    <template #header>
                        <div class="card-header p-4">
                            <h2 class="text-xl font-semibold mb-2">Review Your Scripts</h2>
                            <p class="text-600">Review and test your scripts before completing the setup</p>
                        </div>
                    </template>
                    
                    <template #content>
                        <div class="review-content">
                            <!-- Scripts Summary -->
                            <div class="scripts-summary mb-4">
                                <div class="flex justify-content-between align-items-center mb-3">
                                    <h3 class="font-semibold">Scripts Created: {{ Object.keys(currentScripts).length }}</h3>
                                    <Button
                                        label="Edit Scripts"
                                        icon="pi pi-pencil"
                                        severity="secondary"
                                        @click="previousStep"
                                    />
                                </div>
                                
                                <div class="scripts-list">
                                    <div
                                        v-for="(script, actionKey) in currentScripts"
                                        :key="actionKey"
                                        class="script-summary-item p-3 border-1 border-200 border-round mb-2"
                                    >
                                        <div class="flex justify-content-between align-items-start">
                                            <div class="flex-1">
                                                <h4 class="font-semibold mb-1 capitalize">{{ actionKey }}</h4>
                                                <p class="text-600 text-sm line-height-3">
                                                    {{ getScriptPreview(script.content) }}
                                                </p>
                                                <div class="mt-2">
                                                    <span class="text-xs text-500">Variables: </span>
                                                    <Tag
                                                        v-for="variable in script.variables"
                                                        :key="variable"
                                                        :value="`[${variable}]`"
                                                        severity="info"
                                                        class="text-xs mr-1"
                                                    />
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <i 
                                                    :class="script.isValid !== false ? 'pi pi-check-circle text-green-500' : 'pi pi-exclamation-triangle text-red-500'"
                                                ></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Variables -->
                            <div class="test-section">
                                <h3 class="font-semibold mb-3">Test Your Scripts</h3>
                                <p class="text-600 text-sm mb-3">
                                    Enter test values to see how your scripts will look with real data:
                                </p>
                                
                                <div class="test-variables grid">
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText
                                                id="test-customer-name"
                                                v-model="testVariables.customer_name"
                                                class="w-full"
                                            />
                                            <label for="test-customer-name">Customer Name</label>
                                        </FloatLabel>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText
                                                id="test-agent-name"
                                                v-model="testVariables.agent_name"
                                                class="w-full"
                                            />
                                            <label for="test-agent-name">Agent Name</label>
                                        </FloatLabel>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText
                                                id="test-company-name"
                                                v-model="testVariables.company_name"
                                                class="w-full"
                                            />
                                            <label for="test-company-name">Company Name</label>
                                        </FloatLabel>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText
                                                id="test-service-type"
                                                v-model="testVariables.service_type"
                                                class="w-full"
                                            />
                                            <label for="test-service-type">Service Type</label>
                                        </FloatLabel>
                                    </div>
                                </div>

                                <!-- Script Preview -->
                                <div v-if="selectedPreviewScript" class="script-preview mt-4">
                                    <div class="flex justify-content-between align-items-center mb-2">
                                        <h4 class="font-semibold">Preview:</h4>
                                        <Dropdown
                                            v-model="selectedPreviewScript"
                                            :options="previewOptions"
                                            option-label="label"
                                            option-value="key"
                                            placeholder="Select script to preview"
                                            class="w-12rem"
                                        />
                                    </div>
                                    <div class="preview-content p-3 bg-blue-50 border-round border-1 border-blue-200">
                                        <pre class="text-sm text-700 m-0 whitespace-pre-wrap">{{ getPreviewContent() }}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </Card>
            </div>

            <!-- Step 4: Complete -->
            <div v-else-if="currentStep === 3" class="complete-step">
                <Card class="text-center">
                    <template #content>
                        <div class="complete-content py-6">
                            <div class="success-icon mb-4">
                                <i class="pi pi-check-circle text-6xl text-green-500"></i>
                            </div>
                            <h2 class="text-2xl font-semibold mb-3">Scripts Setup Complete!</h2>
                            <p class="text-600 text-lg mb-4 max-w-30rem mx-auto">
                                Your AI scripts are now ready to use. Your AI will automatically use these 
                                scripts when communicating with leads based on their actions.
                            </p>
                            
                            <div class="completion-stats grid max-w-30rem mx-auto mb-4">
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="text-2xl font-bold text-primary">{{ Object.keys(currentScripts).length }}</div>
                                        <div class="text-sm text-600">Scripts Created</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="text-2xl font-bold text-green-500">{{ totalVariables }}</div>
                                        <div class="text-sm text-600">Variables Used</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </Card>
            </div>
        </div>

        <!-- Navigation Footer -->
        <div class="navigation-footer mt-6 flex justify-content-between align-items-center">
            <div class="step-info">
                <span class="text-sm text-600">
                    Step {{ currentStep + 1 }} of {{ onboardingSteps.length }}
                </span>
            </div>
            
            <div class="navigation-buttons flex gap-2">
                <Button
                    v-if="currentStep > 0 && currentStep < 3"
                    label="Previous"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    @click="previousStep"
                    :disabled="isLoading"
                />
                <Button
                    v-if="currentStep < 2"
                    label="Next"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    @click="nextStep"
                    :disabled="isLoading || (currentStep === 1 && !canProceedFromScripts)"
                />
                <Button
                    v-else-if="currentStep === 2"
                    label="Complete Setup"
                    icon="pi pi-check"
                    iconPos="right"
                    @click="completeOnboarding"
                    :disabled="isLoading"
                />
                <Button
                    v-else-if="currentStep === 3"
                    label="Finish"
                    icon="pi pi-check"
                    @click="$emit('complete', currentScripts)"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import LeadActionsScriptManager from './components/LeadActionsScriptManager.vue';
import { SCRIPT_ONBOARDING_STEPS } from './types';
import type { 
    ScriptsCollection, 
    ScriptOnboardingStep,
    ScriptOnboardingEvents 
} from './types';

// Emits
const emit = defineEmits<ScriptOnboardingEvents & {
    'close': [];
}>();

// Composables
const toast = useToast();
const authStore = useAuthStore();

// State
const currentStep = ref(0);
const isLoading = ref(false);
const currentScripts = ref<ScriptsCollection>({});
const scriptProgress = ref(0);

const onboardingSteps = ref<ScriptOnboardingStep[]>([...SCRIPT_ONBOARDING_STEPS]);

// Test variables for preview
const testVariables = ref({
    customer_name: 'John Smith',
    agent_name: 'Sarah',
    company_name: 'Your Company',
    service_type: 'Kitchen Installation'
});

const selectedPreviewScript = ref<string | null>(null);

// Computed
const canProceedFromScripts = computed(() => {
    return Object.keys(currentScripts.value).length > 0 && scriptProgress.value >= 50;
});

const previewOptions = computed(() => {
    return Object.keys(currentScripts.value).map(key => ({
        key,
        label: key.charAt(0).toUpperCase() + key.slice(1)
    }));
});

const totalVariables = computed(() => {
    const allVariables = new Set<string>();
    Object.values(currentScripts.value).forEach(script => {
        script.variables.forEach(variable => allVariables.add(variable));
    });
    return allVariables.size;
});

// Methods
const nextStep = () => {
    if (currentStep.value < onboardingSteps.value.length - 1) {
        onboardingSteps.value[currentStep.value].completed = true;
        currentStep.value++;
        onboardingSteps.value[currentStep.value].active = true;
        
        emit('step-change', onboardingSteps.value[currentStep.value]);
        
        // Set default preview script when entering review step
        if (currentStep.value === 2 && Object.keys(currentScripts.value).length > 0) {
            selectedPreviewScript.value = Object.keys(currentScripts.value)[0];
        }
    }
};

const previousStep = () => {
    if (currentStep.value > 0) {
        onboardingSteps.value[currentStep.value].active = false;
        currentStep.value--;
        onboardingSteps.value[currentStep.value].active = true;
        
        emit('step-change', onboardingSteps.value[currentStep.value]);
    }
};

const completeOnboarding = () => {
    onboardingSteps.value[currentStep.value].completed = true;
    nextStep();
};

const onScriptsChange = (scripts: ScriptsCollection) => {
    currentScripts.value = scripts;
    emit('scripts-change', scripts);
};

const onProgressChange = (progress: number) => {
    scriptProgress.value = progress;
    emit('progress-change', progress);
};

const onConfigureLeadActions = () => {
    toast.add({
        severity: 'info',
        summary: 'Configure Lead Actions',
        detail: 'Please configure your lead actions first in the settings',
        life: 5000
    });
};

const getScriptPreview = (content: string): string => {
    const maxLength = 100;
    const stripped = content.replace(/<[^>]*>/g, '').trim();
    return stripped.length > maxLength ? stripped.substring(0, maxLength) + '...' : stripped;
};

const getPreviewContent = (): string => {
    if (!selectedPreviewScript.value || !currentScripts.value[selectedPreviewScript.value]) {
        return '';
    }
    
    let content = currentScripts.value[selectedPreviewScript.value].content;
    
    // Replace variables with test values
    Object.entries(testVariables.value).forEach(([key, value]) => {
        const regex = new RegExp(`\\[${key}\\]`, 'g');
        content = content.replace(regex, value);
    });
    
    // Remove HTML tags for preview
    return content.replace(/<[^>]*>/g, '');
};

// Lifecycle
onMounted(() => {
    onboardingSteps.value[0].active = true;
});
</script>

<style scoped>
.scripts-onboarding {
    @apply max-w-6xl mx-auto p-4;
}

.progress-steps {
    @apply border-bottom-1 border-200 pb-4;
}

.steps-container {
    @apply flex justify-content-center;
}

.step-item {
    @apply flex flex-column align-items-center text-center mx-3 transition-all duration-300;
    min-width: 120px;
}

.step-item.active {
    @apply text-primary;
}

.step-item.completed {
    @apply text-green-600;
}

.step-item.disabled {
    @apply text-400;
}

.step-indicator {
    @apply relative flex align-items-center mb-2;
}

.step-circle {
    @apply w-3rem h-3rem border-2 border-round-50 flex align-items-center justify-content-center font-semibold transition-all duration-300;
}

.step-item.active .step-circle {
    @apply border-primary bg-primary text-white;
}

.step-item.completed .step-circle {
    @apply border-green-600 bg-green-600 text-white;
}

.step-item.disabled .step-circle {
    @apply border-300 bg-100 text-400;
}

.step-connector {
    @apply absolute left-full top-50 w-6rem h-1 bg-200 transform -translate-y-50;
    margin-left: 0.5rem;
}

.step-item.completed .step-connector {
    @apply bg-green-600;
}

.step-content {
    @apply max-w-8rem;
}

.step-title {
    @apply text-sm font-semibold mb-1;
}

.step-description {
    @apply text-xs text-600;
}

.step-content-area {
    @apply min-h-30rem;
}

.welcome-content,
.complete-content {
    @apply max-w-40rem mx-auto;
}

.benefit-item,
.stat-item {
    @apply text-center;
}

.script-summary-item {
    @apply transition-all duration-200;
}

.script-summary-item:hover {
    @apply border-primary-200 shadow-1;
}

.test-variables {
    @apply mb-4;
}

.preview-content {
    @apply max-h-20rem overflow-y-auto;
}

.navigation-footer {
    @apply border-top-1 border-200 pt-4;
}

:deep(.p-card-header) {
    @apply p-0;
}

:deep(.p-card-content) {
    @apply pt-0;
}

@media (max-width: 768px) {
    .steps-container {
        @apply flex-column;
    }
    
    .step-item {
        @apply flex-row text-left mx-0 mb-3;
    }
    
    .step-indicator {
        @apply mr-3 mb-0;
    }
    
    .step-connector {
        @apply hidden;
    }
    
    .step-content {
        @apply max-w-none;
    }
}
</style>
