<template>
    <div class="script-onboarding-demo">
        <!-- De<PERSON> Header -->
        <div class="demo-header mb-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-900 mb-3">AI Scripts Onboarding Demo</h1>
                <p class="text-600 text-lg mb-4">
                    Experience the complete AI scripts setup process with interactive examples
                </p>
                <div class="demo-badges flex justify-content-center gap-2">
                    <Badge value="Interactive Demo" severity="info" />
                    <Badge value="No Data Saved" severity="warning" />
                    <Badge value="Full Feature Preview" severity="success" />
                </div>
            </div>
        </div>

        <!-- Demo Controls -->
        <div class="demo-controls mb-4">
            <Card>
                <template #content>
                    <div class="controls-content">
                        <div class="flex justify-content-between align-items-center">
                            <div>
                                <h3 class="font-semibold mb-2">Demo Controls</h3>
                                <p class="text-600 text-sm">
                                    Control the demo experience and explore different scenarios
                                </p>
                            </div>
                            <div class="flex gap-2">
                                <Button
                                    label="Reset Demo"
                                    icon="pi pi-refresh"
                                    severity="secondary"
                                    @click="resetDemo"
                                />
                                <Button
                                    label="Skip to Complete"
                                    icon="pi pi-fast-forward"
                                    @click="skipToComplete"
                                />
                            </div>
                        </div>
                        
                        <!-- Demo Settings -->
                        <div class="demo-settings mt-4 grid">
                            <div class="col-12 md:col-4">
                                <div class="setting-item">
                                    <label class="text-sm font-semibold mb-2 block">Demo Lead Actions:</label>
                                    <MultiSelect
                                        v-model="selectedDemoActions"
                                        :options="demoActionOptions"
                                        option-label="label"
                                        option-value="key"
                                        placeholder="Select actions"
                                        class="w-full"
                                        :max-selected-labels="3"
                                    />
                                </div>
                            </div>
                            <div class="col-12 md:col-4">
                                <div class="setting-item">
                                    <label class="text-sm font-semibold mb-2 block">Demo Company:</label>
                                    <Dropdown
                                        v-model="selectedDemoCompany"
                                        :options="demoCompanyOptions"
                                        option-label="name"
                                        option-value="key"
                                        placeholder="Select company type"
                                        class="w-full"
                                    />
                                </div>
                            </div>
                            <div class="col-12 md:col-4">
                                <div class="setting-item">
                                    <label class="text-sm font-semibold mb-2 block">Auto-save Demo:</label>
                                    <div class="flex align-items-center gap-2">
                                        <InputSwitch v-model="demoAutoSave" />
                                        <span class="text-sm">{{ demoAutoSave ? 'Enabled' : 'Disabled' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </Card>
        </div>

        <!-- Demo Content -->
        <div class="demo-content">
            <OnboardingScripts
                :key="demoKey"
                @complete="handleDemoComplete"
                @close="handleDemoClose"
                @scripts-change="handleScriptsChange"
                @progress-change="handleProgressChange"
            />
        </div>

        <!-- Demo Information Panel -->
        <div class="demo-info mt-6">
            <Card>
                <template #header>
                    <div class="info-header p-3 bg-blue-50">
                        <h3 class="font-semibold flex align-items-center gap-2">
                            <i class="pi pi-info-circle text-blue-500"></i>
                            Demo Information
                        </h3>
                    </div>
                </template>
                
                <template #content>
                    <div class="info-content">
                        <div class="grid">
                            <!-- Current Progress -->
                            <div class="col-12 md:col-6">
                                <div class="progress-info">
                                    <h4 class="font-semibold mb-2">Current Progress</h4>
                                    <div class="progress-details">
                                        <div class="flex justify-content-between align-items-center mb-2">
                                            <span class="text-sm">Overall Progress:</span>
                                            <span class="font-semibold">{{ currentProgress }}%</span>
                                        </div>
                                        <ProgressBar :value="currentProgress" class="mb-3" />
                                        
                                        <div class="progress-stats grid">
                                            <div class="col-6">
                                                <div class="stat-item text-center">
                                                    <div class="text-lg font-bold text-primary">{{ scriptsCreated }}</div>
                                                    <div class="text-xs text-600">Scripts Created</div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="stat-item text-center">
                                                    <div class="text-lg font-bold text-green-500">{{ variablesUsed }}</div>
                                                    <div class="text-xs text-600">Variables Used</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Demo Features -->
                            <div class="col-12 md:col-6">
                                <div class="features-info">
                                    <h4 class="font-semibold mb-2">Demo Features</h4>
                                    <div class="features-list">
                                        <div class="feature-item flex align-items-center gap-2 mb-2">
                                            <i class="pi pi-check-circle text-green-500"></i>
                                            <span class="text-sm">Real-time auto-save simulation</span>
                                        </div>
                                        <div class="feature-item flex align-items-center gap-2 mb-2">
                                            <i class="pi pi-check-circle text-green-500"></i>
                                            <span class="text-sm">Interactive script editor</span>
                                        </div>
                                        <div class="feature-item flex align-items-center gap-2 mb-2">
                                            <i class="pi pi-check-circle text-green-500"></i>
                                            <span class="text-sm">Variable validation & suggestions</span>
                                        </div>
                                        <div class="feature-item flex align-items-center gap-2 mb-2">
                                            <i class="pi pi-check-circle text-green-500"></i>
                                            <span class="text-sm">Template examples & usage guide</span>
                                        </div>
                                        <div class="feature-item flex align-items-center gap-2 mb-2">
                                            <i class="pi pi-check-circle text-green-500"></i>
                                            <span class="text-sm">Progress tracking & completion</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Demo Actions -->
                        <div class="demo-actions mt-4 pt-4 border-top-1 border-200">
                            <div class="flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="font-semibold mb-1">Try These Demo Actions:</h4>
                                    <p class="text-600 text-sm">Explore different aspects of the onboarding process</p>
                                </div>
                                <div class="flex gap-2">
                                    <Button
                                        label="Load Sample Scripts"
                                        icon="pi pi-file-import"
                                        size="small"
                                        severity="secondary"
                                        @click="loadSampleScripts"
                                    />
                                    <Button
                                        label="Simulate Auto-save"
                                        icon="pi pi-save"
                                        size="small"
                                        @click="simulateAutoSave"
                                    />
                                    <Button
                                        label="Show Variables Help"
                                        icon="pi pi-question-circle"
                                        size="small"
                                        severity="info"
                                        @click="showVariablesHelp"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </Card>
        </div>

        <!-- Demo Completion Dialog -->
        <Dialog
            v-model:visible="showCompletionDialog"
            modal
            header="Demo Completed!"
            :style="{ width: '30rem' }"
            :closable="false"
        >
            <div class="completion-content text-center py-4">
                <i class="pi pi-check-circle text-6xl text-green-500 mb-3"></i>
                <h3 class="text-xl font-semibold mb-2">Great Job!</h3>
                <p class="text-600 mb-4">
                    You've completed the AI scripts onboarding demo. 
                    Ready to set up your real scripts?
                </p>
                
                <div class="completion-stats grid mb-4">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-primary">{{ scriptsCreated }}</div>
                            <div class="text-sm text-600">Demo Scripts</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-green-500">{{ variablesUsed }}</div>
                            <div class="text-sm text-600">Variables Used</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <div class="flex justify-content-center gap-2">
                    <Button
                        label="Try Again"
                        icon="pi pi-refresh"
                        severity="secondary"
                        @click="resetDemo"
                    />
                    <Button
                        label="Start Real Setup"
                        icon="pi pi-arrow-right"
                        @click="startRealSetup"
                    />
                </div>
            </template>
        </Dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import OnboardingScripts from '../OnboardingScripts.vue';
import type { ScriptsCollection } from '../types';

// Composables
const toast = useToast();

// State
const demoKey = ref(0);
const currentProgress = ref(0);
const currentScripts = ref<ScriptsCollection>({});
const showCompletionDialog = ref(false);

// Demo settings
const selectedDemoActions = ref(['inquired', 'quoted', 'booked', 'converted']);
const selectedDemoCompany = ref('home_services');
const demoAutoSave = ref(true);

// Demo options
const demoActionOptions = [
    { key: 'inquired', label: 'Inquired' },
    { key: 'quoted', label: 'Quoted' },
    { key: 'booked', label: 'Booked' },
    { key: 'converted', label: 'Converted' },
    { key: 'qualified', label: 'Qualified' },
    { key: 'unqualified', label: 'Unqualified' }
];

const demoCompanyOptions = [
    { key: 'home_services', name: 'Home Services Company' },
    { key: 'consulting', name: 'Consulting Firm' },
    { key: 'retail', name: 'Retail Business' },
    { key: 'healthcare', name: 'Healthcare Provider' },
    { key: 'education', name: 'Education Services' }
];

// Computed
const scriptsCreated = computed(() => {
    return Object.keys(currentScripts.value).length;
});

const variablesUsed = computed(() => {
    const allVariables = new Set<string>();
    Object.values(currentScripts.value).forEach(script => {
        script.variables.forEach(variable => allVariables.add(variable));
    });
    return allVariables.size;
});

// Methods
const resetDemo = () => {
    demoKey.value++;
    currentProgress.value = 0;
    currentScripts.value = {};
    showCompletionDialog.value = false;
    
    toast.add({
        severity: 'info',
        summary: 'Demo Reset',
        detail: 'Demo has been reset. You can start over!',
        life: 3000
    });
};

const skipToComplete = () => {
    // Simulate completed scripts
    const sampleScripts: ScriptsCollection = {
        inquired: {
            content: 'Hello [customer_name], thank you for your inquiry about our [service_type] services...',
            variables: ['customer_name', 'service_type', 'agent_name'],
            lastUpdated: new Date(),
            isValid: true
        },
        quoted: {
            content: 'Hi [customer_name], I wanted to follow up on the quote for [quote_amount]...',
            variables: ['customer_name', 'quote_amount', 'agent_name'],
            lastUpdated: new Date(),
            isValid: true
        }
    };
    
    currentScripts.value = sampleScripts;
    currentProgress.value = 100;
    showCompletionDialog.value = true;
};

const handleDemoComplete = (scripts: ScriptsCollection) => {
    currentScripts.value = scripts;
    showCompletionDialog.value = true;
    
    toast.add({
        severity: 'success',
        summary: 'Demo Complete!',
        detail: 'You\'ve successfully completed the demo onboarding process',
        life: 5000
    });
};

const handleDemoClose = () => {
    toast.add({
        severity: 'info',
        summary: 'Demo Closed',
        detail: 'Demo onboarding was closed',
        life: 3000
    });
};

const handleScriptsChange = (scripts: ScriptsCollection) => {
    currentScripts.value = scripts;
};

const handleProgressChange = (progress: number) => {
    currentProgress.value = progress;
};

const loadSampleScripts = () => {
    toast.add({
        severity: 'info',
        summary: 'Sample Scripts',
        detail: 'Sample scripts would be loaded in the real implementation',
        life: 3000
    });
};

const simulateAutoSave = () => {
    toast.add({
        severity: 'success',
        summary: 'Auto-save Simulated',
        detail: 'In the real app, scripts are automatically saved as you type',
        life: 3000
    });
};

const showVariablesHelp = () => {
    toast.add({
        severity: 'info',
        summary: 'Variables Help',
        detail: 'Click the help button in the script editor to see available variables',
        life: 3000
    });
};

const startRealSetup = () => {
    showCompletionDialog.value = false;
    toast.add({
        severity: 'info',
        summary: 'Ready for Real Setup',
        detail: 'You can now implement this feature in your application!',
        life: 5000
    });
};
</script>

<style scoped>
.script-onboarding-demo {
    @apply max-w-7xl mx-auto p-4;
}

.demo-badges {
    @apply flex-wrap;
}

.demo-controls :deep(.p-card-content) {
    @apply p-3;
}

.setting-item {
    @apply h-full;
}

.progress-stats .stat-item {
    @apply p-2 border-1 border-200 border-round;
}

.feature-item {
    @apply transition-all duration-200 p-1 border-round;
}

.feature-item:hover {
    @apply bg-gray-50;
}

.demo-actions {
    @apply bg-gray-50 border-round p-3;
}

.completion-content .stat-item {
    @apply p-2 border-1 border-200 border-round;
}

:deep(.p-card-header) {
    @apply p-0;
}

:deep(.p-progressbar) {
    @apply h-2;
}

@media (max-width: 768px) {
    .demo-controls .flex {
        @apply flex-column gap-3;
    }
    
    .demo-actions .flex {
        @apply flex-column align-items-start gap-3;
    }
    
    .demo-actions .flex:last-child {
        @apply w-full justify-content-start;
    }
}
</style>
