<template>
    <div class="script-onboarding-example">
        <div class="example-header mb-4">
            <h1 class="text-2xl font-bold mb-2">AI Scripts Onboarding</h1>
            <p class="text-gray-600">
                Complete the AI scripts setup to enable personalized automated communications with your leads.
            </p>
        </div>

        <!-- Show onboarding if not complete -->
        <OnboardingScripts 
            v-if="!isOnboardingComplete" 
            @complete="handleOnboardingComplete"
            @close="handleOnboardingClose"
        />

        <!-- Show completion status if already complete -->
        <Card v-else class="text-center">
            <template #content>
                <div class="completion-status py-6">
                    <div class="success-icon mb-4">
                        <i class="pi pi-check-circle text-6xl text-green-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-green-700 mb-2">AI Scripts Active</h3>
                    <p class="text-gray-600 mb-4">
                        Your AI scripts are set up and working. Your AI will use these scripts when communicating with leads.
                    </p>
                    
                    <!-- Scripts Summary -->
                    <div class="scripts-summary mb-4">
                        <div class="grid max-w-30rem mx-auto">
                            <div class="col-6">
                                <div class="stat-card p-3 border-1 border-200 border-round">
                                    <div class="text-2xl font-bold text-primary">{{ scriptsCount }}</div>
                                    <div class="text-sm text-600">Scripts Created</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-card p-3 border-1 border-200 border-round">
                                    <div class="text-2xl font-bold text-green-500">{{ lastUpdated }}</div>
                                    <div class="text-sm text-600">Last Updated</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons flex justify-content-center gap-2">
                        <Button
                            label="Edit Scripts"
                            icon="pi pi-pencil"
                            @click="editScripts"
                        />
                        <Button
                            label="View Scripts"
                            icon="pi pi-eye"
                            severity="secondary"
                            @click="viewScripts"
                        />
                        <Button
                            label="Reset Setup"
                            icon="pi pi-refresh"
                            severity="danger"
                            outlined
                            @click="confirmReset"
                        />
                    </div>
                </div>
            </template>
        </Card>

        <!-- Scripts Viewer Dialog -->
        <Dialog
            v-model:visible="showScriptsViewer"
            modal
            header="Your AI Scripts"
            :style="{ width: '60rem' }"
            :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
        >
            <div class="scripts-viewer">
                <div v-if="currentScripts && Object.keys(currentScripts).length > 0">
                    <div
                        v-for="(script, actionKey) in currentScripts"
                        :key="actionKey"
                        class="script-item mb-4"
                    >
                        <Card>
                            <template #header>
                                <div class="script-header p-3 bg-gray-50">
                                    <h4 class="font-semibold capitalize mb-1">{{ actionKey }} Script</h4>
                                    <div class="flex align-items-center gap-2">
                                        <span class="text-sm text-600">
                                            Variables: {{ script.variables.length }}
                                        </span>
                                        <span class="text-sm text-600">•</span>
                                        <span class="text-sm text-600">
                                            Updated: {{ formatDate(script.lastUpdated) }}
                                        </span>
                                    </div>
                                </div>
                            </template>
                            
                            <template #content>
                                <div class="script-content">
                                    <div class="content-preview p-3 bg-blue-50 border-round mb-3">
                                        <pre class="text-sm text-700 m-0 whitespace-pre-wrap">{{ script.content.replace(/<[^>]*>/g, '') }}</pre>
                                    </div>
                                    
                                    <div v-if="script.variables.length > 0" class="variables-used">
                                        <span class="text-sm font-semibold text-700 mr-2">Variables:</span>
                                        <Tag
                                            v-for="variable in script.variables"
                                            :key="variable"
                                            :value="`[${variable}]`"
                                            severity="info"
                                            class="text-xs mr-1"
                                        />
                                    </div>
                                </div>
                            </template>
                        </Card>
                    </div>
                </div>
                <div v-else class="text-center py-4">
                    <i class="pi pi-info-circle text-4xl text-400 mb-3"></i>
                    <p class="text-600">No scripts found</p>
                </div>
            </div>
            
            <template #footer>
                <div class="flex justify-content-end">
                    <Button
                        label="Close"
                        severity="secondary"
                        @click="showScriptsViewer = false"
                    />
                </div>
            </template>
        </Dialog>

        <!-- Reset Confirmation Dialog -->
        <ConfirmDialog />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant/store/tenantStore';
import OnboardingScripts from '../OnboardingScripts.vue';
import type { ScriptsCollection } from '../types';
import type { Tenant } from '@/entities/tenant/model';

// Composables
const confirm = useConfirm();
const toast = useToast();
const authStore = useAuthStore();
const tenantStore = useTenantStore();

// State
const isOnboardingComplete = ref(false);
const currentScripts = ref<ScriptsCollection | null>(null);
const showScriptsViewer = ref(false);
const isLoading = ref(false);

// Get user data
const userData = authStore.getUserData();
const tenantId = userData?.tenantId;

// Computed
const scriptsCount = computed(() => {
    return currentScripts.value ? Object.keys(currentScripts.value).length : 0;
});

const lastUpdated = computed(() => {
    if (!currentScripts.value) return 'Never';
    
    const dates = Object.values(currentScripts.value)
        .map(script => new Date(script.lastUpdated))
        .sort((a, b) => b.getTime() - a.getTime());
    
    if (dates.length === 0) return 'Never';
    
    const latest = dates[0];
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - latest.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return latest.toLocaleDateString();
});

// Methods
const loadTenantData = async () => {
    if (!tenantId) return;
    
    try {
        isLoading.value = true;
        const tenant = await tenantStore.getTenant(tenantId);
        
        if (tenant?.scripts) {
            currentScripts.value = tenant.scripts;
            isOnboardingComplete.value = checkOnboardingComplete(tenant);
        } else {
            isOnboardingComplete.value = false;
        }
    } catch (error) {
        console.error('Error loading tenant data:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load scripts configuration',
            life: 3000
        });
    } finally {
        isLoading.value = false;
    }
};

const checkOnboardingComplete = (tenant: Tenant): boolean => {
    const scripts = tenant.scripts;
    if (!scripts || Object.keys(scripts).length === 0) return false;
    
    // Check if at least one script has content
    return Object.values(scripts).some(script => 
        script.content && script.content.trim().length > 0
    );
};

const handleOnboardingComplete = (scripts: ScriptsCollection) => {
    currentScripts.value = scripts;
    isOnboardingComplete.value = true;
    
    toast.add({
        severity: 'success',
        summary: 'Setup Complete',
        detail: 'Your AI scripts have been configured successfully!',
        life: 5000
    });
};

const handleOnboardingClose = () => {
    // Handle onboarding close if needed
    toast.add({
        severity: 'info',
        summary: 'Setup Cancelled',
        detail: 'You can complete the setup later from the settings',
        life: 3000
    });
};

const editScripts = () => {
    isOnboardingComplete.value = false;
};

const viewScripts = () => {
    showScriptsViewer.value = true;
};

const confirmReset = () => {
    confirm.require({
        message: 'Are you sure you want to reset your AI scripts setup? This will delete all your current scripts.',
        header: 'Reset Scripts Setup',
        icon: 'pi pi-exclamation-triangle',
        rejectClass: 'p-button-secondary p-button-outlined',
        rejectLabel: 'Cancel',
        acceptLabel: 'Reset',
        accept: resetScripts
    });
};

const resetScripts = async () => {
    if (!tenantId) return;
    
    try {
        isLoading.value = true;
        
        // Clear scripts from tenant
        await tenantStore.updateTenant(tenantId, { scripts: {} });
        
        currentScripts.value = null;
        isOnboardingComplete.value = false;
        
        toast.add({
            severity: 'info',
            summary: 'Scripts Reset',
            detail: 'Your scripts have been reset. You can now set them up again.',
            life: 3000
        });
    } catch (error) {
        console.error('Error resetting scripts:', error);
        toast.add({
            severity: 'error',
            summary: 'Reset Failed',
            detail: 'Failed to reset scripts. Please try again.',
            life: 3000
        });
    } finally {
        isLoading.value = false;
    }
};

const formatDate = (date: Date | string): string => {
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// Lifecycle
onMounted(() => {
    loadTenantData();
});
</script>

<style scoped>
.script-onboarding-example {
    @apply max-w-6xl mx-auto p-4;
}

.completion-status {
    @apply max-w-40rem mx-auto;
}

.stat-card {
    @apply text-center transition-all duration-200;
}

.stat-card:hover {
    @apply shadow-2 border-primary-200;
}

.scripts-viewer {
    @apply max-h-80vh overflow-y-auto;
}

.script-item :deep(.p-card-header) {
    @apply p-0;
}

.script-content {
    @apply p-0;
}

.content-preview {
    @apply max-h-20rem overflow-y-auto;
}

.content-preview pre {
    @apply font-sans leading-relaxed;
}

.variables-used {
    @apply border-t-1 border-200 pt-3;
}

:deep(.p-dialog-content) {
    @apply p-4;
}
</style>
