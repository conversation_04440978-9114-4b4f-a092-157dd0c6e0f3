# AI Scripts Onboarding - Integration Test Guide

## Overview
This document provides a comprehensive test guide for the AI Scripts Onboarding feature to ensure all components work correctly together.

## Test Scenarios

### 1. Basic Feature Integration
- [ ] Feature exports are accessible from index.ts
- [ ] All components can be imported without errors
- [ ] Types are properly defined and exported
- [ ] Services can be instantiated and used

### 2. Auto-Save Service Testing
- [ ] Auto-save service initializes correctly with tenant ID
- [ ] Debounced saving works (1-second delay)
- [ ] Retry logic functions on failures (max 3 retries)
- [ ] Status callbacks are triggered correctly
- [ ] Multiple scripts can be saved simultaneously
- [ ] onSnapshot integration works with tenant store

### 3. Script Validation Service Testing
- [ ] Script content validation works correctly
- [ ] Variable extraction identifies all variables
- [ ] Invalid variables are detected
- [ ] Action-specific validation provides relevant warnings
- [ ] Suggestions are generated appropriately
- [ ] Script formatting fixes common issues

### 4. Script Editor Component Testing
- [ ] PrimeVue Editor renders with custom toolbar
- [ ] Auto-save triggers on text changes
- [ ] Validation feedback displays correctly
- [ ] Variables help dialog opens and functions
- [ ] Save status indicator updates properly
- [ ] Action badge displays with correct styling

### 5. Variables Help Component Testing
- [ ] Dialog opens and displays available variables
- [ ] Search functionality filters variables correctly
- [ ] Category tabs organize variables properly
- [ ] Variable insertion works from dialog
- [ ] Action-specific recommendations appear
- [ ] Usage tips are displayed clearly

### 6. Lead Actions Script Manager Testing
- [ ] Loads tenant lead_actions configuration
- [ ] Generates script editors for enabled actions
- [ ] Progress tracking updates correctly
- [ ] Template loading functionality works
- [ ] Clear all confirmation and execution
- [ ] Save all scripts functionality
- [ ] Continue button enables when ready

### 7. Main Onboarding Component Testing
- [ ] Step navigation works correctly
- [ ] Progress steps update visually
- [ ] Welcome step displays properly
- [ ] Script creation step integrates manager
- [ ] Review step shows script summaries
- [ ] Test variables preview functionality
- [ ] Completion step shows final status

### 8. Example Components Testing
- [ ] Example component shows onboarding when incomplete
- [ ] Completion status displays when scripts exist
- [ ] Scripts viewer dialog functions correctly
- [ ] Reset functionality works properly
- [ ] Demo component provides interactive experience

## Integration Points to Verify

### Tenant Store Integration
```typescript
// Verify these integrations work:
- useTenantStore().getTenant(tenantId)
- useTenantStore().updateTenant(tenantId, { scripts: {...} })
- onSnapshot subscription for real-time updates
- Lead actions configuration access
```

### Auth Store Integration
```typescript
// Verify these integrations work:
- useAuthStore().getUserData()
- Tenant ID extraction from user data
- Authentication state handling
```

### PrimeVue Components
```typescript
// Verify these components render correctly:
- Editor with custom toolbar
- Dialog components
- Progress indicators
- Form controls (InputText, Dropdown, etc.)
- Toast notifications
- Confirm dialogs
```

## Manual Testing Steps

### Step 1: Setup
1. Ensure tenant has lead_actions configured
2. User is authenticated with valid tenant ID
3. Required dependencies are installed

### Step 2: Basic Flow
1. Import and render OnboardingScripts component
2. Navigate through all onboarding steps
3. Create scripts for different lead actions
4. Test auto-save functionality
5. Complete the onboarding process

### Step 3: Advanced Features
1. Test variables help dialog
2. Use script templates
3. Test validation with invalid content
4. Test script preview with variables
5. Test reset and edit functionality

### Step 4: Error Handling
1. Test with no lead actions configured
2. Test with network failures during save
3. Test with invalid tenant ID
4. Test with missing authentication

## Expected Behaviors

### Auto-Save
- Scripts should save automatically after 1 second of inactivity
- Save status should show: idle → saving → saved → idle
- Failed saves should retry up to 3 times
- Multiple scripts should save independently

### Validation
- Real-time validation as user types
- Error messages for invalid content
- Warnings for missing recommended variables
- Success indicators for valid scripts

### Progress Tracking
- Overall progress percentage updates
- Individual script completion status
- Visual progress indicators
- Enable/disable navigation based on progress

### User Experience
- Smooth transitions between steps
- Clear instructions and help text
- Responsive design on mobile devices
- Accessible keyboard navigation

## Performance Considerations

### Auto-Save Optimization
- Debouncing prevents excessive API calls
- Only changed scripts are updated
- Efficient onSnapshot subscriptions
- Proper cleanup on component unmount

### Memory Management
- Event listeners are properly removed
- Timeouts are cleared on cleanup
- Large script content is handled efficiently
- Component state is managed properly

## Browser Compatibility
- Modern browsers with ES6+ support
- Firebase SDK compatibility
- PrimeVue component support
- Vue 3 Composition API support

## Deployment Checklist
- [ ] All TypeScript types are properly exported
- [ ] No console errors in development
- [ ] Build process completes successfully
- [ ] All dependencies are included in package.json
- [ ] Feature documentation is complete
- [ ] Example usage is provided
- [ ] Integration tests pass
- [ ] Manual testing completed

## Troubleshooting Common Issues

### Auto-Save Not Working
1. Check tenant ID is valid
2. Verify tenant store integration
3. Check network connectivity
4. Verify Firebase permissions

### Components Not Rendering
1. Check PrimeVue imports
2. Verify component registration
3. Check for TypeScript errors
4. Verify required props are passed

### Validation Issues
1. Check script content format
2. Verify variable syntax
3. Check validation rules configuration
4. Verify available variables list

This comprehensive test guide ensures the AI Scripts Onboarding feature is fully functional and ready for production use.
