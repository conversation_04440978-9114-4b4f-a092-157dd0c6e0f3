# AI Scripts Onboarding Feature

A comprehensive Vue.js feature for onboarding users to create AI scripts based on their lead actions configuration in a multi-tenant CRM application.

## Features

- **Dynamic Script Creation**: Automatically generates script editors based on tenant lead_actions configuration
- **Real-time Auto-save**: Uses Firestore onSnapshot for live script saving and updates
- **Rich Text Editor**: Beautiful script editor with PrimeVue Editor and custom toolbar
- **Step-by-Step UI**: Guided onboarding flow with progress indicators
- **Usage Information**: Comprehensive help and examples for script creation
- **Responsive Design**: Mobile-friendly interface using PrimeVue components
- **Tenant Integration**: Seamlessly integrates with tenant store and authentication

## Components

### Main Components
- `OnboardingScripts.vue` - Main onboarding component with step navigation
- `ScriptEditor.vue` - Rich text editor for individual scripts
- `LeadActionsScriptManager.vue` - Manages multiple script editors based on lead actions
- `ScriptUsageInfo.vue` - Displays usage information and help

### Supporting Components
- `ScriptVariablesHelp.vue` - Shows available variables and their usage
- `ScriptExamples.vue` - Provides example scripts for different scenarios

## Services

- `ScriptAutoSaveService` - Handles auto-saving of scripts with debouncing
- `ScriptValidationService` - Validates script content and variables

## Types

- `ScriptConfig` - Configuration for individual scripts
- `LeadActionScript` - Script associated with a lead action
- `ScriptOnboardingStep` - Onboarding step definition

## Usage

### Basic Usage

```vue
<template>
  <OnboardingScripts @complete="handleComplete" />
</template>

<script setup lang="ts">
import { OnboardingScripts } from '@/features/scripts-onboarding';

const handleComplete = (scripts: Record<string, ScriptConfig>) => {
  console.log('Scripts onboarding completed:', scripts);
  // Handle completion logic
};
</script>
```

## Data Flow

1. **Initialization**:
   - Gets user data and tenant ID from auth store
   - Fetches tenant lead_actions configuration
   - Subscribes to tenant changes for real-time updates
   - Generates script editors for each enabled lead action

2. **Script Creation**:
   - User creates/edits scripts for each lead action
   - Auto-save triggers on text changes with debouncing
   - Real-time validation and feedback
   - Progress tracking across all scripts

3. **Completion**:
   - Validates all required scripts are created
   - Saves final configuration to tenant document
   - Emits completion event with script data

## Integration

The feature integrates with:
- **Tenant Store**: For lead_actions configuration and script storage
- **Auth Store**: For user authentication and tenant identification
- **Lead Configuration**: Uses existing lead_actions setup
- **Auto-save Service**: Real-time script persistence

## Tenant Data Structure

Scripts are stored in the tenant document under the `scripts` field:

```typescript
{
  scripts: {
    inquired: {
      content: "Script content...",
      variables: ["customer_name", "agent_name"],
      lastUpdated: timestamp
    },
    quoted: {
      content: "Script content...",
      variables: ["customer_name", "quote_amount"],
      lastUpdated: timestamp
    }
    // ... other lead actions
  }
}
```
