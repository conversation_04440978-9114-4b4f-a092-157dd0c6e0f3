<script setup lang="ts">
import { ref } from 'vue';
import { useToast } from 'primevue/usetoast';
import { LeadConfiguration } from './index';

const toast = useToast();
const showConfiguration = ref(false);

const handleComplete = () => {
    toast.add({
        severity: 'success',
        summary: 'Configuration Complete',
        detail: 'Lead configuration has been saved successfully',
        life: 3000
    });
    showConfiguration.value = false;
};

const handleClose = () => {
    showConfiguration.value = false;
};

const openConfiguration = () => {
    showConfiguration.value = true;
};
</script>

<template>
    <div class="lead-configuration-demo p-6">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-4">
                Lead Configuration Demo
            </h1>
            <p class="text-surface-600 dark:text-surface-400 mb-6">
                Click the button below to open the lead configuration interface
            </p>
            
            <Button
                label="Open Lead Configuration"
                icon="pi pi-cog"
                size="large"
                @click="openConfiguration"
            />
        </div>

        <!-- Configuration Component -->
        <div v-if="showConfiguration" class="mt-8">
            <LeadConfiguration
                :visible="showConfiguration"
                @complete="handleComplete"
                @close="handleClose"
            />
        </div>
    </div>
</template>

<style scoped>
.lead-configuration-demo {
    @apply min-h-screen bg-surface-50 dark:bg-surface-900;
}
</style>
