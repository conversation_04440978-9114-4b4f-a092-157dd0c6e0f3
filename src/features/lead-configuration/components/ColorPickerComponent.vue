<script setup lang="ts">
import { ref, computed } from 'vue';
import { PREDEFINED_COLORS } from '../types';

// Props
interface Props {
    modelValue: string;
    error?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    error: false
});

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: string];
}>();

// Reactive state
const showColorPicker = ref(false);
const customColor = ref(props.modelValue);

// Computed
const selectedColor = computed({
    get: () => props.modelValue,
    set: (value: string) => emit('update:modelValue', value)
});

const isCustomColor = computed(() => {
    return !PREDEFINED_COLORS.includes(props.modelValue);
});

// Methods
const selectPredefinedColor = (color: string) => {
    selectedColor.value = color;
    customColor.value = color;
    showColorPicker.value = false;
};

const selectCustomColor = () => {
    selectedColor.value = customColor.value;
    showColorPicker.value = false;
};

const openColorPicker = () => {
    customColor.value = selectedColor.value;
    showColorPicker.value = true;
};

const isValidHexColor = (color: string): boolean => {
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexColorRegex.test(color);
};
</script>

<template>
    <div class="color-picker-component">
        <!-- Current Color Display -->
        <div 
            :class="[
                'color-display flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-all',
                error 
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20' 
                    : 'border-surface-200 dark:border-surface-700 bg-surface-0 dark:bg-surface-900 hover:border-primary-300'
            ]"
            @click="openColorPicker"
        >
            <div
                :style="{ backgroundColor: selectedColor }"
                class="w-8 h-8 rounded border border-surface-300 dark:border-surface-600 flex-shrink-0"
            ></div>
            <div class="flex-1">
                <div class="text-sm font-medium text-surface-900 dark:text-surface-0">
                    {{ selectedColor }}
                </div>
                <div class="text-xs text-surface-500 dark:text-surface-400">
                    {{ isCustomColor ? 'Custom Color' : 'Predefined Color' }}
                </div>
            </div>
            <i class="pi pi-chevron-down text-surface-400"></i>
        </div>

        <!-- Color Picker Dialog -->
        <Dialog
            v-model:visible="showColorPicker"
            header="Select Color"
            :modal="true"
            :style="{ width: '400px' }"
            :closable="true"
        >
            <div class="space-y-4">
                <!-- Predefined Colors -->
                <div>
                    <h4 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3">
                        Predefined Colors
                    </h4>
                    <div class="grid grid-cols-5 gap-2">
                        <button
                            v-for="color in PREDEFINED_COLORS"
                            :key="color"
                            :style="{ backgroundColor: color }"
                            :class="[
                                'w-12 h-12 rounded border-2 transition-all hover:scale-110',
                                selectedColor === color 
                                    ? 'border-surface-900 dark:border-surface-0 shadow-lg' 
                                    : 'border-surface-300 dark:border-surface-600'
                            ]"
                            @click="selectPredefinedColor(color)"
                            :title="color"
                        ></button>
                    </div>
                </div>

                <!-- Custom Color Input -->
                <div>
                    <h4 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3">
                        Custom Color
                    </h4>
                    <div class="flex gap-2">
                        <div class="flex-1">
                            <InputText
                                v-model="customColor"
                                placeholder="#000000"
                                :class="{ 'p-invalid': !isValidHexColor(customColor) }"
                                class="w-full"
                            />
                            <small v-if="!isValidHexColor(customColor)" class="p-error">
                                Please enter a valid hex color (e.g., #FF0000)
                            </small>
                        </div>
                        <div
                            :style="{ backgroundColor: isValidHexColor(customColor) ? customColor : '#CCCCCC' }"
                            class="w-12 h-12 rounded border border-surface-300 dark:border-surface-600 flex-shrink-0"
                        ></div>
                    </div>
                </div>

                <!-- Color Picker Input (HTML5) -->
                <div>
                    <h4 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3">
                        Color Picker
                    </h4>
                    <input
                        type="color"
                        v-model="customColor"
                        class="w-full h-12 rounded border border-surface-300 dark:border-surface-600 cursor-pointer"
                    />
                </div>
            </div>

            <template #footer>
                <div class="flex justify-end gap-2">
                    <Button
                        label="Cancel"
                        icon="pi pi-times"
                        severity="secondary"
                        outlined
                        @click="showColorPicker = false"
                    />
                    <Button
                        label="Select"
                        icon="pi pi-check"
                        :disabled="!isValidHexColor(customColor)"
                        @click="selectCustomColor"
                    />
                </div>
            </template>
        </Dialog>
    </div>
</template>

<style scoped>
.color-display:hover {
    @apply shadow-sm;
}

.p-error {
    @apply text-red-500 text-xs mt-1 block;
}

/* Style the HTML5 color input */
input[type="color"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: transparent;
    border: none;
    cursor: pointer;
}

input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 6px;
}

input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 6px;
}

input[type="color"]::-moz-color-swatch {
    border: none;
    border-radius: 6px;
}
</style>
