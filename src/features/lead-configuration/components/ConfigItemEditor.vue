<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { LeadConfigService } from '../services/leadConfigService';
import ColorPickerComponent from './ColorPickerComponent.vue';
import type { EditableConfigItem } from '../types';
import { AVAILABLE_ICONS } from '../types';

// Props
interface Props {
    visible: boolean;
    item: EditableConfigItem;
    existingKeys: string[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
    save: [item: EditableConfigItem];
    cancel: [];
}>();

// Reactive state
const localItem = ref<EditableConfigItem>({ ...props.item });
const errors = ref<Record<string, string>>({});
const submitted = ref(false);

// Computed
const isEditing = computed(() => {
    return props.existingKeys.includes(props.item.key);
});

const dialogTitle = computed(() => {
    return isEditing.value ? 'Edit Configuration Item' : 'Add New Configuration Item';
});

const isKeyTaken = computed(() => {
    if (isEditing.value) return false;
    return props.existingKeys.includes(localItem.value.key);
});

const canSave = computed(() => {
    const validation = LeadConfigService.validateConfigItem(localItem.value);
    return validation.isValid && !isKeyTaken.value && localItem.value.key.trim().length > 0;
});

// Methods
const validateForm = () => {
    const validation = LeadConfigService.validateConfigItem(localItem.value);
    errors.value = { ...validation.errors };

    // Additional key validation
    if (!localItem.value.key || localItem.value.key.trim().length === 0) {
        errors.value.key = 'Key is required';
    } else if (isKeyTaken.value) {
        errors.value.key = 'Key already exists';
    } else if (!/^[a-z0-9_]+$/.test(localItem.value.key)) {
        errors.value.key = 'Key must contain only lowercase letters, numbers, and underscores';
    }

    return Object.keys(errors.value).length === 0;
};

const generateKeyFromLabel = () => {
    if (localItem.value.label && (!localItem.value.key || !isEditing.value)) {
        const generatedKey = LeadConfigService.generateUniqueKey(
            props.existingKeys,
            localItem.value.label
        );
        localItem.value.key = generatedKey;
    }
};

const handleSave = () => {
    submitted.value = true;

    if (validateForm()) {
        emit('save', { ...localItem.value });
    }
};

const handleCancel = () => {
    emit('cancel');
};

const resetForm = () => {
    localItem.value = { ...props.item };
    errors.value = {};
    submitted.value = false;
};

// Watchers
watch(() => props.item, resetForm, { immediate: true });
watch(() => localItem.value.label, generateKeyFromLabel);
</script>

<template>
    <Dialog
        :visible="visible"
        :header="dialogTitle"
        :modal="true"
        :closable="false"
        :style="{ width: '600px' }"
        class="config-item-editor"
    >
        <div class="space-y-6">
            <!-- Preview -->
            <div class="preview-section">
                <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                    Preview
                </label>
                <div class="flex items-center gap-3 p-3 border border-surface-200 dark:border-surface-700 rounded-lg bg-surface-50 dark:bg-surface-800">
                    <div
                        :style="{
                            backgroundColor: localItem.bg_color,
                            color: localItem.text_color
                        }"
                        class="w-10 h-10 rounded-full flex items-center justify-center"
                    >
                        <i :class="localItem.icon" class="text-lg"></i>
                    </div>
                    <div>
                        <div class="font-medium text-surface-900 dark:text-surface-0">
                            {{ localItem.label || 'Item Label' }}
                        </div>
                        <div class="text-sm text-surface-600 dark:text-surface-400">
                            {{ localItem.key || 'item_key' }}
                        </div>
                    </div>
                    <Badge
                        :value="localItem.status"
                        :severity="localItem.status === 'enabled' ? 'success' : 'secondary'"
                        class="ml-auto"
                    />
                </div>
            </div>

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Label -->
                <div class="form-field">
                    <label for="label" class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                        Label *
                    </label>
                    <InputText
                        id="label"
                        v-model="localItem.label"
                        placeholder="Enter label"
                        :class="{ 'p-invalid': errors.label && submitted }"
                        class="w-full"
                    />
                    <small v-if="errors.label && submitted" class="p-error">
                        {{ errors.label }}
                    </small>
                </div>

                <!-- Key -->
                <div class="form-field">
                    <label for="key" class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                        Key *
                    </label>
                    <InputText
                        id="key"
                        v-model="localItem.key"
                        placeholder="Enter key"
                        :disabled="isEditing"
                        :class="{ 'p-invalid': (errors.key || isKeyTaken) && submitted }"
                        class="w-full"
                    />
                    <small v-if="(errors.key || isKeyTaken) && submitted" class="p-error">
                        {{ errors.key || (isKeyTaken ? 'Key already exists' : '') }}
                    </small>
                    <small v-else class="text-surface-500 dark:text-surface-400">
                        Lowercase letters, numbers, and underscores only
                    </small>
                </div>
            </div>

            <!-- Icon Selection -->
            <div class="form-field">
                <label for="icon" class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                    Icon *
                </label>
                <Dropdown
                    id="icon"
                    v-model="localItem.icon"
                    :options="AVAILABLE_ICONS"
                    option-label="label"
                    option-value="value"
                    placeholder="Select an icon"
                    :class="{ 'p-invalid': errors.icon && submitted }"
                    class="w-full"
                    filter
                    show-clear
                >
                    <template #value="slotProps">
                        <div v-if="slotProps.value" class="flex items-center gap-2">
                            <i :class="slotProps.value"></i>
                            <span>{{ AVAILABLE_ICONS.find(icon => icon.value === slotProps.value)?.label }}</span>
                        </div>
                        <span v-else>Select an icon</span>
                    </template>
                    <template #option="slotProps">
                        <div class="flex items-center gap-2">
                            <i :class="slotProps.option.value"></i>
                            <span>{{ slotProps.option.label }}</span>
                        </div>
                    </template>
                </Dropdown>
                <small v-if="errors.icon && submitted" class="p-error">
                    {{ errors.icon }}
                </small>
            </div>

            <!-- Colors -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Background Color -->
                <div class="form-field">
                    <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                        Background Color *
                    </label>
                    <ColorPickerComponent
                        v-model="localItem.bg_color"
                        :error="errors.bg_color && submitted"
                    />
                    <small v-if="errors.bg_color && submitted" class="p-error">
                        {{ errors.bg_color }}
                    </small>
                </div>

                <!-- Text Color -->
                <div class="form-field">
                    <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                        Text Color *
                    </label>
                    <ColorPickerComponent
                        v-model="localItem.text_color"
                        :error="errors.text_color && submitted"
                    />
                    <small v-if="errors.text_color && submitted" class="p-error">
                        {{ errors.text_color }}
                    </small>
                </div>
            </div>

            <!-- Status -->
            <div class="form-field">
                <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                    Status
                </label>
                <div class="flex items-center gap-4">
                    <div class="flex items-center">
                        <RadioButton
                            id="enabled"
                            v-model="localItem.status"
                            value="enabled"
                        />
                        <label for="enabled" class="ml-2 text-sm">Enabled</label>
                    </div>
                    <div class="flex items-center">
                        <RadioButton
                            id="disabled"
                            v-model="localItem.status"
                            value="disabled"
                        />
                        <label for="disabled" class="ml-2 text-sm">Disabled</label>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end gap-2">
                <Button
                    label="Cancel"
                    icon="pi pi-times"
                    severity="secondary"
                    outlined
                    @click="handleCancel"
                />
                <Button
                    label="Save"
                    icon="pi pi-check"
                    :disabled="!canSave"
                    @click="handleSave"
                />
            </div>
        </template>
    </Dialog>
</template>

<style scoped>
.config-item-editor .form-field {
    @apply space-y-1;
}

.preview-section {
    @apply border-b border-surface-200 dark:border-surface-700 pb-4;
}

.p-error {
    @apply text-red-500 text-xs mt-1 block;
}
</style>
