import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant';
import type {
    LeadConfiguration,
    LeadActionsConfig,
    LeadSourcesConfig,
    LeadStatusesConfig,
    ConfigurationType,
    EditableConfigItem
} from '../types';
import {
    DEFAULT_LEAD_ACTIONS,
    DEFAULT_LEAD_SOURCES,
    DEFAULT_LEAD_STATUSES
} from '../types';

/**
 * Service class for managing lead configuration
 */
export class LeadConfigService {

    /**
     * Get the current user's tenant ID
     */
    static async getCurrentTenantId(): Promise<string | null> {
        try {
            const authStore = useAuthStore();
            const userData = await authStore.getUserData();
            return userData?.tenantId || null;
        } catch (error) {
            console.error('Error getting current tenant ID:', error);
            return null;
        }
    }

    /**
     * Get tenant configuration data
     */
    static async getTenantConfiguration(): Promise<LeadConfiguration | null> {
        try {
            const tenantId = await this.getCurrentTenantId();
            if (!tenantId) {
                throw new Error('No tenant ID found');
            }

            const tenantStore = useTenantStore();
            const tenant = await tenantStore.getTenant(tenantId);

            if (!tenant) {
                throw new Error('Tenant not found');
            }

            // Return existing configuration or defaults
            return {
                lead_actions: tenant.lead_actions || DEFAULT_LEAD_ACTIONS,
                lead_sources: tenant.lead_sources || DEFAULT_LEAD_SOURCES,
                lead_statuses: tenant.lead_statuses || DEFAULT_LEAD_STATUSES
            };
        } catch (error) {
            console.error('Error getting tenant configuration:', error);
            return null;
        }
    }

    /**
     * Update tenant configuration
     */
    static async updateTenantConfiguration(
        configurationType: ConfigurationType,
        configuration: LeadActionsConfig | LeadSourcesConfig | LeadStatusesConfig
    ): Promise<boolean> {
        try {
            const tenantId = await this.getCurrentTenantId();
            if (!tenantId) {
                throw new Error('No tenant ID found');
            }

            const tenantStore = useTenantStore();
            await tenantStore.updateTenant(tenantId, {
                [configurationType]: configuration,
                updatedAt: new Date()
            });

            return true;
        } catch (error) {
            console.error('Error updating tenant configuration:', error);
            return false;
        }
    }

    /**
     * Subscribe to tenant configuration changes
     */
    static subscribeToTenantChanges(
        callback: (configuration: LeadConfiguration | null) => void
    ): (() => void) | null {
        try {
            const tenantStore = useTenantStore();

            this.getCurrentTenantId().then(tenantId => {
                if (!tenantId) {
                    callback(null);
                    return;
                }

                tenantStore.subscribeToTenantChanges(tenantId, (tenant) => {
                    if (tenant) {
                        const configuration: LeadConfiguration = {
                            lead_actions: tenant.lead_actions || DEFAULT_LEAD_ACTIONS,
                            lead_sources: tenant.lead_sources || DEFAULT_LEAD_SOURCES,
                            lead_statuses: tenant.lead_statuses || DEFAULT_LEAD_STATUSES
                        };
                        callback(configuration);
                    } else {
                        callback(null);
                    }
                });
            });

            // Return a function to unsubscribe
            return () => {
                this.getCurrentTenantId().then(tenantId => {
                    if (tenantId) {
                        tenantStore.unsubscribeFromTenantChanges(tenantId);
                    }
                });
            };
        } catch (error) {
            console.error('Error subscribing to tenant changes:', error);
            return null;
        }
    }

    /**
     * Unsubscribe from tenant changes
     */
    static async unsubscribeFromTenantChanges(): Promise<void> {
        try {
            const tenantId = await this.getCurrentTenantId();
            if (tenantId) {
                const tenantStore = useTenantStore();
                tenantStore.unsubscribeFromTenantChanges(tenantId);
            }
        } catch (error) {
            console.error('Error unsubscribing from tenant changes:', error);
        }
    }

    /**
     * Validate configuration item
     */
    static validateConfigItem(item: EditableConfigItem): { isValid: boolean; errors: Record<string, string> } {
        const errors: Record<string, string> = {};

        // Validate label
        if (!item.label || item.label.trim().length === 0) {
            errors.label = 'Label is required';
        } else if (item.label.trim().length > 50) {
            errors.label = 'Label must be 50 characters or less';
        }

        // Validate icon
        if (!item.icon || item.icon.trim().length === 0) {
            errors.icon = 'Icon is required';
        }

        // Validate background color
        if (!item.bg_color || !this.isValidColor(item.bg_color)) {
            errors.bg_color = 'Valid background color is required';
        }

        // Validate text color
        if (!item.text_color || !this.isValidColor(item.text_color)) {
            errors.text_color = 'Valid text color is required';
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * Check if a color is valid (hex format)
     */
    static isValidColor(color: string): boolean {
        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        return hexColorRegex.test(color);
    }

    /**
     * Convert configuration object to editable items array
     */
    static configToEditableItems(
        config: LeadActionsConfig | LeadSourcesConfig | LeadStatusesConfig,
        defaultKeys: string[] = []
    ): EditableConfigItem[] {
        return Object.entries(config).map(([key, item]) => ({
            key,
            ...item,
            isDefault: defaultKeys.includes(key),
            isEditing: false
        }));
    }

    /**
     * Convert editable items array back to configuration object
     */
    static editableItemsToConfig(
        items: EditableConfigItem[]
    ): LeadActionsConfig | LeadSourcesConfig | LeadStatusesConfig {
        const config: any = {};
        items.forEach(item => {
            const { key, isDefault, isEditing, ...configItem } = item;
            config[key] = configItem;
        });
        return config;
    }

    /**
     * Check if at least one item is enabled (to prevent disabling all)
     */
    static hasAtLeastOneEnabled(items: EditableConfigItem[]): boolean {
        return items.some(item => item.status === 'enabled');
    }

    /**
     * Generate a unique key for new configuration items
     */
    static generateUniqueKey(existingKeys: string[], baseName: string): string {
        const baseKey = baseName.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
        
        if (!existingKeys.includes(baseKey)) {
            return baseKey;
        }

        let counter = 1;
        let newKey = `${baseKey}_${counter}`;
        while (existingKeys.includes(newKey)) {
            counter++;
            newKey = `${baseKey}_${counter}`;
        }
        
        return newKey;
    }

    /**
     * Get default keys for a configuration type
     */
    static getDefaultKeys(configurationType: ConfigurationType): string[] {
        switch (configurationType) {
            case 'lead_actions':
                return Object.keys(DEFAULT_LEAD_ACTIONS);
            case 'lead_sources':
                return Object.keys(DEFAULT_LEAD_SOURCES);
            case 'lead_statuses':
                return Object.keys(DEFAULT_LEAD_STATUSES);
            default:
                return [];
        }
    }

    /**
     * Reset configuration to defaults
     */
    static async resetToDefaults(configurationType: ConfigurationType): Promise<boolean> {
        try {
            let defaultConfig;
            switch (configurationType) {
                case 'lead_actions':
                    defaultConfig = DEFAULT_LEAD_ACTIONS;
                    break;
                case 'lead_sources':
                    defaultConfig = DEFAULT_LEAD_SOURCES;
                    break;
                case 'lead_statuses':
                    defaultConfig = DEFAULT_LEAD_STATUSES;
                    break;
                default:
                    throw new Error('Invalid configuration type');
            }

            return await this.updateTenantConfiguration(configurationType, defaultConfig);
        } catch (error) {
            console.error('Error resetting to defaults:', error);
            return false;
        }
    }
}
