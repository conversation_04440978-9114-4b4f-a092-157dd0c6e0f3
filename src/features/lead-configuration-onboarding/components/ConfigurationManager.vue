<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { LeadConfigService } from '../services/leadConfigService';
import ConfigItemEditor from './ConfigItemEditor.vue';
import type { 
    ConfigurationType, 
    EditableConfigItem,
    LeadActionsConfig,
    LeadSourcesConfig,
    LeadStatusesConfig
} from '../types';

// Props
interface Props {
    configurationType: ConfigurationType;
    initialConfig: LeadActionsConfig | LeadSourcesConfig | LeadStatusesConfig;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
    update: [configurationType: ConfigurationType, config: any];
    changes: [hasChanges: boolean];
}>();

// Reactive state
const items = ref<EditableConfigItem[]>([]);
const showAddDialog = ref(false);
const editingItem = ref<EditableConfigItem | null>(null);
const hasChanges = ref(false);
const toast = useToast();
const confirm = useConfirm();

// Computed
const defaultKeys = computed(() => {
    return LeadConfigService.getDefaultKeys(props.configurationType);
});

const enabledItemsCount = computed(() => {
    return items.value.filter(item => item.status === 'enabled').length;
});

const canDisableItem = computed(() => (item: EditableConfigItem) => {
    if (item.status === 'disabled') return true;
    return enabledItemsCount.value > 1;
});

// Methods
const loadItems = () => {
    items.value = LeadConfigService.configToEditableItems(
        props.initialConfig,
        defaultKeys.value
    );
    hasChanges.value = false;
};

const saveChanges = async () => {
    try {
        const config = LeadConfigService.editableItemsToConfig(items.value);
        emit('update', props.configurationType, config);
        hasChanges.value = false;
    } catch (error) {
        console.error('Error saving changes:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save changes',
            life: 3000
        });
    }
};

const addNewItem = () => {
    editingItem.value = {
        key: '',
        label: '',
        icon: 'pi pi-circle',
        bg_color: '#3B82F6',
        text_color: '#FFFFFF',
        status: 'enabled',
        isDefault: false,
        isEditing: true
    };
    showAddDialog.value = true;
};

const editItem = (item: EditableConfigItem) => {
    editingItem.value = { ...item, isEditing: true };
    showAddDialog.value = true;
};

const handleItemSave = (savedItem: EditableConfigItem) => {
    const existingIndex = items.value.findIndex(item => item.key === savedItem.key);
    
    if (existingIndex >= 0) {
        // Update existing item
        items.value[existingIndex] = { ...savedItem, isEditing: false };
    } else {
        // Add new item
        const existingKeys = items.value.map(item => item.key);
        savedItem.key = LeadConfigService.generateUniqueKey(existingKeys, savedItem.label);
        items.value.push({ ...savedItem, isEditing: false });
    }
    
    hasChanges.value = true;
    showAddDialog.value = false;
    editingItem.value = null;
};

const handleItemCancel = () => {
    showAddDialog.value = false;
    editingItem.value = null;
};

const deleteItem = (item: EditableConfigItem) => {
    if (item.isDefault) {
        toast.add({
            severity: 'warn',
            summary: 'Warning',
            detail: 'Cannot delete default items',
            life: 3000
        });
        return;
    }

    confirm.require({
        message: `Are you sure you want to delete "${item.label}"?`,
        header: 'Confirm Delete',
        icon: 'pi pi-exclamation-triangle',
        rejectProps: {
            label: 'Cancel',
            severity: 'secondary',
            outlined: true
        },
        acceptProps: {
            label: 'Delete',
            severity: 'danger'
        },
        accept: () => {
            const index = items.value.findIndex(i => i.key === item.key);
            if (index >= 0) {
                items.value.splice(index, 1);
                hasChanges.value = true;
                toast.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Item deleted successfully',
                    life: 3000
                });
            }
        }
    });
};

const toggleItemStatus = (item: EditableConfigItem) => {
    if (item.status === 'enabled' && !canDisableItem.value(item)) {
        toast.add({
            severity: 'warn',
            summary: 'Warning',
            detail: 'At least one item must remain enabled',
            life: 3000
        });
        return;
    }

    item.status = item.status === 'enabled' ? 'disabled' : 'enabled';
    hasChanges.value = true;
};

const discardChanges = () => {
    confirm.require({
        message: 'Are you sure you want to discard all unsaved changes?',
        header: 'Confirm Discard',
        icon: 'pi pi-exclamation-triangle',
        rejectProps: {
            label: 'Cancel',
            severity: 'secondary',
            outlined: true
        },
        acceptProps: {
            label: 'Discard',
            severity: 'danger'
        },
        accept: () => {
            loadItems();
        }
    });
};

// Watchers
watch(() => props.initialConfig, loadItems, { immediate: true });
watch(hasChanges, (value) => emit('changes', value));
</script>

<template>
    <div class="configuration-manager">
        <!-- Actions Bar -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center gap-4">
                <Button
                    label="Add New"
                    icon="pi pi-plus"
                    @click="addNewItem"
                    size="small"
                />
                <div class="text-sm text-surface-600 dark:text-surface-400">
                    {{ items.length }} items ({{ enabledItemsCount }} enabled)
                </div>
            </div>
            
            <div v-if="hasChanges" class="flex gap-2">
                <Button
                    label="Discard"
                    icon="pi pi-times"
                    severity="secondary"
                    outlined
                    size="small"
                    @click="discardChanges"
                />
                <Button
                    label="Save Changes"
                    icon="pi pi-check"
                    size="small"
                    @click="saveChanges"
                />
            </div>
        </div>

        <!-- Items Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
                v-for="item in items"
                :key="item.key"
                :class="[
                    'config-item-card p-4 border rounded-lg transition-all duration-200',
                    item.status === 'enabled'
                        ? 'border-surface-200 dark:border-surface-700 bg-surface-0 dark:bg-surface-900'
                        : 'border-surface-300 dark:border-surface-600 bg-surface-100 dark:bg-surface-800 opacity-60'
                ]"
            >
                <!-- Item Header -->
                <div class="flex justify-between items-start mb-3">
                    <div class="flex items-center gap-2">
                        <div
                            :style="{ 
                                backgroundColor: item.bg_color, 
                                color: item.text_color 
                            }"
                            class="w-8 h-8 rounded-full flex items-center justify-center text-sm"
                        >
                            <i :class="item.icon"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-surface-900 dark:text-surface-0">
                                {{ item.label }}
                            </h4>
                            <div class="flex items-center gap-2 mt-1">
                                <Badge
                                    :value="item.status"
                                    :severity="item.status === 'enabled' ? 'success' : 'secondary'"
                                    size="small"
                                />
                                <Badge
                                    v-if="item.isDefault"
                                    value="default"
                                    severity="info"
                                    size="small"
                                />
                            </div>
                        </div>
                    </div>
                    
                    <!-- Item Actions -->
                    <div class="flex gap-1">
                        <Button
                            icon="pi pi-pencil"
                            size="small"
                            text
                            rounded
                            @click="editItem(item)"
                            v-tooltip.top="'Edit'"
                        />
                        <Button
                            :icon="item.status === 'enabled' ? 'pi pi-eye-slash' : 'pi pi-eye'"
                            size="small"
                            text
                            rounded
                            @click="toggleItemStatus(item)"
                            :disabled="item.status === 'enabled' && !canDisableItem(item)"
                            v-tooltip.top="item.status === 'enabled' ? 'Disable' : 'Enable'"
                        />
                        <Button
                            v-if="!item.isDefault"
                            icon="pi pi-trash"
                            size="small"
                            text
                            rounded
                            severity="danger"
                            @click="deleteItem(item)"
                            v-tooltip.top="'Delete'"
                        />
                    </div>
                </div>

                <!-- Item Details -->
                <div class="text-sm text-surface-600 dark:text-surface-400 space-y-1">
                    <div class="flex justify-between">
                        <span>Key:</span>
                        <code class="text-xs bg-surface-100 dark:bg-surface-800 px-1 rounded">
                            {{ item.key }}
                        </code>
                    </div>
                    <div class="flex justify-between">
                        <span>Icon:</span>
                        <code class="text-xs bg-surface-100 dark:bg-surface-800 px-1 rounded">
                            {{ item.icon }}
                        </code>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Colors:</span>
                        <div class="flex gap-1">
                            <div
                                :style="{ backgroundColor: item.bg_color }"
                                class="w-4 h-4 rounded border border-surface-300 dark:border-surface-600"
                                v-tooltip.top="`Background: ${item.bg_color}`"
                            ></div>
                            <div
                                :style="{ backgroundColor: item.text_color }"
                                class="w-4 h-4 rounded border border-surface-300 dark:border-surface-600"
                                v-tooltip.top="`Text: ${item.text_color}`"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-if="items.length === 0" class="text-center py-8">
            <i class="pi pi-inbox text-4xl text-surface-400 mb-4"></i>
            <h3 class="text-lg font-medium text-surface-600 dark:text-surface-400 mb-2">
                No items configured
            </h3>
            <p class="text-surface-500 dark:text-surface-500 mb-4">
                Add your first configuration item to get started
            </p>
            <Button
                label="Add Item"
                icon="pi pi-plus"
                @click="addNewItem"
            />
        </div>

        <!-- Add/Edit Dialog -->
        <ConfigItemEditor
            v-if="editingItem"
            :visible="showAddDialog"
            :item="editingItem"
            :existing-keys="items.map(item => item.key)"
            @save="handleItemSave"
            @cancel="handleItemCancel"
        />
    </div>
</template>

<style scoped>
.config-item-card {
    @apply hover:shadow-md;
}

.config-item-card:hover {
    @apply transform scale-105;
}
</style>
