/**
 * Base interface for lead configuration items
 */
export interface LeadConfigItem {
    bg_color: string;
    icon: string;
    label: string;
    text_color: string;
    status: 'enabled' | 'disabled';
}

/**
 * Lead actions configuration map
 */
export interface LeadActionsConfig {
    [key: string]: LeadConfigItem;
}

/**
 * Lead sources configuration map
 */
export interface LeadSourcesConfig {
    [key: string]: LeadConfigItem;
}

/**
 * Lead statuses configuration map
 */
export interface LeadStatusesConfig {
    [key: string]: LeadConfigItem;
}

/**
 * Complete lead configuration interface
 */
export interface LeadConfiguration {
    lead_actions: LeadActionsConfig;
    lead_sources: LeadSourcesConfig;
    lead_statuses: LeadStatusesConfig;
}

/**
 * Form validation errors interface
 */
export interface LeadConfigErrors {
    label: string;
    icon: string;
    bg_color: string;
    text_color: string;
}

/**
 * Configuration item for editing
 */
export interface EditableConfigItem extends LeadConfigItem {
    key: string;
    isDefault?: boolean;
    isEditing?: boolean;
}

/**
 * Default lead actions configuration
 */
export const DEFAULT_LEAD_ACTIONS: LeadActionsConfig = {
    booked: {
        bg_color: '#10B981',
        icon: 'pi pi-calendar-check',
        label: 'Booked',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    inquired: {
        bg_color: '#3B82F6',
        icon: 'pi pi-question-circle',
        label: 'Inquired',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    quoted: {
        bg_color: '#F59E0B',
        icon: 'pi pi-file-edit',
        label: 'Quoted',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    converted: {
        bg_color: '#8B5CF6',
        icon: 'pi pi-check-circle',
        label: 'Converted',
        text_color: '#FFFFFF',
        status: 'enabled'
    }
};

/**
 * Default lead sources configuration
 */
export const DEFAULT_LEAD_SOURCES: LeadSourcesConfig = {
    ai_call: {
        bg_color: '#6366F1',
        icon: 'pi pi-phone',
        label: 'AI Call',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    whatsapp: {
        bg_color: '#25D366',
        icon: 'pi pi-whatsapp',
        label: 'WhatsApp',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    facebook: {
        bg_color: '#1877F2',
        icon: 'pi pi-facebook',
        label: 'Facebook',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    email: {
        bg_color: '#EF4444',
        icon: 'pi pi-envelope',
        label: 'Email',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    call: {
        bg_color: '#059669',
        icon: 'pi pi-phone',
        label: 'Call',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    sms: {
        bg_color: '#059669',
        icon: 'pi pi-mobile',
        label: 'SMS',
        text_color: '#FFFFFF',
        status: 'enabled'
    }
};

/**
 * Default lead statuses configuration
 */
export const DEFAULT_LEAD_STATUSES: LeadStatusesConfig = {
    cold: {
        bg_color: '#6B7280',
        icon: 'pi pi-snowflake',
        label: 'Cold',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    warm: {
        bg_color: '#F59E0B',
        icon: 'pi pi-sun',
        label: 'Warm',
        text_color: '#FFFFFF',
        status: 'enabled'
    },
    hot: {
        bg_color: '#EF4444',
        icon: 'pi pi-fire',
        label: 'Hot',
        text_color: '#FFFFFF',
        status: 'enabled'
    }
};

/**
 * Available PrimeVue icons for selection
 */
export const AVAILABLE_ICONS = [
    { label: 'Calendar Check', value: 'pi pi-calendar-check' },
    { label: 'Question Circle', value: 'pi pi-question-circle' },
    { label: 'File Edit', value: 'pi pi-file-edit' },
    { label: 'Check Circle', value: 'pi pi-check-circle' },
    { label: 'Phone', value: 'pi pi-phone' },
    { label: 'WhatsApp', value: 'pi pi-whatsapp' },
    { label: 'Facebook', value: 'pi pi-facebook' },
    { label: 'Envelope', value: 'pi pi-envelope' },
    { label: 'Snowflake', value: 'pi pi-snowflake' },
    { label: 'Sun', value: 'pi pi-sun' },
    { label: 'Fire', value: 'pi pi-fire' },
    { label: 'Star', value: 'pi pi-star' },
    { label: 'Heart', value: 'pi pi-heart' },
    { label: 'User', value: 'pi pi-user' },
    { label: 'Users', value: 'pi pi-users' },
    { label: 'Building', value: 'pi pi-building' },
    { label: 'Home', value: 'pi pi-home' },
    { label: 'Globe', value: 'pi pi-globe' },
    { label: 'Map Marker', value: 'pi pi-map-marker' },
    { label: 'Clock', value: 'pi pi-clock' },
    { label: 'Calendar', value: 'pi pi-calendar' },
    { label: 'Tag', value: 'pi pi-tag' },
    { label: 'Tags', value: 'pi pi-tags' },
    { label: 'Flag', value: 'pi pi-flag' },
    { label: 'Bookmark', value: 'pi pi-bookmark' },
    { label: 'Thumbs Up', value: 'pi pi-thumbs-up' },
    { label: 'Thumbs Down', value: 'pi pi-thumbs-down' },
    { label: 'Chart Line', value: 'pi pi-chart-line' },
    { label: 'Chart Bar', value: 'pi pi-chart-bar' },
    { label: 'Money Bill', value: 'pi pi-money-bill' },
    { label: 'Shopping Cart', value: 'pi pi-shopping-cart' },
    { label: 'Gift', value: 'pi pi-gift' },
    { label: 'Trophy', value: 'pi pi-trophy' },
    { label: 'Medal', value: 'pi pi-medal' },
    { label: 'Shield', value: 'pi pi-shield' },
    { label: 'Lock', value: 'pi pi-lock' },
    { label: 'Key', value: 'pi pi-key' },
    { label: 'Eye', value: 'pi pi-eye' },
    { label: 'Search', value: 'pi pi-search' },
    { label: 'Filter', value: 'pi pi-filter' },
    { label: 'Sort', value: 'pi pi-sort' },
    { label: 'Plus', value: 'pi pi-plus' },
    { label: 'Minus', value: 'pi pi-minus' },
    { label: 'Times', value: 'pi pi-times' },
    { label: 'Check', value: 'pi pi-check' },
    { label: 'Exclamation Triangle', value: 'pi pi-exclamation-triangle' },
    { label: 'Info Circle', value: 'pi pi-info-circle' },
    { label: 'Bell', value: 'pi pi-bell' },
    { label: 'Comment', value: 'pi pi-comment' },
    { label: 'Comments', value: 'pi pi-comments' },
    { label: 'Share', value: 'pi pi-share-alt' },
    { label: 'Download', value: 'pi pi-download' },
    { label: 'Upload', value: 'pi pi-upload' },
    { label: 'Print', value: 'pi pi-print' },
    { label: 'Copy', value: 'pi pi-copy' },
    { label: 'Paste', value: 'pi pi-paste' },
    { label: 'Cut', value: 'pi pi-cut' },
    { label: 'Save', value: 'pi pi-save' },
    { label: 'Folder', value: 'pi pi-folder' },
    { label: 'File', value: 'pi pi-file' },
    { label: 'Image', value: 'pi pi-image' },
    { label: 'Video', value: 'pi pi-video' },
    { label: 'Music', value: 'pi pi-music' },
    { label: 'Microphone', value: 'pi pi-microphone' },
    { label: 'Volume Up', value: 'pi pi-volume-up' },
    { label: 'Volume Down', value: 'pi pi-volume-down' },
    { label: 'Volume Off', value: 'pi pi-volume-off' },
    { label: 'Play', value: 'pi pi-play' },
    { label: 'Pause', value: 'pi pi-pause' },
    { label: 'Stop', value: 'pi pi-stop' },
    { label: 'Forward', value: 'pi pi-forward' },
    { label: 'Backward', value: 'pi pi-backward' },
    { label: 'Step Forward', value: 'pi pi-step-forward' },
    { label: 'Step Backward', value: 'pi pi-step-backward' },
    { label: 'Refresh', value: 'pi pi-refresh' },
    { label: 'Sync', value: 'pi pi-sync' },
    { label: 'Undo', value: 'pi pi-undo' },
    { label: 'Redo', value: 'pi pi-redo' },
    { label: 'Power Off', value: 'pi pi-power-off' },
    { label: 'Cog', value: 'pi pi-cog' },
    { label: 'Wrench', value: 'pi pi-wrench' },
    { label: 'Palette', value: 'pi pi-palette' }
];

/**
 * Predefined color options for quick selection
 */
export const PREDEFINED_COLORS = [
    '#EF4444', // Red
    '#F97316', // Orange
    '#F59E0B', // Amber
    '#EAB308', // Yellow
    '#84CC16', // Lime
    '#22C55E', // Green
    '#10B981', // Emerald
    '#14B8A6', // Teal
    '#06B6D4', // Cyan
    '#0EA5E9', // Sky
    '#3B82F6', // Blue
    '#6366F1', // Indigo
    '#8B5CF6', // Violet
    '#A855F7', // Purple
    '#D946EF', // Fuchsia
    '#EC4899', // Pink
    '#F43F5E', // Rose
    '#6B7280', // Gray
    '#374151', // Gray Dark
    '#111827' // Gray Very Dark
];

/**
 * Configuration type for different sections
 */
export type ConfigurationType = 'lead_actions' | 'lead_sources' | 'lead_statuses';

/**
 * Configuration section metadata
 */
export interface ConfigurationSection {
    key: ConfigurationType;
    title: string;
    description: string;
    icon: string;
    defaultConfig: LeadActionsConfig | LeadSourcesConfig | LeadStatusesConfig;
}

/**
 * Available configuration sections
 */
export const CONFIGURATION_SECTIONS: ConfigurationSection[] = [
    {
        key: 'lead_actions',
        title: 'Lead Actions',
        description: 'Configure available actions for leads (e.g., Booked, Inquired, Quoted)',
        icon: 'pi pi-bolt',
        defaultConfig: DEFAULT_LEAD_ACTIONS
    },
    {
        key: 'lead_sources',
        title: 'Lead Sources',
        description: 'Configure lead source channels (e.g., WhatsApp, Facebook, Email)',
        icon: 'pi pi-share-alt',
        defaultConfig: DEFAULT_LEAD_SOURCES
    },
    {
        key: 'lead_statuses',
        title: 'Lead Statuses',
        description: 'Configure lead temperature statuses (e.g., Cold, Warm, Hot)',
        icon: 'pi pi-thermometer',
        defaultConfig: DEFAULT_LEAD_STATUSES
    }
];
