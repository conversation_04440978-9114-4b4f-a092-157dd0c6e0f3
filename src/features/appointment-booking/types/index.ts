import { Lead } from '@/entities/lead';

export interface LeadDetails {
    door_size?: string;
    door_colour?: string;
}

export interface AppointmentFormData {
    lead: Lead | null;
    id: string | null;
    tenantId?: string | null;
    eventId: string | null;
    action: string | null;
    appointmentDate: any | null;
    gps_coordinates: string;
    location_string: string;
    google_link: string;
    notes: string[];
    doorType: string;
    doorSize: string;
    customSize: string;
    doorColor: string;
    customColor: string;
    tradeIn: boolean;
    has_alternative_garage_access: boolean;
    wants_external_override: boolean;
    has_electrical_connection: boolean;
}

export interface AvailableSlotsResponse {
    available: boolean;
    date: number;
    slots: number[];
    message: string;
}

export interface AvailableDaysResponse {
    availableDays: number[];
    message: string;
}
