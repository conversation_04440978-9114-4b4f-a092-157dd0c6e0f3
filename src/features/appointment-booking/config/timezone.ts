/**
 * Timezone configuration for appointment booking feature
 * Hardcoded to use London/UK timezone
 */

export interface TimezoneConfig {
    timezone: string;
    locale: string;
    dateFormat: Intl.DateTimeFormatOptions;
    timeFormat: Intl.DateTimeFormatOptions;
    dateTimeFormat: Intl.DateTimeFormatOptions;
}

/**
 * Hardcoded timezone configuration - London/UK
 */
export const APPOINTMENT_TIMEZONE_CONFIG: TimezoneConfig = {
    timezone: 'Europe/London',
    locale: 'en-GB',
    dateFormat: {
        weekday: 'long',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        timeZone: 'Europe/London'
    },
    timeFormat: {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZone: 'Europe/London'
    },
    dateTimeFormat: {
        weekday: 'long',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZone: 'Europe/London'
    }
};

/**
 * Get the hardcoded timezone configuration
 */
export function getTimezoneConfig(): TimezoneConfig {
    return APPOINTMENT_TIMEZONE_CONFIG;
}

/**
 * Get the appointment timezone (always London/UK)
 */
export function getAppointmentTimezone(): string {
    return APPOINTMENT_TIMEZONE_CONFIG.timezone;
}

/**
 * Get timezone label for display
 */
export function getTimezoneLabel(): string {
    return 'London (GMT/BST)';
}

/**
 * Check if user's local timezone is different from appointment timezone
 */
export function isUserTimezoneDifferent(): boolean {
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return userTimezone !== APPOINTMENT_TIMEZONE_CONFIG.timezone;
}

/**
 * Get user's local timezone
 */
export function getUserTimezone(): string {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Get timezone difference message for display
 */
export function getTimezoneDifferenceMessage(): string {
    if (!isUserTimezoneDifferent()) {
        return '';
    }

    const userTimezone = getUserTimezone();
    const now = new Date();

    // Get current time in both timezones
    const appointmentTime = now.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: APPOINTMENT_TIMEZONE_CONFIG.timezone
    });

    const userTime = now.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: userTimezone
    });

    return `All appointment times are shown in London time (${appointmentTime}). Your local time is ${userTime}.`;
}
