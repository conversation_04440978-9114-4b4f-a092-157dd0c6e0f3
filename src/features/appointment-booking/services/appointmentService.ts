import { Appointment } from '@/entities/appointments';
import { requestAddAppointmentToCalendar, requestFetchAvailableSlots } from '@/shared';
import { Loader } from '@googlemaps/js-api-loader';

export const fetchAvailableSlots = async (preferredAppointmentDate: number, data: any, timezone?: string) => {
    // Include timezone in the request if provided
    const requestData = {
        preferredAppointmentDate: preferredAppointmentDate,
        selectedLocationLatLong: data.gps_coordinates,
        selectedLocation: data.location_string,
        ...(timezone && { timezone })
    };

    return await requestFetchAvailableSlots(requestData);
};

export const bookAppointment = async (data: any, timezone?: string) => {
    if (!data.action) data.action = 'add';

    // Include timezone information in appointment data
    const appointmentData = {
        ...data,
        ...(timezone && { timezone })
    } as Appointment;

    await requestAddAppointmentToCalendar(appointmentData);
};

// Google Maps Loader
export const loader = new Loader({
    apiKey: 'AIzaSyAoEnUYPSr9F0Ek5-lx35FzdC_evf0gwNE',
    version: 'weekly',
    libraries: ['places', 'geometry', 'routes']
});
