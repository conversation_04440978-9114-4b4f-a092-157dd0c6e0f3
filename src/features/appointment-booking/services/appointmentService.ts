import { Appointment } from '@/entities/appointments';
import { requestAddAppointmentToCalendar, requestFetchAvailableSlots } from '@/shared';
import { Loader } from '@googlemaps/js-api-loader';

export const fetchAvailableSlots = async (preferredAppointmentDate: number, data: any) => {
    console.log('====');
    console.log(data);
    return await requestFetchAvailableSlots({ preferredAppointmentDate: preferredAppointmentDate, selectedLocationLatLong: data.gps_coordinates, selectedLocation: data.location_string });
};

export const bookAppointment = async (data: any) => {
    if (!data.action) data.action = 'add';

    await requestAddAppointmentToCalendar(data as Appointment);
};

// Google Maps Loader
export const loader = new Loader({
    apiKey: 'AIzaSyAoEnUYPSr9F0Ek5-lx35FzdC_evf0gwNE',
    version: 'weekly',
    libraries: ['places', 'geometry', 'routes']
});
