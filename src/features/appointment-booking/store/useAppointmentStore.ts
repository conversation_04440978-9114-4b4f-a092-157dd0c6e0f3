import { defineStore } from 'pinia';
import { ref } from 'vue';
import { fetchAvailableSlots, bookAppointment } from '../services/appointmentService';
import { AppointmentFormData } from '../types';
import { Lead } from '@/entities/lead';
import { getAppointmentTimezone } from '../config/timezone';
import { isDateWeekendInLondon } from '@/features/appointment-booking/utils';

export const useAppointmentStore = defineStore('appointment', () => {
    // Get hardcoded appointment timezone
    const appointmentTimezone = getAppointmentTimezone();
    const formData = ref<AppointmentFormData>({
        id: '',
        action: 'add',
        appointmentDate: null,
        eventId: null,
        gps_coordinates: '',
        location_string: '',
        google_link: '',
        notes: [''],
        lead: { name: '', email: '', phone: '' },
        doorType: '',
        doorSize: '',
        customSize: '',
        doorColor: '',
        customColor: '',
        tradeIn: false,
        has_alternative_garage_access: false,
        wants_external_override: false,
        has_electrical_connection: false
    });

    const availableSlots = ref<number[]>([]);
    const travelEstimates = ref<any[]>([]);
    const noSlotsFoundOnDates = ref<string[]>([]);
    const isLoading = ref(false);
    const error = ref<string | null>(null);
    const isSuccess = ref(false);
    const noSlotsFound = ref(false);
    const lastScannedDate = ref<number | null>(null);
    const searchStartDate = ref<number | null>(null);
    const searchingDate = ref<number | null>(null);

    const resetState = () => {
        availableSlots.value = [];
        noSlotsFound.value = false;
        lastScannedDate.value = null;
        searchStartDate.value = null;
        error.value = null;
    };
    const setLead = (lead: Lead) => {
        if (lead.selectedBooking) {
            formData.value = JSON.parse(JSON.stringify(lead.selectedBooking));
        }

        if (lead.address) {
            formData.value.location_string = (lead.address as string) || '';
        }
        formData.value.action = lead?.booking_action || 'add';

        formData.value.lead = lead;
    };
    const searchNextAvailableSlot = async (startDate: number) => {
        isLoading.value = true;
        resetState();
        searchStartDate.value = startDate;
        let currentDate = startDate;
        const oneDay = 24 * 60 * 60; // seconds in a day
        let slotsFound = false;

        try {
            for (let i = 0; i < 7; i++) {
                const tempDate = new Date(currentDate * 1000);

                // Skip Saturday (6) and Sunday (0)
                if (isDateWeekendInLondon(tempDate)) {
                    currentDate += oneDay;
                    continue;
                }

                searchingDate.value = currentDate;
                const response = await fetchAvailableSlots(currentDate, formData.value, appointmentTimezone);
                if (response !== null) {
                    lastScannedDate.value = currentDate;

                    if (response?.available && response?.slots.length > 0) {
                        availableSlots.value = response?.slots.filter((slot: any) => {
                            const date = new Date(slot * 1000);
                            const hour = date.getHours();
                            return hour >= 9 && hour < 12;
                        });

                        travelEstimates.value = response?.travelEstimates ?? [];

                        if (!availableSlots.value) {
                            noSlotsFoundOnDates.value.push(new Date(currentDate * 1000).toISOString().split('T')[0]);
                        }
                        if (availableSlots.value && availableSlots.value?.length > 0) {
                            formData.value.appointmentDate = response?.date;
                            slotsFound = true;
                            break;
                        }
                    }

                    currentDate += oneDay;
                } else {
                    currentDate += oneDay;
                }
            }

            if (!slotsFound) {
                noSlotsFound.value = true;
            }
        } catch (err) {
            error.value = 'Failed to load available time slots.';
        } finally {
            isLoading.value = false;
        }
    };

    const loadNextMonth = () => {
        if (lastScannedDate.value) {
            const nextDay = lastScannedDate.value + 24 * 60 * 60;
            searchNextAvailableSlot(nextDay);
        }
    };

    const submitAppointment = async () => {
        isLoading.value = true;
        error.value = null;
        try {
            if (formData.value.lead) formData.value.lead.address = formData.value.location_string;
            await bookAppointment(formData.value, appointmentTimezone);
            isSuccess.value = true;
        } catch (err) {
            error.value = 'Failed to book appointment.';
        } finally {
            isLoading.value = false;
        }
    };

    return {
        formData,
        availableSlots,
        travelEstimates,
        noSlotsFoundOnDates,
        isLoading,
        error,
        isSuccess,
        noSlotsFound,
        searchStartDate,
        lastScannedDate,
        searchingDate,
        appointmentTimezone,
        searchNextAvailableSlot,
        loadNextMonth,
        submitAppointment,
        setLead
    };
});
