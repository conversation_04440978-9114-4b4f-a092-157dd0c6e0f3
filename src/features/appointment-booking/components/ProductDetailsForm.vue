<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from 'vue';

const props = defineProps<{
    modelValue: any;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void;
}>();

const doorTypes = [
    { name: 'Double Roller Garage Doors', icon: 'pi-door-open' },
    { name: 'Electric Garage Doors', icon: 'pi-bolt' },
    { name: 'Roller Garage Doors', icon: 'pi-arrows-v' }
];

const sizes = [
    { name: 'Single Door', description: '2.4m wide' },
    { name: 'Large Door', description: '2.4m - 3.5m wide' },
    { name: 'Double Door', description: '3.5m - 5.0m wide' },
    { name: 'Custom Size', description: 'Specify exact dimensions' }
];

const colorGroups = [
    {
        name: 'Standard Colors',
        colors: ['Classic White', 'Anthracite Grey', 'Deep Black', 'Soft Beige', 'Warm Brown']
    },
    {
        name: 'Premium Colors',
        colors: ['Chartwell Green', 'Steel Blue', 'Golden Oak', 'Rosewood']
    },
    {
        name: 'Additional Colors',
        colors: ['Light Grey', 'Red', 'Green', 'Fir Green', 'Dark Brown', 'Brown', 'Beige', 'Cream', 'Silver', 'Other']
    }
];

// Flattened colors array for the dropdown
const colors = computed(() => {
    return colorGroups.flatMap((group) => group.colors);
});

// Check if form is complete
const isFormComplete = computed(() => {
    const { doorType, doorSize, doorColor } = props.modelValue;

    if (!doorType || !doorSize || !doorColor) return false;

    if (doorSize === 'Custom Size' && !props.modelValue.customSize) return false;
    if (doorColor === 'Other' && !props.modelValue.customColor) return false;

    return true;
});

// Get icon for door type
const getDoorTypeIcon = (type) => {
    const doorType = doorTypes.find((dt) => dt.name === type);
    return doorType ? doorType.icon : 'pi-door-open';
};

function getColorHex(colorName) {
    const colorMap = {
        'Classic White': '#F5F5F5',
        'Anthracite Grey': '#293133',
        'Deep Black': '#0A0A0A',
        'Soft Beige': '#E8DCCA',
        'Warm Brown': '#6E4C1E',
        'Chartwell Green': '#7BA05B',
        'Steel Blue': '#4682B4',
        'Golden Oak': '#B68D40',
        Rosewood: '#65000B',
        'Light Grey': '#D3D3D3',
        Red: '#C41E3A',
        Green: '#228B22',
        'Fir Green': '#386150',
        'Dark Brown': '#3B2F2F',
        Brown: '#964B00',
        Beige: '#F5F5DC',
        Cream: '#FFFDD0',
        Silver: '#C0C0C0',
        Other: '#CCCCCC'
    };

    return colorMap[colorName] || '#CCCCCC';
}
</script>

<template>
    <div class="product-details-form">
        <div class="form-header">
            <h4>Product Specifications</h4>
            <p>Tell us about the garage door you're interested in</p>
        </div>

        <div class="form-content">
            <!-- Door Type Selection -->
            <!--
            <div class="form-section">
                <label class="section-label">Door Type</label>
                <div class="door-type-options">
                    <div v-for="type in doorTypes" :key="type.name" class="door-type-option" :class="{ selected: props.modelValue.doorType === type.name }" @click="props.modelValue.doorType = type.name">
                        <i :class="`pi ${type.icon}`"></i>
                        <span>{{ type.name }}</span>
                    </div>
                </div>
            </div>
            -->
            <!-- Door Size Selection -->
            <div class="form-section">
                <label class="section-label">Door Size</label>
                <div class="size-options">
                    <div v-for="size in sizes" :key="size.name" class="size-option" :class="{ selected: props.modelValue.doorSize === size.name }" @click="props.modelValue.doorSize = size.name">
                        <div class="size-name">{{ size.name }}</div>
                        <div class="size-description">{{ size.description }}</div>
                    </div>
                </div>

                <!-- Custom Size Input -->
                <div v-if="props.modelValue.doorSize === 'Custom Size'" class="custom-size-input">
                    <IconField class="w-full">
                        <InputIcon>
                            <i class="pi pi-hammer"></i>
                        </InputIcon>
                        <InputText v-model="props.modelValue.customSize" placeholder="Enter dimensions (e.g. 4.2m x 2.5m)" class="w-full" />
                    </IconField>
                </div>
            </div>

            <!-- Door Color Selection -->
            <div class="form-section">
                <label class="section-label">Door Color</label>
                <Dropdown v-model="props.modelValue.doorColor" :options="colors" placeholder="Select Color" class="w-full color-dropdown" optionLabel="">
                    <template #value="slotProps">
                        <div v-if="slotProps.value" class="color-option">
                            <div class="color-swatch" :style="{ backgroundColor: getColorHex(slotProps.value) }"></div>
                            <span>{{ slotProps.value }}</span>
                        </div>
                        <span v-else>Select Color</span>
                    </template>

                    <template #option="slotProps">
                        <div class="color-option">
                            <div class="color-swatch" :style="{ backgroundColor: getColorHex(slotProps.option) }"></div>
                            <span>{{ slotProps.option }}</span>
                        </div>
                    </template>
                </Dropdown>

                <!-- Custom Color Input -->
                <div v-if="props.modelValue.doorColor === 'Other'" class="custom-color-input">
                    <IconField class="w-full">
                        <InputIcon>
                            <i class="pi pi-palette"></i>
                        </InputIcon>
                        <InputText v-model="props.modelValue.customColor" placeholder="Specify color" class="w-full" />
                    </IconField>
                </div>
            </div>

            <!-- Additional Options -->
            <div class="form-section">
                <label class="section-label">Additional Options</label>
                <div class="additional-options">
                    <div class="option-card">
                        <div class="option-content">
                            <div class="option-icon">
                                <i class="pi pi-bolt"></i>
                            </div>
                            <div class="option-details">
                                <div class="option-title">Electrical Connection</div>
                                <div class="option-description">Do you have an existing electrical connection for the garage door?</div>
                            </div>
                        </div>
                        <div class="option-toggle">
                            <InputSwitch v-model="props.modelValue.has_electrical_connection" />
                        </div>
                    </div>
                    <div class="option-card">
                        <div class="option-content">
                            <div class="option-icon">
                                <i class="pi pi-warehouse"></i>
                            </div>
                            <div class="option-details">
                                <div class="option-title">Alternative Garage Access</div>
                                <div class="option-description">Does your property have an alternative garage access?</div>
                            </div>
                        </div>
                        <div class="option-toggle">
                            <InputSwitch v-model="props.modelValue.has_alternative_garage_access" />
                        </div>
                    </div>
                    <div class="option-card" v-if="props.modelValue.has_alternative_garage_access === false">
                        <div class="option-content">
                            <div class="option-icon">
                                <i class="pi pi-server"></i>
                            </div>
                            <div class="option-details">
                                <div class="option-title">External Override</div>
                                <div class="option-description">Do you want an external override for the garage door?</div>
                            </div>
                        </div>
                        <div class="option-toggle">
                            <InputSwitch v-model="props.modelValue.wants_external_override" />
                        </div>
                    </div>
                    <div class="option-card">
                        <div class="option-content">
                            <div class="option-icon">
                                <i class="pi pi-sync"></i>
                            </div>
                            <div class="option-details">
                                <div class="option-title">Trade-in Old Door</div>
                                <div class="option-description">We'll remove and dispose of your existing door</div>
                            </div>
                        </div>
                        <div class="option-toggle">
                            <InputSwitch v-model="props.modelValue.tradeIn" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="isFormComplete" class="form-summary">
            <div class="summary-header">
                <i class="pi pi-check-circle"></i>
                <span>Product Selection Complete</span>
            </div>
            <div class="summary-details">
                <div class="summary-item">
                    <i :class="`pi ${getDoorTypeIcon(props.modelValue.doorType)}`"></i>
                    <span>{{ props.modelValue.doorType }}</span>
                </div>
                <div class="summary-item">
                    <i class="pi pi-arrows-h"></i>
                    <span>
                        {{ props.modelValue.doorSize }}
                        <template v-if="props.modelValue.doorSize === 'Custom Size'"> ({{ props.modelValue.customSize }}) </template>
                    </span>
                </div>
                <div class="summary-item">
                    <i class="pi pi-palette"></i>
                    <span>
                        {{ props.modelValue.doorColor }}
                        <template v-if="props.modelValue.doorColor === 'Other'"> ({{ props.modelValue.customColor }}) </template>
                    </span>
                </div>
                <div v-if="props.modelValue.tradeIn" class="summary-item">
                    <i class="pi pi-sync"></i>
                    <span>Trade-in included</span>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.product-details-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 0.5rem;
}

.form-header {
    margin-bottom: 0.5rem;
}

.form-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.25rem 0;
}

.form-header p {
    font-size: 0.9rem;
    color: var(--text-color-secondary);
    margin: 0;
}

.form-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.section-label {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-color);
}

/* Door Type Selection */
.door-type-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.door-type-option {
    flex: 1;
    min-width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: var(--surface-card);
    border: 1px solid var(--surface-border);
    cursor: pointer;
    transition: all 0.2s ease;
}

.door-type-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.door-type-option.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

.door-type-option i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Size Selection */
.size-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
}

.size-option {
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--surface-card);
    border: 1px solid var(--surface-border);
    cursor: pointer;
    transition: all 0.2s ease;
}

.size-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.size-option.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

.size-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.size-description {
    font-size: 0.8rem;
    opacity: 0.8;
}

.custom-size-input,
.custom-color-input {
    margin-top: 0.75rem;
}

/* Color Selection */
.color-dropdown {
    width: 100%;
}

.color-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.color-swatch {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    border: 1px solid var(--surface-border);
}

/* Additional Options */
.additional-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.option-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: var(--surface-card);
    border: 1px solid var(--surface-border);
}

.option-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.option-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--primary-color-lighten);
    color: var(--primary-color);
}

.option-icon i {
    font-size: 1.25rem;
}

.option-details {
    display: flex;
    flex-direction: column;
}

.option-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.option-description {
    font-size: 0.85rem;
    color: var(--text-color-secondary);
}

/* Form Summary */
.form-summary {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: var(--surface-hover);
    border: 1px solid var(--surface-border);
}

.summary-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--primary-color);
}

.summary-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.summary-item i {
    color: var(--primary-color);
}

/* Helper function for color swatches */
</style>
