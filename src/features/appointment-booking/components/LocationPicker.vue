<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { Loader } from '@googlemaps/js-api-loader';
import { loader } from '@/features/appointment-booking/services/appointmentService';

const map = ref<google.maps.Map | null>(null);
const marker = ref<google.maps.Marker | null>(null);
const isGoogleMapsLoaded = ref(false);
const isLoading = ref(true);
const locationLink = ref('');
const geocoder = ref<google.maps.Geocoder | null>(null);
const locationString = ref('');
const addressFound = ref(false);

const props = defineProps<{
    modelValue: {
        location_string: string | null;
        gps_coordinates: string | null;
    };
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: { location_string: string; gps_coordinates: string; google_link: string }): void;
}>();

// Initialize locationString from props
watch(
    () => props.modelValue,
    (newValue) => {
        locationString.value = newValue.location_string || '';

        if (newValue.gps_coordinates && map.value && isGoogleMapsLoaded.value) {
            const [lat, lng] = newValue.gps_coordinates.split(',').map(Number);
            if (!isNaN(lat) && !isNaN(lng)) {
                updateMarker(lat, lng);
                map.value.setCenter({ lat, lng });
                map.value.setZoom(15);
                updateLocationLink(lat, lng);

                // If we have coordinates but no address, try to get the address
                if (!newValue.location_string || newValue.location_string.includes(',')) {
                    getAddressFromCoordinates(lat, lng);
                }
            }
        }
    },
    { immediate: true }
);

onMounted(async () => {
    try {
        await loader.load();
        console.log('Google Maps API loaded successfully');
        isGoogleMapsLoaded.value = true;
        geocoder.value = new google.maps.Geocoder();
        initMap();
        isLoading.value = false;
    } catch (error) {
        console.error('Error loading Google Maps API:', error);
        isLoading.value = false;
    }
});

const initMap = () => {
    if (!isGoogleMapsLoaded.value) {
        console.error('Google Maps API not loaded yet');
        return;
    }

    // Initialize map with custom styling
    const mapOptions = {
        center: { lat: 51.5074, lng: -0.1278 }, // London default
        zoom: 12,
        mapTypeId: 'roadmap',
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'on' }]
            },
            {
                featureType: 'transit',
                elementType: 'labels',
                stylers: [{ visibility: 'on' }]
            }
        ]
    };

    const mapElement = document.getElementById('map');
    if (!mapElement) {
        console.error('Map element not found');
        return;
    }

    map.value = new google.maps.Map(mapElement, mapOptions);

    // If coordinates already exist in props, set marker
    if (props.modelValue.gps_coordinates) {
        const [lat, lng] = props.modelValue.gps_coordinates.split(',').map(Number);
        if (!isNaN(lat) && !isNaN(lng)) {
            updateMarker(lat, lng);
            map.value.setCenter({ lat, lng });
            map.value.setZoom(15);
            updateLocationLink(lat, lng);

            // If we have coordinates but no address, try to get the address
            if (!props.modelValue.location_string || props.modelValue.location_string.includes(',')) {
                getAddressFromCoordinates(lat, lng);
            }
        }
    }

    // Marker interaction
    map.value.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (event.latLng && map.value) {
            const coords = event.latLng.toJSON();
            updateMarker(coords.lat, coords.lng);
            updateLocationLink(coords.lat, coords.lng);
            getAddressFromCoordinates(coords.lat, coords.lng);
        }
    });

    // Places Autocomplete
    const input = document.getElementById('location-search') as HTMLInputElement;
    if (!input) {
        console.error('Location search input not found');
        return;
    }

    const autocomplete = new google.maps.places.Autocomplete(input, {
        fields: ['formatted_address', 'geometry', 'name', 'place_id']
    });

    autocomplete.addListener('place_changed', () => {
        const place = autocomplete.getPlace();
        if (place.geometry?.location) {
            const coords = place.geometry.location.toJSON();
            updateMarker(coords.lat, coords.lng);
            map.value?.setCenter(place.geometry.location);
            map.value?.setZoom(15);
            updateLocationLink(coords.lat, coords.lng);

            const address = place.formatted_address || input.value;
            locationString.value = address;
            addressFound.value = true;
            emit('update:modelValue', {
                location_string: address,
                gps_coordinates: `${coords.lat},${coords.lng}`,
                google_link: locationLink.value
            });
        }
    });
};

// Function to get address from coordinates
const getAddressFromCoordinates = (lat: number, lng: number) => {
    if (!geocoder.value) {
        console.error('Geocoder not initialized');
        updateWithCoordinates(lat, lng);
        return;
    }

    geocoder.value.geocode({ location: { lat, lng } }, (results, status) => {
        if (status === 'OK' && results && results[0]) {
            const formattedAddress = results[0].formatted_address;
            locationString.value = formattedAddress;
            addressFound.value = true;
            emit('update:modelValue', {
                location_string: formattedAddress,
                gps_coordinates: `${lat},${lng}`,
                google_link: locationLink.value
            });
        } else {
            console.warn('Geocoder failed due to: ' + status);
            updateWithCoordinates(lat, lng);
        }
    });
};

// Fallback to using coordinates as the location string
const updateWithCoordinates = (lat: number, lng: number) => {
    const coordsString = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    locationString.value = coordsString;
    addressFound.value = false;
    emit('update:modelValue', {
        location_string: coordsString,
        gps_coordinates: `${lat},${lng}`,
        google_link: locationLink.value
    });
};

const updateMarker = (lat: number, lng: number) => {
    if (marker.value) {
        marker.value.setPosition({ lat, lng });
    } else if (map.value) {
        marker.value = new google.maps.Marker({
            position: { lat, lng },
            map: map.value,
            animation: google.maps.Animation.DROP
        });
    }
};

const updateLocationLink = (lat: number, lng: number) => {
    locationLink.value = `https://www.google.com/maps?q=${lat},${lng}`;
};

const copyLocationLink = () => {
    if (locationLink.value) {
        navigator.clipboard
            .writeText(locationLink.value)
            .then(() => {
                // Could add a toast notification here
                console.log('Location link copied to clipboard');
            })
            .catch((err) => {
                console.error('Could not copy text: ', err);
            });
    }
};

// Handle manual input changes
const handleLocationInputChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    locationString.value = target.value;
};
</script>

<template>
    <div class="location-picker-container">
        <!-- Header -->
        <div class="location-header">
            <h4>Select Location</h4>
            <p>Search for an address or click on the map to set a location</p>
        </div>

        <!-- Search Input with Icon -->
        <div class="search-container">
            <span class="p-input-icon-left w-full">
                <InputText id="location-search" v-model="locationString" placeholder="Enter an address or place name" class="w-full" @input="handleLocationInputChange" />
            </span>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="map-loading">
            <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
            <p>Loading map...</p>
        </div>

        <!-- Map Container -->
        <div id="map" class="map-container" :class="{ 'map-has-marker': marker !== null }"></div>

        <!-- Location Details -->
        <div v-if="props.modelValue.gps_coordinates" class="location-details">
            <div class="location-info">
                <i class="pi pi-map-marker location-icon"></i>
                <div class="location-text">
                    <div class="location-address">
                        <span v-if="addressFound" class="address-found">
                            {{ props.modelValue.location_string }}
                        </span>
                        <span v-else class="coordinates-only">
                            {{ props.modelValue.location_string }}
                        </span>
                    </div>
                    <div class="location-coordinates">{{ props.modelValue.gps_coordinates }}</div>
                </div>
            </div>

            <div class="location-actions">
                <Button v-if="locationLink" icon="pi pi-link" label="Copy Link" class="p-button-sm p-button-outlined" @click="copyLocationLink" v-tooltip.top="'Copy Google Maps link'" />
            </div>
        </div>
    </div>
</template>

<style scoped>
.location-picker-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background-color: var(--surface-ground);
    border-radius: 0.5rem;
    padding: 1rem;
}

.location-header {
    margin-bottom: 0.5rem;
}

.location-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.25rem 0;
}

.location-header p {
    font-size: 0.9rem;
    color: var(--text-color-secondary);
    margin: 0;
}

.search-container {
    margin-bottom: 0.5rem;
}

.map-container {
    height: 350px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.map-has-marker {
    border: 2px solid var(--primary-color);
}

.map-loading {
    height: 350px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-card);
    border-radius: 12px;
    color: var(--text-color-secondary);
}

.map-loading p {
    margin-top: 1rem;
}

.location-details {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background-color: var(--surface-card);
    border-radius: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.location-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    min-width: 0;
}

.location-icon {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.location-text {
    flex: 1;
    min-width: 0;
}

.location-address {
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.address-found {
    color: var(--text-color);
}

.coordinates-only {
    color: var(--text-color-secondary);
    font-style: italic;
}

.location-coordinates {
    font-size: 0.85rem;
    color: var(--text-color-secondary);
}

.location-actions {
    display: flex;
    gap: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .location-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .location-actions {
        align-self: flex-end;
    }
}
</style>
