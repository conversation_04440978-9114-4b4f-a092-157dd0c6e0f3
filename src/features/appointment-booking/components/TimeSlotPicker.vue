<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from 'vue';

const props = defineProps<{
    slots: number[];
    selectedSlot?: number;
}>();

const emit = defineEmits<{
    (e: 'select', time: number): void;
}>();

const selected = ref(props.selectedSlot);

const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const getTimeOfDay = (timestamp: number) => {
    const hours = new Date(timestamp * 1000).getHours();
    if (hours < 12) return 'morning';
    if (hours < 17) return 'afternoon';
    return 'evening';
};

const groupedSlots = computed(() => {
    const groups = {
        morning: [] as number[],
        afternoon: [] as number[],
        evening: [] as number[]
    };

    props.slots.forEach((slot) => {
        groups[getTimeOfDay(slot)].push(slot);
    });

    return groups;
});

const handleSelect = (slot: number) => {
    selected.value = slot;
    emit('select', slot);
};

const isSlotSelected = (slot: number) => {
    return selected.value === slot;
};
</script>

<template>
    <div class="time-slot-picker">
        <!-- Morning slots -->
        <div v-if="groupedSlots.morning.length" class="time-group">
            <div class="time-group-header">
                <i class="pi pi-sun"></i>
                <span>Morning</span>
            </div>
            <div class="time-slots">
                <div v-for="slot in groupedSlots.morning" :key="slot" class="time-slot-wrapper">
                    <button class="time-slot" :class="{ selected: isSlotSelected(slot) }" @click="handleSelect(slot)">
                        <span class="time">{{ formatTime(slot) }}</span>
                        <i v-if="isSlotSelected(slot)" class="pi pi-check-circle"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Afternoon slots -->
        <div v-if="groupedSlots.afternoon.length" class="time-group">
            <div class="time-group-header">
                <i class="pi pi-cloud"></i>
                <span>Afternoon</span>
            </div>
            <div class="time-slots">
                <div v-for="slot in groupedSlots.afternoon" :key="slot" class="time-slot-wrapper">
                    <button class="time-slot" :class="{ selected: isSlotSelected(slot) }" @click="handleSelect(slot)">
                        <span class="time">{{ formatTime(slot) }}</span>
                        <i v-if="isSlotSelected(slot)" class="pi pi-check-circle"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Evening slots -->
        <div v-if="groupedSlots.evening.length" class="time-group">
            <div class="time-group-header">
                <i class="pi pi-moon"></i>
                <span>Evening</span>
            </div>
            <div class="time-slots">
                <div v-for="slot in groupedSlots.evening" :key="slot" class="time-slot-wrapper">
                    <button class="time-slot" :class="{ selected: isSlotSelected(slot) }" @click="handleSelect(slot)">
                        <span class="time">{{ formatTime(slot) }}</span>
                        <i v-if="isSlotSelected(slot)" class="pi pi-check-circle"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- No slots message -->
        <div v-if="!slots.length" class="no-slots">
            <i class="pi pi-calendar-times"></i>
            <p>No available time slots for this date</p>
        </div>
    </div>
</template>

<style scoped>
.time-slot-picker {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
}

.time-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.time-group-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--surface-border);
}

.time-group-header i {
    color: var(--primary-color);
    font-size: 1rem;
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .time-slots {
        grid-template-columns: repeat(4, 1fr);
    }
}

.time-slot-wrapper {
    position: relative;
}

.time-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.75rem 0.5rem;
    border-radius: 0.5rem;
    background-color: var(--surface-card);
    border: 1px solid var(--surface-border);
    color: var(--text-color);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.time-slot:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.time-slot.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

.time-slot.selected i {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    font-size: 0.75rem;
}

.time {
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.no-slots {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--text-color-secondary);
    text-align: center;
}

.no-slots i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

/* Animation for selection */
.time-slot::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.time-slot:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0) translate(-50%, -50%);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20) translate(-50%, -50%);
        opacity: 0;
    }
}
</style>
