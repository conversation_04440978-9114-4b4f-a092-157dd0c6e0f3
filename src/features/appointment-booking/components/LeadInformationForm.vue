<script setup lang="ts">
import { defineProps, defineEmits, ref, watch, computed } from 'vue';
import { validateEmail, validateRequired } from '@/utils/formvalidation';

const props = defineProps<{
    modelValue: any;
    noValidation?: boolean | undefined;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void;
    (e: 'editLocation'): void;
    (e: 'hasErrors', hasErrors: boolean): void;
}>();

// Local copy of the model to avoid direct mutation
const localModel = ref(JSON.parse(JSON.stringify(props.modelValue)));

// Validation states
const errors = ref({
    name: '',
    phone: '',
    email: ''
});

// Basic validation functions
const validateName = () => {
    if (props.noValidation) return true;
    const nameError = validateRequired(localModel.value.lead.name);
    errors.value.name = nameError;
    return !nameError;
};

const validatePhone = () => {
    if (props.noValidation) return true;
    // UK phone number regex: Accepts formats like +44 7123 456789, 07123 456789, (0)7123 456789
    const ukPhoneRegex = /^(\+44\s?7\d{3}|\(?07\d{3}\)?)\s?\d{3}\s?\d{3}$/;

    if (!localModel.value.lead.phone) {
        errors.value.phone = 'Phone number is required';
        return false;
    }

    // if (!ukPhoneRegex.test(localModel.value.lead.phone)) {
    //     errors.value.phone = 'Please enter a valid UK mobile number';
    //     localModel.value.lead.phone = '';
    //     return false;
    // }

    errors.value.phone = '';
    return true;
};

const validateEmailEl = () => {
    if (props.noValidation) return true;
    if (!localModel.value.lead.email) {
        errors.value.email = 'Email is required';
        return false;
    }
    // const emailError = validateEmail(localModel.value.lead.email);
    // errors.value.email = emailError;
    // localModel.value.lead.email = '';
    return true;
};

// Validate all fields
const validateAll = () => {
    const nameValid = validateName();
    const phoneValid = validatePhone();
    const emailValid = validateEmailEl();

    emit('hasErrors', !(nameValid && phoneValid && emailValid));
    return nameValid && phoneValid && emailValid;
};

// Use a debounced watcher to prevent infinite recursion
let updateTimeout = null;
watch(
    localModel.value,
    (newValue) => {
        // Clear any existing timeout
        if (updateTimeout) clearTimeout(updateTimeout);

        // Set a new timeout to debounce the update
        updateTimeout = setTimeout(() => {
            emit('update:modelValue', JSON.parse(JSON.stringify(newValue)));
            validateAll();
        }, 100);
    },
    { deep: true }
);

// Watch for external changes to modelValue
watch(
    () => props.modelValue,
    (newValue) => {
        if (JSON.stringify(newValue) !== JSON.stringify(localModel.value)) {
            localModel.value = JSON.parse(JSON.stringify(newValue));
        }
    },
    { deep: true }
);
</script>

<template>
    <div class="lead-information-form">
        <div class="form-header">
            <h4>Personal Details</h4>
            {{}}
            <p>Please provide your contact details</p>
        </div>

        <div class="form-grid">
            <!-- Full Name -->
            <div class="form-field">
                <label for="lead-name">Full Name</label>
                <IconField class="w-full">
                    <InputIcon>
                        <i class="pi pi-user"></i>
                    </InputIcon>
                    <InputText id="lead-name" v-model="localModel.lead.name" @blur="validateName" :class="{ 'p-invalid': errors.name }" placeholder="John Doe" class="w-full" />
                </IconField>
                <small v-if="errors.name" class="error-message">{{ errors.name }}</small>
            </div>

            <!-- Phone -->
            <div class="form-field">
                <label for="lead-phone">Phone Number</label>
                <IconField class="w-full">
                    <InputIcon>
                        <i class="pi pi-phone"></i>
                    </InputIcon>
                    <InputText id="lead-phone" v-model="localModel.lead.phone" @blur="validatePhone" :class="{ 'p-invalid': errors.phone }" placeholder="(*************" class="w-full" />
                </IconField>
                <small v-if="errors.phone" class="error-message">{{ errors.phone }}</small>
            </div>

            <!-- Email -->
            <div class="form-field">
                <label for="lead-email">Email Address</label>
                <IconField class="w-full">
                    <InputIcon>
                        <i class="pi pi-envelope"></i>
                    </InputIcon>
                    <InputText id="lead-email" v-model="localModel.lead.email" @blur="validateEmailEl" :class="{ 'p-invalid': errors.email }" placeholder="<EMAIL>" class="w-full" />
                </IconField>
                <small v-if="errors.email" class="error-message">{{ errors.email }}</small>
            </div>

            <!-- Optional Address Field -->
            <div class="form-field">
                <label for="lead-address">Address <a @click="emit('editLocation')" class="optional">(Click to Edit)</a></label>
                <IconField class="w-full">
                    <InputIcon>
                        <i class="pi pi-map-marker"></i>
                    </InputIcon>
                    <InputText disabled id="lead-address" v-model="localModel.location_string" placeholder="Your address" class="w-full" />
                </IconField>
            </div>
        </div>

        <div class="privacy-notice">
            <i class="pi pi-shield"></i>
            <span>Your information is secure and will only be used to contact you about your appointment.</span>
        </div>
    </div>
</template>

<style scoped>
.lead-information-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 0.5rem;
}

.form-header {
    margin-bottom: 0.5rem;
}

.form-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.25rem 0;
}

.form-header p {
    font-size: 0.9rem;
    color: var(--text-color-secondary);
    margin: 0;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.25rem;
}

@media (min-width: 768px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-field label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
}

.optional {
    font-size: 0.8rem;
    font-weight: normal;
    color: var(--text-color-secondary);
}

.error-message {
    color: var(--red-500);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.privacy-notice {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: var(--surface-hover);
    border-radius: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-color-secondary);
}

.privacy-notice i {
    color: var(--primary-color);
    font-size: 1rem;
}

/* Input field styling enhancements */
:deep(.p-inputtext) {
    transition: all 0.3s ease;
}

:deep(.p-inputtext:focus) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px var(--primary-color-lighten);
}

:deep(.p-inputtext:hover:not(.p-invalid)) {
    border-color: var(--primary-color-lighten);
}

:deep(.p-inputicon) {
    color: var(--text-color-secondary);
}

:deep(.p-inputtext:focus + .p-inputicon) {
    color: var(--primary-color);
}

/* Animation for field focus */
:deep(.p-inputtext) {
    position: relative;
    overflow: hidden;
}

:deep(.p-inputtext:focus::after) {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
    animation: slide-in 0.3s ease forwards;
}

@keyframes slide-in {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}
</style>
