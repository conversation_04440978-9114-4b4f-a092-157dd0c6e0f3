<script setup lang="ts">
import { defineProps, defineEmits, ref } from 'vue';

const props = defineProps<{
    modelValue: string[];
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string[]): void;
}>();

const newNote = ref('');

const addNote = () => {
    if (newNote.value.trim()) {
        // Add the new note from the input field
        emit('update:modelValue', [...props.modelValue, newNote.value.trim()]);
        newNote.value = ''; // Clear the input field
    } else {
        // Add an empty note if no text is provided
        emit('update:modelValue', [...props.modelValue, '']);
    }
};

const removeNote = (index: number) => {
    const notes = [...props.modelValue];
    notes.splice(index, 1);
    emit('update:modelValue', notes);
};

const updateNote = (index: number, value: string) => {
    const notes = [...props.modelValue];
    notes[index] = value;
    emit('update:modelValue', notes);
};

const isNoteEmpty = (index: number) => {
    return !props.modelValue[index] || props.modelValue[index].trim() === '';
};
</script>

<template>
    <div class="notes-input-container">
        <div class="notes-header">
            <h4>Notes</h4>
            <p>Add any additional information about the appointment</p>
        </div>

        <!-- Existing Notes -->
        <div class="notes-list" v-if="modelValue.length > 0">
            <div v-for="(note, i) in modelValue" :key="i" class="note-item">
                <div class="note-content">
                    <Textarea :value="note" @input="(e) => updateNote(i, e.target.value)" rows="2" class="w-full" placeholder="Enter note details..." autoResize />
                </div>
                <div class="note-actions">
                    <Button icon="pi pi-trash" @click="removeNote(i)" severity="danger" text rounded aria-label="Delete note" v-tooltip.top="'Delete note'" />
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-if="modelValue.length === 0" class="empty-notes">
            <i class="pi pi-file-edit"></i>
            <p>No notes added yet</p>
        </div>

        <!-- Add New Note Input -->
        <div class="add-note-section">
            <div class="add-note-input">
                <Textarea v-model="newNote" rows="2" placeholder="Type a new note here..." class="w-full" autoResize @keydown.enter.prevent="addNote" />
            </div>
            <div class="add-note-actions">
                <Button icon="pi pi-plus" @click="addNote" label="Add Note" text class="add-note-button" />
            </div>
        </div>

        <!-- Note Count -->
        <div class="note-count" v-if="modelValue.length > 0">
            <span>{{ modelValue.length }} {{ modelValue.length === 1 ? 'note' : 'notes' }}</span>
        </div>
    </div>
</template>

<style scoped>
.notes-input-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--surface-ground);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.notes-header {
    margin-bottom: 0.5rem;
}

.notes-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.25rem 0;
}

.notes-header p {
    font-size: 0.9rem;
    color: var(--text-color-secondary);
    margin: 0;
}

.notes-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.note-item {
    display: flex;
    gap: 0.5rem;
    align-items: flex-start;
    padding: 0.75rem;
    background-color: var(--surface-card);
    border-radius: 0.5rem;
    border-left: 3px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.note-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.note-content {
    flex: 1;
}

.note-actions {
    display: flex;
    align-items: center;
}

.empty-notes {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background-color: var(--surface-card);
    border-radius: 0.5rem;
    color: var(--text-color-secondary);
    text-align: center;
}

.empty-notes i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.add-note-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.add-note-input {
    width: 100%;
}

.add-note-actions {
    display: flex;
    justify-content: flex-end;
}

.add-note-button {
    transition: all 0.2s ease;
}

.add-note-button:hover {
    background-color: var(--primary-color-lighten);
    color: var(--primary-color);
}

.note-count {
    font-size: 0.85rem;
    color: var(--text-color-secondary);
    text-align: right;
    padding-top: 0.5rem;
    border-top: 1px solid var(--surface-border);
    margin-top: 0.5rem;
}

/* Textarea styling */
:deep(.p-textarea) {
    transition: all 0.3s ease;
    width: 100%;
}

:deep(.p-textarea:focus) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px var(--primary-color-lighten);
}

:deep(.p-textarea textarea) {
    resize: none;
    width: 100%;
}

/* Custom scrollbar for notes list */
.notes-list::-webkit-scrollbar {
    width: 6px;
}

.notes-list::-webkit-scrollbar-track {
    background: var(--surface-ground);
    border-radius: 10px;
}

.notes-list::-webkit-scrollbar-thumb {
    background: var(--surface-border);
    border-radius: 10px;
}

.notes-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-color-secondary);
}

/* Animation for adding/removing notes */
.note-item {
    animation: slide-in 0.3s ease;
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
