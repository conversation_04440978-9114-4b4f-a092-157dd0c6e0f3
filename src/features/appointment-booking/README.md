# Appointment Booking - Timezone Configuration

This document describes the timezone configuration system implemented for the appointment booking feature.

## Overview

The timezone configuration system uses a hardcoded London/UK timezone for all appointment scheduling. All date and time displays, calendar operations, and appointment bookings use London time. When a user's local timezone differs from London time, a notice is displayed to inform them of the time difference.

## Architecture

### Configuration Files

- **`config/timezone.ts`** - Contains hardcoded London timezone configuration and utility functions
- **`utils/timezone.ts`** - Timezone utility functions for formatting and conversion using London time

### Components

- **`TimeSlotPicker.vue`** - Updated to use London timezone for time formatting
- **`AppointmentBooking.vue`** - Updated to use London timezone and shows timezone difference notice

### Store Integration

- **`store/useAppointmentStore.ts`** - Uses hardcoded London timezone
- **`services/appointmentService.ts`** - Passes London timezone information to APIs

## Usage

### Basic Usage

```typescript
import {
    formatTimestampToDate,
    formatTimestampToTime,
    formatTimestampToDateTime,
    getTimeOfDay,
    isDateWeekendInLondon,
    getTodayInLondon
} from '@/features/appointment-booking/utils/timezone';

// Format a timestamp with London timezone
const formattedDate = formatTimestampToDate(timestamp);
const formattedTime = formatTimestampToTime(timestamp);
const formattedDateTime = formatTimestampToDateTime(timestamp);

// Get time period in London timezone
const period = getTimeOfDay(timestamp); // 'morning', 'afternoon', 'evening'

// Check if a date is weekend in London timezone
const isWeekend = isDateWeekendInLondon(new Date());

// Get today's date in London timezone
const todayInLondon = getTodayInLondon();
```

### Timezone Difference Detection

```typescript
import {
    isUserTimezoneDifferent,
    getTimezoneDifferenceMessage,
    getUserTimezone
} from '@/features/appointment-booking/config/timezone';

// Check if user's timezone is different from London
const isDifferent = isUserTimezoneDifferent();

// Get a message explaining the time difference
const message = getTimezoneDifferenceMessage();

// Get user's local timezone
const userTz = getUserTimezone();
```

## Configuration

### Hardcoded Timezone

The system uses a hardcoded London/UK timezone configuration:

```typescript
export const APPOINTMENT_TIMEZONE_CONFIG: TimezoneConfig = {
    timezone: 'Europe/London',
    locale: 'en-GB',
    // ... formatting options
};
```

### Timezone Detection

The system automatically detects when a user's local timezone differs from London time and displays an informative notice.

## Features

### Hardcoded London Timezone

All appointment times are consistently displayed in London time (GMT/BST).

### Timezone Difference Notice

When a user's local timezone differs from London time, an informative notice is displayed showing both times.

### Consistent Time Display

All date and time formatting uses London timezone for consistency across the application.

### Automatic Detection

The system automatically detects the user's local timezone to determine if a notice should be shown.

### London Timezone Weekend Detection

The calendar component disables weekends based on London timezone, ensuring consistent weekend blocking regardless of the user's local timezone. The weekend detection uses `Intl.DateTimeFormat` to properly handle timezone conversions.

## API Integration

### Backend Communication

The appointment services pass London timezone information to backend APIs:

```typescript
// Fetch available slots with London timezone
const slots = await fetchAvailableSlots(date, data, 'Europe/London');

// Book appointment with London timezone
await bookAppointment(appointmentData, 'Europe/London');
```

### Calendar Integration

London timezone is used when creating Google Calendar events and processing calendar webhooks.

## Best Practices

1. **Always use the timezone utilities** for date/time formatting instead of native JavaScript Date methods
2. **Use the timezone difference notice** to inform users when their local time differs from London time
3. **Pass London timezone to backend APIs** when making appointment-related requests
4. **Test with different user timezones** to ensure the notice displays correctly

## Troubleshooting

### Common Issues

1. **Incorrect time display**: Check that the timezone utilities are being used correctly
2. **Notice not showing**: Verify that timezone difference detection is working
3. **API errors**: Verify that London timezone is being passed to backend services

### Debug Information

Check timezone difference detection:

```typescript
import { isUserTimezoneDifferent, getUserTimezone } from './config/timezone';

console.log('User timezone different:', isUserTimezoneDifferent());
console.log('User timezone:', getUserTimezone());
console.log('Appointment timezone: Europe/London');

// Test weekend detection
import { isDateWeekendInLondon } from './utils/timezone';
const testDate = new Date();
console.log('Is weekend in London:', isDateWeekendInLondon(testDate));
```
