<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import Message from 'primevue/message';

import LocationPicker from './components/LocationPicker.vue';
import ProductDetailsForm from './components/ProductDetailsForm.vue';
import TimeSlotPicker from './components/TimeSlotPicker.vue';
import NotesInput from './components/NotesInput.vue';
import SuccessErrorPage from './components/SuccessErrorPage.vue';
import { Lead } from '@/entities/lead';
import LeadInformationForm from '@/features/appointment-booking/components/LeadInformationForm.vue';
import { useAppointmentStore } from '@/features/appointment-booking/store/useAppointmentStore';
import { useTenantSettingStore } from '@/entities/settings';
import { useAuthStore } from '@/entities/auth';
import TravelEstimatesMap from '@/features/appointment-booking/components/TravelEstimatesMap.vue';
import { useConfirm } from 'primevue/useconfirm';
import { formatTimestampToDate, formatTimestampToDateTime, isDateWeekendInLondon, getTodayInLondon } from './utils/timezone';
import { isUserTimezoneDifferent, getTimezoneDifferenceMessage, getAppointmentTimezone } from './config/timezone';

const authStore = useAuthStore();

const props = defineProps<{
    leadData?: Lead | null;
    isEmbedded?: boolean | null;
}>();

const emits = defineEmits<{
    closeModal: (formData: any | object) => void;
    saveFormData: (formData: any | object) => void;
}>();

const toast = useToast();
const store = useAppointmentStore();
const settingStore = useTenantSettingStore();
const confirmDialog = useConfirm();

const selectedDate = ref<Date | null>(null);
const isSubmitted = ref(false);
const isLocationValid = ref(false);
const activeStep = ref(0);
const validationOtherErrors = ref(false);

onMounted(async () => {
    await authStore.getCurrentUser();
    await settingStore.getConversationSettings();
});
const handleLocationUpdate = (val: { location_string: string; gps_coordinates: string; google_link: string }) => {
    store.formData.location_string = val.location_string;
    store.formData.gps_coordinates = val.gps_coordinates;
    store.formData.google_link = val.google_link;
    isLocationValid.value = Boolean(val.gps_coordinates.trim());
};

const handleDateSelect = (date: Date) => {
    selectedDate.value = date;

    const timestamp = Math.floor(date.getTime() / 1000);
    store.searchNextAvailableSlot(timestamp);
};

// Weekend detection using London timezone
// This ensures weekends are disabled based on London time, not user's local time
const isWeekend = (date: Date): boolean => {
    return isDateWeekendInLondon(date);
};

// Define today as a Date object normalized to midnight in London timezone
const today = getTodayInLondon();
const maxDate = new Date(today);
maxDate.setMonth(today.getMonth() + 3);

const getDisabledDates = (): Date[] => {
    const futureDates: Date[] = [];

    let currentDate = new Date(today);
    while (currentDate <= maxDate) {
        if (isWeekend(currentDate)) {
            futureDates.push(new Date(currentDate));
        }
        const currentDateFormat = currentDate.toISOString().split('T')[0];
        const isLoggedIn = authStore.isLoggedIn();
        if (settingStore.conversationSetting && !isLoggedIn) {
            // disable the currentDate if its not in the available dates
            if (!settingStore.conversationSetting?.call?.availableAppointmentDates?.includes(currentDateFormat)) {
                futureDates.push(new Date(currentDate));
            }
        }

        if (store.noSlotsFoundOnDates && store.noSlotsFoundOnDates?.includes(currentDateFormat)) {
            futureDates.push(new Date(currentDate));
        }
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return futureDates;
};

const handleSubmit = async () => {
    if (!isLocationValid.value) {
        toast.add({
            severity: 'warn',
            summary: 'Validation',
            detail: 'Please select a valid location with GPS coordinates'
        });
        return;
    }

    if (!store.formData.appointmentDate) {
        toast.add({
            severity: 'warn',
            summary: 'Validation',
            detail: 'Please select a time slot'
        });
        return;
    }

    confirmDialog.require({
        message: 'Are you sure you want to proceed booking an appointment. Please note that  50% deposit required at the time of final measurement survey?',
        header: 'Confirmation',
        icon: 'pi pi-exclamation-triangle',
        rejectProps: {
            label: 'Cancel',
            severity: 'secondary',
            outlined: true
        },
        acceptProps: {
            label: 'Save'
        },
        accept: async () => {
            await store.submitAppointment();
            isSubmitted.value = true;
            activeStep.value = 4; // Move to confirmation step
            if (store.isSuccess) {
                emits('saveFormData', store.formData);
            }
            toast.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted to book the appointment', life: 3000 });
        },
        reject: () => {
            toast.add({ severity: 'info', summary: 'Rejected', detail: 'You have rejected to book the appointment', life: 3000 });
        }
    });
};

const canGoToStep2 = computed(() => isLocationValid.value);
const canGoToStep3 = computed(() => selectedDate.value !== null);
const canGoToStep4 = computed(() => store.availableSlots.length > 0 && store.formData.appointmentDate);

const steps = [
    {
        label: 'Location',
        icon: 'pi pi-map-marker',
        command: () => {
            activeStep.value = 0;
        }
    },
    {
        label: 'Date',
        icon: 'pi pi-calendar',
        command: () => {
            if (canGoToStep2.value) activeStep.value = 1;
            else {
                toast.add({
                    severity: 'warn',
                    summary: 'Validation',
                    detail: 'Please select a valid location first'
                });
            }
        }
    },
    {
        label: 'Time Slot',
        icon: 'pi pi-clock',
        command: () => {
            if (canGoToStep3.value) activeStep.value = 2;
            else {
                toast.add({
                    severity: 'warn',
                    summary: 'Validation',
                    detail: 'Please select a date first'
                });
            }
        }
    },
    {
        label: 'Details',
        icon: 'pi pi-user',
        command: () => {
            if (canGoToStep4.value) activeStep.value = 3;
            else {
                toast.add({
                    severity: 'warn',
                    summary: 'Validation',
                    detail: 'Please select a time slot first'
                });
            }
        }
    },
    {
        label: 'Confirmation',
        icon: 'pi pi-check',
        command: () => {
            /* This step is only accessible after submission */
        }
    }
];

const isFormValid = computed(() => {
    const baseValid = validationOtherErrors.value === false && isLocationValid.value && selectedDate.value !== null && store.availableSlots.length > 0 && store.formData.appointmentDate && store.formData.lead?.name && store.formData.lead?.email;

    return props.isEmbedded ? baseValid : baseValid && store.formData.lead?.phone;
});

const handleTimeSlotSelect = (time: number) => {
    store.formData.appointmentDate = time;
};
const convertTimestampToDate = (timestamp: number, noTime: boolean = false) => {
    if (typeof timestamp !== 'number') {
        timestamp = new Date(timestamp).getTime() / 1000;
    }

    if (noTime) {
        return formatTimestampToDate(timestamp, {
            weekday: 'long',
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    } else {
        return formatTimestampToDateTime(timestamp, {
            weekday: 'long',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
};

// Timezone difference notice
const showTimezoneNotice = computed(() => isUserTimezoneDifferent());
const timezoneNoticeMessage = computed(() => getTimezoneDifferenceMessage());

// Watch for changes in leadData and update store
watch(
    () => props.leadData,
    (newVal) => {
        if (newVal) {
            store.setLead(newVal);
        }
    },
    { immediate: true }
);

const resetBookingForm = () => {
    if (props.isEmbedded && store.isSuccess) {
        emits('closeModal', store.formData);
    }
    isSubmitted.value = false;
    activeStep.value = 0;
};
</script>

<template>
    <div :style="isEmbedded ? 'padding: 0 !important;' : ''" class="appointment-booking-container">
        <div class="appointment-card">
            <div class="appointment-header" v-if="!isEmbedded">
                <h1 class="appointment-title">Book Your Appointment</h1>
                <p class="appointment-subtitle">Complete the steps below to schedule your service</p>
            </div>

            <!-- Timezone Notice -->
            <Message v-if="showTimezoneNotice" severity="info" :closable="false" class="timezone-notice">
                <div class="flex align-items-center gap-2">
                    <i class="pi pi-clock"></i>
                    <span>{{ timezoneNoticeMessage }}</span>
                </div>
            </Message>

            <Stepper :model="steps" :activeStep="activeStep" class="custom-stepper" />

            <div class="step-content">
                <transition-group name="fade" mode="out-in">
                    <!-- Step 1: Location Selection -->
                    <div v-if="activeStep === 0" key="location" class="step-panel">
                        <div class="step-header">
                            <i class="pi pi-map-marker step-icon"></i>
                            <h3 class="step-title">Select Your Location</h3>
                        </div>

                        <LocationPicker
                            :model-value="{
                                location_string: store.formData?.location_string || '',
                                gps_coordinates: store.formData?.gps_coordinates || ''
                            }"
                            @update:model-value="handleLocationUpdate"
                        />
                        <div v-if="isLocationValid" class="location-confirmed"><i class="pi pi-check-circle"></i> Location confirmed</div>
                        <div class="step-actions">
                            <Button label="Continue to Date Selection" icon="pi pi-arrow-right" iconPos="right" @click="activeStep = 1" :disabled="!isLocationValid" class="p-button-raised p-button-rounded" />
                        </div>
                    </div>

                    <!-- Step 2: Date Picker -->
                    <div v-if="activeStep === 1" key="date" class="step-panel">
                        <div class="step-header">
                            <i class="pi pi-calendar step-icon"></i>

                            <h3 class="step-title">Select a Date: {{ convertTimestampToDate(store.searchingDate ?? selectedDate, true) }}</h3>
                        </div>
                        <!-- Calendar with London timezone weekend detection -->
                        <Calendar v-model="selectedDate" :inline="true" :show-week="false" :min-date="today" :maxDate="maxDate" :disabled-dates="getDisabledDates()" @date-select="handleDateSelect" :manual-input="false" class="custom-calendar" />
                        <div v-if="selectedDate" class="location-confirmed"><i class="pi pi-check-circle"></i> Date selected</div>
                        <div class="step-actions">
                            <Button label="Back" icon="pi pi-arrow-left" @click="activeStep = 0" class="p-button-outlined p-button-rounded" />
                            <Button label="Continue to Time Selection" icon="pi pi-arrow-right" iconPos="right" @click="activeStep = 2" :disabled="!selectedDate" class="p-button-raised p-button-rounded" />
                        </div>
                    </div>

                    <!-- Step 3: Time Slots -->
                    <div v-if="activeStep === 2" key="time" class="step-panel">
                        <div class="step-header">
                            <i class="pi pi-clock step-icon"></i>
                            <h3 class="step-title">Select a Time Slot: {{ convertTimestampToDate(store.searchingDate, true) }}</h3>
                        </div>

                        <!-- Slot Search Loader -->
                        <div v-if="store.isLoading" class="loading-container">
                            <h4>Searching for available time slots...</h4>
                            <ProgressSpinner class="custom-spinner" />
                        </div>

                        <div v-if="!store.isLoading && store.availableSlots.length" class="slots-container">
                            <!-- Available Slots -->
                            <TimeSlotPicker @select="handleTimeSlotSelect" :selectedSlot="store.formData.appointmentDate" :slots="store.availableSlots" />

                            <!-- Travel Estimates Map -->
                            <TravelEstimatesMap v-if="authStore.isLoggedIn() && 1 != 1" :travelEstimate="store.travelEstimates" />
                        </div>

                        <!-- No Slots Found -->
                        <div v-if="store.noSlotsFound && !store.isLoading" class="no-slots-container">
                            <Message severity="info" :closable="false" class="custom-message">
                                <div class="no-slots-content flex flex-wrap gap-7 items-center justify-center">
                                    <i class="pi pi-calendar-times no-slots-icon"></i>
                                    <h4 class="no-slots-title">No Available Appointments</h4>
                                    <p class="no-slots-text">We couldn't find any available appointments in the next 7 days from your selected date.</p>
                                    <Button label="Try Another Week" icon="pi pi-calendar-plus" @click="store.loadNextMonth" class="p-button-rounded no-slots-button" />
                                </div>
                            </Message>
                        </div>

                        <div v-if="store.formData.appointmentDate" class="location-confirmed"><i class="pi pi-check-circle"></i> Time slot selected</div>
                        <div class="step-actions">
                            <Button label="Back" icon="pi pi-arrow-left" @click="activeStep = 1" class="p-button-outlined p-button-rounded" />
                            <Button label="Refresh" icon="pi pi-refresh" @click="handleDateSelect(selectedDate)" class="p-button-outlined p-button-rounded" />
                            <Button label="Continue to Details" icon="pi pi-arrow-right" iconPos="right" @click="activeStep = 3" :disabled="!store.formData.appointmentDate" class="p-button-raised p-button-rounded" />
                        </div>
                    </div>

                    <!-- Step 4: Details -->
                    <div v-if="activeStep === 3" key="details" class="step-panel">
                        <div class="step-header">
                            <i class="pi pi-user step-icon"></i>
                            <h3 class="step-title">Enter Your Details</h3>
                        </div>

                        <Accordion :activeIndex="0" class="custom-accordion">
                            <AccordionTab header="Personal Information">
                                <LeadInformationForm v-model="store.formData" @edit-location="activeStep = 0" :no-validation="isEmbedded" />
                            </AccordionTab>

                            <AccordionTab header="Product Details">
                                <ProductDetailsForm v-model="store.formData" />
                            </AccordionTab>

                            <AccordionTab header="Additional Notes">
                                <NotesInput v-model="store.formData.notes" />
                            </AccordionTab>
                        </Accordion>

                        <div class="step-actions">
                            <Button label="Back" icon="pi pi-arrow-left" @click="activeStep = 2" class="p-button-outlined p-button-rounded" />
                            <Button
                                :disabled="!isFormValid"
                                label="Book Appointment"
                                :severity="isFormValid ? 'success' : 'danger'"
                                :icon="isFormValid ? 'pi pi-check' : 'pi pi-times'"
                                @click="handleSubmit"
                                :loading="store.isLoading"
                                class="p-button-raised p-button-rounded p-button-success"
                                v-tooltip.bottom="isFormValid ? 'Book Appointment' : 'Please fill in all required fields'"
                            />
                        </div>
                    </div>

                    <!-- Step 5: Confirmation -->
                    <div v-if="activeStep === 4" key="confirmation" class="step-panel">
                        <SuccessErrorPage v-if="isSubmitted" :success="store.isSuccess" :error="store.error" @reset="resetBookingForm" />
                    </div>
                </transition-group>
            </div>
        </div>
    </div>

    <!-- Add ConfirmDialog component -->
    <ConfirmDialog></ConfirmDialog>
</template>

<style scoped lang="scss">
.appointment-booking-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    min-height: 80vh;
}

.appointment-card {
    background: var(--surface-card);
    border-radius: 1rem;
    width: 70vw;
    max-width: 800px;
    overflow: hidden;
    transition: all 0.3s ease;
}

@media (min-width: 1200px) {
    .appointment-card {
        width: 60vw;
    }
}

.appointment-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-lighten) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    border-radius: 1rem 1rem 0 0;
}

.appointment-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.5px;
}

.appointment-subtitle {
    margin-top: 0.5rem;
    opacity: 0.9;
    font-weight: 300;
}

.custom-stepper {
    margin: 1.5rem 0;
    padding: 0 1rem;
}

.step-content {
    position: relative;
    min-height: 500px; /* Ensure consistent height */
    padding: 0 1.5rem 1.5rem;
}

.step-panel {
    width: 100%;
    transition: all 0.3s ease;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.step-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 0.75rem;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-color);
}

.step-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    gap: 1rem;
}

.location-confirmed {
    display: flex;
    align-items: center;
    color: var(--green-500);
    font-weight: 500;
    margin-top: 1rem;
}

.location-confirmed i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.custom-spinner {
    width: 60px !important;
    height: 60px !important;
    margin-top: 1rem;
}

.slots-container {
    margin: 1rem 0;
}

.no-slots-container {
    margin: 1rem 0;
}

.no-slots-icon {
    font-size: 3rem;
    color: var(--blue-500);
    margin-bottom: 1rem;
}

.custom-message {
    width: 100%;
    text-align: center;
    padding: 1.5rem;
}

.custom-accordion {
    margin-bottom: 1.5rem;
}

.custom-calendar {
    width: 100%;
    margin-bottom: 1rem;

    // Available dates - Using PrimeVue success colors
    :deep(.p-datepicker-day:not(.p-disabled)) {
        background: linear-gradient(135deg, var(--p-green-400) 0%, var(--p-green-500) 100%) !important;
        color: var(--p-green-contrast-color) !important;
        font-weight: 600;
        border: 1px solid var(--p-green-500) !important;
        box-shadow: 0 2px 4px color-mix(in srgb, var(--p-green-500) 20%, transparent);

        &:hover,
        &[data-p='selected'] {
            background: linear-gradient(135deg, var(--p-green-500) 0%, var(--p-green-600) 100%) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px color-mix(in srgb, var(--p-green-500) 30%, transparent);
            transition: all var(--p-datepicker-transition-duration) ease;
        }
    }

    // Disabled dates - Using PrimeVue error colors
    :deep(.p-datepicker-day.p-disabled) {
        background: linear-gradient(135deg, var(--p-red-400) 0%, var(--p-red-500) 100%) !important;
        font-weight: 600;
        border: 1px solid var(--p-red-500) !important;
        opacity: 1 !important; // Override default disabled opacity
        cursor: not-allowed;
        box-shadow: 0 2px 4px color-mix(in srgb, var(--p-red-500) 20%, transparent);
        text-decoration: line-through;

        &:hover {
            background: linear-gradient(135deg, var(--p-red-500) 0%, var(--p-red-600) 100%) !important;
            opacity: 1 !important;
        }
    }
}

/* Fixed Transitions */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
    position: absolute;
    width: 100%;
}

.fade-enter-from {
    opacity: 0;
    transform: translateX(30px);
}

.fade-leave-to {
    opacity: 0;
    transform: translateX(-30px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .appointment-booking-container {
        padding: 1rem;
    }

    .appointment-header {
        padding: 1.5rem;
    }

    .appointment-title {
        font-size: 1.5rem;
    }

    .step-actions {
        flex-direction: column;
    }

    .step-actions .p-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Timezone Notice */
.timezone-notice {
    margin-bottom: 1rem;
    border-left: 4px solid var(--blue-500);
}

.timezone-notice :deep(.p-message-wrapper) {
    padding: 0.75rem 1rem;
}

.timezone-notice :deep(.p-message-icon) {
    color: var(--blue-500);
}
</style>
