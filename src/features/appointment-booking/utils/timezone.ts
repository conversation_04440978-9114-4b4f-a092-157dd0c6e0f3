/**
 * Timezone utility functions for appointment booking
 * Uses hardcoded London/UK timezone
 */

import { TimezoneConfig, getTimezoneConfig } from '../config/timezone';

/**
 * Format timestamp to date string using London timezone
 */
export function formatTimestampToDate(
    timestamp: number,
    options?: Partial<Intl.DateTimeFormatOptions>
): string {
    if (typeof timestamp !== 'number' || timestamp <= 0) return '';

    const config = getTimezoneConfig();
    const formatOptions = { ...config.dateFormat, ...options };

    return new Date(timestamp * 1000).toLocaleString(config.locale, formatOptions);
}

/**
 * Format timestamp to time string using London timezone
 */
export function formatTimestampToTime(
    timestamp: number,
    options?: Partial<Intl.DateTimeFormatOptions>
): string {
    if (typeof timestamp !== 'number' || timestamp <= 0) return '';

    const config = getTimezoneConfig();
    const formatOptions = { ...config.timeFormat, ...options };

    return new Date(timestamp * 1000).toLocaleString(config.locale, formatOptions);
}

/**
 * Format timestamp to date and time string using London timezone
 */
export function formatTimestampToDateTime(
    timestamp: number,
    options?: Partial<Intl.DateTimeFormatOptions>
): string {
    if (typeof timestamp !== 'number' || timestamp <= 0) return '';

    const config = getTimezoneConfig();
    const formatOptions = { ...config.dateTimeFormat, ...options };

    return new Date(timestamp * 1000).toLocaleString(config.locale, formatOptions);
}

/**
 * Get time of day category (morning, afternoon, evening) for a timestamp in London timezone
 */
export function getTimeOfDay(timestamp: number): 'morning' | 'afternoon' | 'evening' {
    if (typeof timestamp !== 'number' || timestamp <= 0) return 'morning';

    const config = getTimezoneConfig();
    const date = new Date(timestamp * 1000);

    // Get the hour in London timezone
    const timeString = date.toLocaleString(config.locale, {
        hour: 'numeric',
        hour12: false,
        timeZone: config.timezone
    });

    const hour = parseInt(timeString, 10);

    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    return 'evening';
}

/**
 * Convert a Date object to timestamp
 */
export function dateToTimestamp(date: Date): number {
    if (!date || !(date instanceof Date)) return 0;

    // Return timestamp in seconds (not milliseconds)
    return Math.floor(date.getTime() / 1000);
}

/**
 * Convert timestamp to Date object
 */
export function timestampToDate(timestamp: number): Date {
    if (typeof timestamp !== 'number' || timestamp <= 0) return new Date();

    return new Date(timestamp * 1000);
}

/**
 * Get current timestamp in seconds
 */
export function getCurrentTimestamp(): number {
    return Math.floor(Date.now() / 1000);
}

/**
 * Check if a timestamp is in the past
 */
export function isTimestampInPast(timestamp: number): boolean {
    return timestamp < getCurrentTimestamp();
}

/**
 * Check if a timestamp is within the next 24 hours
 */
export function isTimestampWithin24Hours(timestamp: number): boolean {
    const now = getCurrentTimestamp();
    const twentyFourHoursFromNow = now + (24 * 60 * 60);
    return timestamp >= now && timestamp <= twentyFourHoursFromNow;
}

/**
 * Format a date for display in calendar components using London timezone
 */
export function formatDateForCalendar(date: Date): string {
    const config = getTimezoneConfig();

    return date.toLocaleDateString(config.locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: config.timezone
    });
}

/**
 * Create London timezone formatter
 */
export function createTimezoneFormatter() {
    const config = getTimezoneConfig();

    return {
        formatDate: (timestamp: number) => formatTimestampToDate(timestamp),
        formatTime: (timestamp: number) => formatTimestampToTime(timestamp),
        formatDateTime: (timestamp: number) => formatTimestampToDateTime(timestamp),
        getTimeOfDay: (timestamp: number) => getTimeOfDay(timestamp),
        config
    };
}

/**
 * Check if a date is a weekend in London timezone
 */
export function isWeekendInLondon(date: Date): boolean {
    if (!date) return false;

    const config = getTimezoneConfig();

    // Use Intl.DateTimeFormat to get the day of week in London timezone
    const formatter = new Intl.DateTimeFormat('en-US', {
        weekday: 'short',
        timeZone: config.timezone
    });

    const dayName = formatter.format(date);

    // Check if it's Saturday or Sunday
    return dayName === 'Sat' || dayName === 'Sun';
}

/**
 * Get today's date in London timezone
 */
export function getTodayInLondon(): Date {
    const config = getTimezoneConfig();
    const now = new Date();

    // Get current date in London timezone
    const londonDateString = now.toLocaleDateString('en-CA', {
        timeZone: config.timezone
    }); // en-CA gives YYYY-MM-DD format

    return new Date(londonDateString + 'T00:00:00');
}

/**
 * Check if a date falls on a weekend in London timezone
 */
export function isDateWeekendInLondon(date: Date): boolean {
    if (!date) return false;

    const config = getTimezoneConfig();

    // Use Intl.DateTimeFormat to get the day of week in London timezone
    const formatter = new Intl.DateTimeFormat('en-US', {
        weekday: 'short',
        timeZone: config.timezone
    });

    const dayName = formatter.format(date);

    // Check if it's Saturday or Sunday
    return dayName === 'Sat' || dayName === 'Sun';
}
