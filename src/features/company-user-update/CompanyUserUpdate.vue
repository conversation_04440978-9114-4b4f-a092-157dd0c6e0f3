<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useUsersStore } from '@/entities/user/store/userStore';
import { useAuthStore } from '@/entities/auth';
import { storeToRefs } from 'pinia';
import { validateEmail, validateRequired, validatePhoneNumber, validateUrl } from '@/utils';
import { JOB_TITLE_OPTIONS, DEPARTMENT_OPTIONS, ADMIN_ROLE_OPTIONS, TIMEZONE_OPTIONS, LANGUAGE_OPTIONS, PERMISSION_OPTIONS, FORM_STEPS, type CompanyUserErrors } from './types';
import { CompanyUserService } from './services/companyUserService';
import type { User } from '@/entities/user';

// Component props
const props = defineProps<{
    userId?: string;
    tenantId?: string;
    showHeader?: boolean;
    showSaveButton?: boolean;
    readonly?: boolean;
    stepperMode?: boolean;
}>();

// Component emits
const emits = defineEmits(['save', 'complete', 'step-change']);

// Stores
const toast = useToast();
const userStore = useUsersStore();
const authStore = useAuthStore();
const { loading } = storeToRefs(userStore);

// Local state
const userInfo = ref<Partial<User>>({});
const isFormValid = ref(false);
const isSaving = ref(false);
const hasUnsavedChanges = ref(false);
const originalData = ref<Partial<User>>({});
const submitted = ref(false);
const currentStep = ref(0);

// Form errors
const errors = ref<CompanyUserErrors>({
    name: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    jobTitle: '',
    department: '',
    role: '',
    timezone: '',
    language: '',
    linkedinUrl: '',
    bio: '',
    alternativeEmail: ''
});

// Computed properties
const currentTenantId = computed(() => {
    return props.tenantId || authStore.user?.tenantId || '';
});

const isReadonly = computed(() => {
    return props.readonly || false;
});

const showHeaderSection = computed(() => {
    return props.showHeader !== false;
});

const showSaveButtons = computed(() => {
    return props.showSaveButton !== false;
});

const isStepperMode = computed(() => {
    return props.stepperMode || false;
});

const currentStepData = computed(() => {
    return FORM_STEPS[currentStep.value] || FORM_STEPS[0];
});

const canProceedToNextStep = computed(() => {
    if (!isStepperMode.value) return true;

    const currentFields = currentStepData.value.fields;
    return currentFields.every((field) => {
        const value = userInfo.value[field as keyof User];
        if (field === 'name' || field === 'email' || field === 'role') {
            return value && String(value).trim().length > 0;
        }
        return true; // Optional fields
    });
});

const isLastStep = computed(() => {
    return currentStep.value >= FORM_STEPS.length - 1;
});

// Validation functions
const validateField = (field: keyof CompanyUserErrors, value: string, validator: (val: string) => string) => {
    const error = validator(value);
    errors.value[field] = error;
    return !error;
};

const validateRequiredField = (field: keyof CompanyUserErrors, value: string) => {
    return validateField(field, value, validateRequired);
};

const validateEmailField = (field: keyof CompanyUserErrors, value: string) => {
    if (!value.trim()) return true; // Allow empty for optional fields
    return validateField(field, value, validateEmail);
};

const validatePhoneField = (field: keyof CompanyUserErrors, value: string) => {
    if (!value.trim()) return true; // Allow empty for optional fields
    return validateField(field, value, validatePhoneNumber);
};

const validateUrlField = (field: keyof CompanyUserErrors, value: string) => {
    if (!value.trim()) return true; // Allow empty for optional fields
    return validateField(field, value, validateUrl);
};

const validateFormFields = () => {
    const validations = [
        // Required fields
        validateRequiredField('email', userInfo.value.email || ''),
        validateEmailField('email', userInfo.value.email || ''),
        validateRequiredField('role', userInfo.value.role || ''),

        // Name validation - either full name or first+last name
        userInfo.value.name?.trim() || (userInfo.value.firstName?.trim() && userInfo.value.lastName?.trim())
            ? true
            : (() => {
                  if (!userInfo.value.firstName?.trim()) errors.value.firstName = 'First name is required';
                  if (!userInfo.value.lastName?.trim()) errors.value.lastName = 'Last name is required';
                  if (!userInfo.value.name?.trim()) errors.value.name = 'Full name is required';
                  return false;
              })(),

        // Optional field validations
        validatePhoneField('phone', userInfo.value.phone || ''),
        validateEmailField('alternativeEmail', userInfo.value.alternativeEmail || ''),
        validateUrlField('linkedinUrl', userInfo.value.linkedinUrl || '')
    ];

    const isValid = validations.every(Boolean);
    isFormValid.value = isValid;
    return isValid;
};

// Data loading
const loadAdminInfo = async () => {
    if (props.userId) {
        try {
            const userData = await userStore.getUser(props.userId);
            if (userData) {
                const convertedData = CompanyUserService.convertFromUserData(userData);
                userInfo.value = convertedData;
                originalData.value = { ...convertedData };
            }
        } catch (error) {
            console.error('Error loading admin info:', error);
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load admin information',
                life: 3000
            });
        }
    } else {
        // Initialize with defaults for new admin
        userInfo.value = {
            tenantId: currentTenantId.value,
            role: 'tenant_admin',
            timezone: 'UTC',
            language: 'en',
            isOwner: true,
            permissions: CompanyUserService.getDefaultPermissions('tenant_admin'),
            onboardingCompleted: false,
            welcomeEmailSent: false,
            twoFactorEnabled: false
        };
        originalData.value = { ...userInfo.value };
    }
};

// Form submission
const saveAdminInfo = async (data?: Partial<User>) => {
    submitted.value = true;
    const dataToSave = data || userInfo.value;

    if (!validateFormFields()) {
        toast.add({
            severity: 'error',
            summary: 'Validation Error',
            detail: 'Please fix the errors in the form',
            life: 3000
        });
        return;
    }

    isSaving.value = true;

    try {
        // Sanitize and validate data
        const sanitizedData = CompanyUserService.sanitizeAdminInfo(dataToSave);
        const validation = await CompanyUserService.validateAdminInfo(sanitizedData);

        if (!validation.isValid) {
            Object.assign(errors.value, validation.errors);
            throw new Error('Validation failed');
        }

        // Convert to user format and save
        const userData = CompanyUserService.convertToUserData(sanitizedData);

        if (props.userId) {
            await userStore.updateUser(props.userId, userData);
        } else {
            await userStore.addUser(userData);
        }

        // Update local state
        originalData.value = { ...sanitizedData };
        hasUnsavedChanges.value = false;
        submitted.value = false;

        // Check if onboarding-numbers is complete
        const isComplete = CompanyUserService.isOnboardingComplete(sanitizedData);
        if (isComplete && !sanitizedData.onboardingCompleted) {
            userInfo.value.onboardingCompleted = true;
            emits('complete', sanitizedData);
        }

        emits('save', sanitizedData);

        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Admin information saved successfully',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving admin info:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save admin information',
            life: 3000
        });
    } finally {
        isSaving.value = false;
    }
};

// Stepper navigation
const nextStep = () => {
    if (canProceedToNextStep.value && currentStep.value < FORM_STEPS.length - 1) {
        currentStep.value++;
        emits('step-change', currentStep.value);
    }
};

const prevStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
        emits('step-change', currentStep.value);
    }
};

const goToStep = (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < FORM_STEPS.length) {
        currentStep.value = stepIndex;
        emits('step-change', currentStep.value);
    }
};

// Form change handling
const handleFormUpdate = () => {
    // Auto-generate full name from first and last name
    if (userInfo.value.firstName && userInfo.value.lastName) {
        userInfo.value.name = `${userInfo.value.firstName} ${userInfo.value.lastName}`.trim();
    }

    // Check if there are unsaved changes
    const hasChanges = JSON.stringify(userInfo.value) !== JSON.stringify(originalData.value);
    hasUnsavedChanges.value = hasChanges;

    // Validate form if submitted
    if (submitted.value) {
        validateFormFields();
    }
};

// Watch for changes in userInfo
watch(userInfo, handleFormUpdate, { deep: true });

// Lifecycle
onMounted(() => {
    loadAdminInfo();
});

// Expose methods for parent components
defineExpose({
    loadAdminInfo,
    saveAdminInfo,
    nextStep,
    prevStep,
    goToStep,
    hasUnsavedChanges: computed(() => hasUnsavedChanges.value),
    isFormValid: computed(() => isFormValid.value),
    currentStep: computed(() => currentStep.value)
});
</script>

<template>
    <div class="company-admin-update">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-8">
            <ProgressSpinner />
        </div>

        <!-- Main Content -->
        <div v-else class="space-y-6">
            <!-- Header Section (optional) -->
            <div v-if="showHeaderSection" class="bg-surface-card rounded-lg p-6 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
                            {{ props.userId ? 'Update Admin Profile' : 'Setup Company Admin' }}
                        </h1>
                        <p class="text-surface-600 dark:text-surface-400 text-lg">
                            {{ props.userId ? 'Manage admin user details and permissions' : 'Configure the primary administrator for your company' }}
                        </p>
                    </div>
                    <div v-if="hasUnsavedChanges" class="flex items-center gap-2 text-orange-600 dark:text-orange-400">
                        <i class="pi pi-exclamation-triangle"></i>
                        <span class="text-sm font-medium">Unsaved changes</span>
                    </div>
                </div>
            </div>

            <!-- Stepper (for onboarding-numbers mode) -->
            <div v-if="isStepperMode" class="bg-surface-card rounded-lg shadow-sm p-6">
                <Steps :model="FORM_STEPS" :activeStep="currentStep" class="mb-6">
                    <template #item="{ item, index, active }">
                        <div class="flex items-center gap-3 cursor-pointer p-3 rounded-lg transition-colors" :class="{ 'bg-primary-50 dark:bg-primary-900/20': active }" @click="goToStep(index)">
                            <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors" :class="active ? 'border-primary-500 bg-primary-500 text-white' : 'border-surface-300 dark:border-surface-600'">
                                <i :class="item.icon" class="text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-semibold text-surface-900 dark:text-surface-0">{{ item.title }}</div>
                                <div class="text-sm text-surface-600 dark:text-surface-400">{{ item.description }}</div>
                            </div>
                        </div>
                    </template>
                </Steps>
            </div>

            <!-- Admin Info Form -->
            <div class="bg-surface-card rounded-lg shadow-sm">
                <div class="company-admin-form">
                    <!-- Header -->
                    <div v-if="!isStepperMode" class="flex justify-between items-center mb-6 p-6 pb-0">
                        <div>
                            <h2 class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">Admin Information</h2>
                            <p class="text-surface-600 dark:text-surface-400">Configure admin user details and access permissions</p>
                        </div>
                    </div>

                    <!-- Form Content -->
                    <div class="p-6">
                        <!-- Step 1: Personal Information -->
                        <div v-if="!isStepperMode || currentStep === 0" class="space-y-6">
                            <div v-if="isStepperMode" class="mb-6">
                                <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Personal Information</h3>
                                <p class="text-surface-600 dark:text-surface-400">Enter basic personal details for the admin user</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- First Name -->
                                <div class="field">
                                    <label for="firstName" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> First Name <span class="text-red-500">*</span> </label>
                                    <IconField>
                                        <InputIcon class="pi pi-user" />
                                        <InputText id="firstName" v-model="userInfo.firstName" :readonly="isReadonly" placeholder="Enter first name..." :class="{ 'p-invalid': errors.firstName && submitted }" @input="handleFormUpdate" fluid />
                                    </IconField>
                                    <small class="p-error" v-if="errors.firstName && submitted">{{ errors.firstName }}</small>
                                </div>

                                <!-- Last Name -->
                                <div class="field">
                                    <label for="lastName" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Last Name <span class="text-red-500">*</span> </label>
                                    <IconField>
                                        <InputIcon class="pi pi-user" />
                                        <InputText id="lastName" v-model="userInfo.lastName" :readonly="isReadonly" placeholder="Enter last name..." :class="{ 'p-invalid': errors.lastName && submitted }" @input="handleFormUpdate" fluid />
                                    </IconField>
                                    <small class="p-error" v-if="errors.lastName && submitted">{{ errors.lastName }}</small>
                                </div>
                            </div>

                            <!-- Full Name (auto-generated or manual) -->
                            <div class="field">
                                <label for="name" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Full Name <span class="text-red-500">*</span> </label>
                                <IconField>
                                    <InputIcon class="pi pi-user" />
                                    <InputText
                                        id="name"
                                        v-model="userInfo.name"
                                        :readonly="isReadonly"
                                        placeholder="Full name (auto-generated from first + last name)"
                                        :class="{ 'p-invalid': errors.name && submitted }"
                                        @input="handleFormUpdate"
                                        fluid
                                    />
                                </IconField>
                                <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
                            </div>

                            <!-- Email -->
                            <div class="field">
                                <label for="email" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Email Address <span class="text-red-500">*</span> </label>
                                <IconField>
                                    <InputIcon class="pi pi-envelope" />
                                    <InputText id="email" v-model="userInfo.email" :readonly="isReadonly" placeholder="Enter email address..." :class="{ 'p-invalid': errors.email && submitted }" @input="handleFormUpdate" fluid />
                                </IconField>
                                <small class="p-error" v-if="errors.email && submitted">{{ errors.email }}</small>
                            </div>
                        </div>

                        <!-- Step 2: Professional Details -->
                        <div v-if="!isStepperMode || currentStep === 1" class="space-y-6">
                            <div v-if="isStepperMode" class="mb-6">
                                <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Professional Details</h3>
                                <p class="text-surface-600 dark:text-surface-400">Configure job title, department, and role</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Job Title -->
                                <div class="field">
                                    <label for="jobTitle" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Job Title </label>
                                    <IconField>
                                        <InputIcon class="pi pi-briefcase" />
                                        <Dropdown
                                            id="jobTitle"
                                            v-model="userInfo.jobTitle"
                                            :options="JOB_TITLE_OPTIONS"
                                            optionLabel="label"
                                            optionValue="value"
                                            :readonly="isReadonly"
                                            placeholder="Select job title..."
                                            :class="{ 'p-invalid': errors.jobTitle && submitted }"
                                            @change="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.jobTitle && submitted">{{ errors.jobTitle }}</small>
                                </div>

                                <!-- Department -->
                                <div class="field">
                                    <label for="department" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Department </label>
                                    <IconField>
                                        <InputIcon class="pi pi-building" />
                                        <Dropdown
                                            id="department"
                                            v-model="userInfo.department"
                                            :options="DEPARTMENT_OPTIONS"
                                            optionLabel="label"
                                            optionValue="value"
                                            :readonly="isReadonly"
                                            placeholder="Select department..."
                                            :class="{ 'p-invalid': errors.department && submitted }"
                                            @change="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.department && submitted">{{ errors.department }}</small>
                                </div>
                            </div>

                            <!-- Role -->
                            <div class="field">
                                <label for="role" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Admin Role <span class="text-red-500">*</span> </label>
                                <IconField>
                                    <InputIcon class="pi pi-shield" />
                                    <Dropdown
                                        id="role"
                                        v-model="userInfo.role"
                                        :options="ADMIN_ROLE_OPTIONS"
                                        optionLabel="label"
                                        optionValue="value"
                                        :readonly="isReadonly"
                                        placeholder="Select admin role..."
                                        :class="{ 'p-invalid': errors.role && submitted }"
                                        @change="handleFormUpdate"
                                        fluid
                                    />
                                </IconField>
                                <small class="p-error" v-if="errors.role && submitted">{{ errors.role }}</small>
                            </div>
                        </div>

                        <!-- Step 3: Contact Information -->
                        <div v-if="!isStepperMode || currentStep === 2" class="space-y-6">
                            <div v-if="isStepperMode" class="mb-6">
                                <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Contact Information</h3>
                                <p class="text-surface-600 dark:text-surface-400">Add phone and additional contact details</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Phone -->
                                <div class="field">
                                    <label for="phone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Phone Number </label>
                                    <IconField>
                                        <InputIcon class="pi pi-phone" />
                                        <InputText id="phone" v-model="userInfo.phone" :readonly="isReadonly" placeholder="Enter phone number..." :class="{ 'p-invalid': errors.phone && submitted }" @input="handleFormUpdate" fluid />
                                    </IconField>
                                    <small class="p-error" v-if="errors.phone && submitted">{{ errors.phone }}</small>
                                </div>

                                <!-- Alternative Email -->
                                <div class="field">
                                    <label for="alternativeEmail" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Alternative Email </label>
                                    <IconField>
                                        <InputIcon class="pi pi-envelope" />
                                        <InputText
                                            id="alternativeEmail"
                                            v-model="userInfo.alternativeEmail"
                                            :readonly="isReadonly"
                                            placeholder="Enter alternative email..."
                                            :class="{ 'p-invalid': errors.alternativeEmail && submitted }"
                                            @input="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.alternativeEmail && submitted">{{ errors.alternativeEmail }}</small>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Timezone -->
                                <div class="field">
                                    <label for="timezone" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Timezone </label>
                                    <IconField>
                                        <InputIcon class="pi pi-clock" />
                                        <Dropdown
                                            id="timezone"
                                            v-model="userInfo.timezone"
                                            :options="TIMEZONE_OPTIONS"
                                            optionLabel="label"
                                            optionValue="value"
                                            :readonly="isReadonly"
                                            placeholder="Select timezone..."
                                            :class="{ 'p-invalid': errors.timezone && submitted }"
                                            @change="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.timezone && submitted">{{ errors.timezone }}</small>
                                </div>

                                <!-- Language -->
                                <div class="field">
                                    <label for="language" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Preferred Language </label>
                                    <IconField>
                                        <InputIcon class="pi pi-globe" />
                                        <Dropdown
                                            id="language"
                                            v-model="userInfo.language"
                                            :options="LANGUAGE_OPTIONS"
                                            optionLabel="label"
                                            optionValue="value"
                                            :readonly="isReadonly"
                                            placeholder="Select language..."
                                            :class="{ 'p-invalid': errors.language && submitted }"
                                            @change="handleFormUpdate"
                                            fluid
                                        />
                                    </IconField>
                                    <small class="p-error" v-if="errors.language && submitted">{{ errors.language }}</small>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Preferences & Access -->
                        <div v-if="!isStepperMode || currentStep === 3" class="space-y-6">
                            <div v-if="isStepperMode" class="mb-6">
                                <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Preferences & Access</h3>
                                <p class="text-surface-600 dark:text-surface-400">Configure system preferences and permissions</p>
                            </div>

                            <!-- LinkedIn URL -->
                            <div class="field">
                                <label for="linkedinUrl" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> LinkedIn Profile </label>
                                <IconField>
                                    <InputIcon class="pi pi-linkedin" />
                                    <InputText
                                        id="linkedinUrl"
                                        v-model="userInfo.linkedinUrl"
                                        :readonly="isReadonly"
                                        placeholder="https://linkedin.com/in/username"
                                        :class="{ 'p-invalid': errors.linkedinUrl && submitted }"
                                        @input="handleFormUpdate"
                                        fluid
                                    />
                                </IconField>
                                <small class="p-error" v-if="errors.linkedinUrl && submitted">{{ errors.linkedinUrl }}</small>
                            </div>

                            <!-- Bio -->
                            <div class="field">
                                <label for="bio" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Bio / Description </label>
                                <Textarea id="bio" v-model="userInfo.bio" :readonly="isReadonly" placeholder="Brief description about yourself..." rows="4" :class="{ 'p-invalid': errors.bio && submitted }" @input="handleFormUpdate" fluid />
                                <small class="p-error" v-if="errors.bio && submitted">{{ errors.bio }}</small>
                            </div>

                            <!-- Permissions -->
                            <div class="field">
                                <label for="permissions" class="block font-semibold mb-2 text-surface-900 dark:text-surface-0"> Permissions </label>
                                <MultiSelect
                                    id="permissions"
                                    v-model="userInfo.permissions"
                                    :options="PERMISSION_OPTIONS"
                                    optionLabel="label"
                                    optionValue="value"
                                    :readonly="isReadonly"
                                    placeholder="Select permissions..."
                                    @change="handleFormUpdate"
                                    fluid
                                />
                                <small class="text-surface-600 dark:text-surface-400 text-sm mt-1"> Select the permissions this admin user should have </small>
                            </div>

                            <!-- Security Options -->
                            <div class="field">
                                <div class="flex items-center gap-3">
                                    <Checkbox id="twoFactorEnabled" v-model="userInfo.twoFactorEnabled" :readonly="isReadonly" @change="handleFormUpdate" binary />
                                    <label for="twoFactorEnabled" class="font-semibold text-surface-900 dark:text-surface-0"> Enable Two-Factor Authentication </label>
                                </div>
                                <small class="text-surface-600 dark:text-surface-400 text-sm mt-1 block"> Recommended for enhanced security </small>
                            </div>
                        </div>

                        <!-- Stepper Navigation -->
                        <div v-if="isStepperMode" class="flex justify-between items-center mt-8 pt-6 border-t border-surface-200 dark:border-surface-700">
                            <Button label="Previous" icon="pi pi-chevron-left" :disabled="currentStep === 0" @click="prevStep" outlined />

                            <div class="flex items-center gap-2">
                                <span class="text-sm text-surface-600 dark:text-surface-400"> Step {{ currentStep + 1 }} of {{ FORM_STEPS.length }} </span>
                            </div>

                            <Button v-if="!isLastStep" label="Next" icon="pi pi-chevron-right" iconPos="right" :disabled="!canProceedToNextStep" @click="nextStep" />

                            <Button v-else label="Complete Setup" icon="pi pi-check" iconPos="right" :loading="isSaving" :disabled="!isFormValid" @click="saveAdminInfo" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons (non-stepper mode) -->
            <div v-if="showSaveButtons && !isStepperMode" class="flex justify-end gap-3">
                <Button label="Reset" icon="pi pi-refresh" :disabled="!hasUnsavedChanges || isSaving" @click="loadAdminInfo" outlined />
                <Button label="Save Changes" icon="pi pi-save" :loading="isSaving" @click="saveAdminInfo" />
            </div>
        </div>
    </div>
</template>

<style scoped>
.p-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
}

.company-admin-form :deep(.p-dropdown),
.company-admin-form :deep(.p-multiselect) {
    width: 100%;
}

.company-admin-form :deep(.p-steps .p-steps-item) {
    flex: 1;
}

.company-admin-form :deep(.p-steps .p-steps-item .p-steps-title) {
    font-weight: 600;
}

.company-admin-update :deep(.p-progressspinner) {
    width: 50px;
    height: 50px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid.grid-cols-1.md\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }

    .company-admin-form {
        padding: 1rem;
    }
}
</style>
