import type { User } from '@/entities/user';
import { validateEmail, validateRequired, validatePhone<PERSON><PERSON><PERSON>, validateUrl } from '@/utils';

/**
 * Service for handling company admin user operations
 */
export class CompanyUserService {
    /**
     * Convert User to User format for API updates
     * @param userInfo - The admin information to convert
     * @returns Partial<User> - Converted user data
     */
    static convertToUserData(userInfo: Partial<User>): Partial<User> {
        // Build full name from firstName and lastName if provided
        let fullName = userInfo.name || '';
        if (userInfo.firstName && userInfo.lastName) {
            fullName = `${userInfo.firstName} ${userInfo.lastName}`.trim();
        } else if (userInfo.firstName) {
            fullName = userInfo.firstName;
        } else if (userInfo.lastName) {
            fullName = userInfo.lastName;
        }

        // Convert User to User format for API
        const userData: Partial<User> = {
            name: fullName,
            email: userInfo.email,
            role: userInfo.role || 'tenant_admin',
            tenantId: userInfo.tenantId,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // Remove undefined values
        Object.keys(userData).forEach((key) => {
            if (userData[key as keyof typeof userData] === undefined) {
                delete userData[key as keyof typeof userData];
            }
        });

        return userData;
    }

    /**
     * Convert User data to User format for form display
     * @param userData - The user data to convert
     * @returns User - Converted admin info
     */
    static convertFromUserData(userData: User): User {
        // Try to split name into firstName and lastName
        const nameParts = userData.name?.split(' ') || [];
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        return {
            name: userData.name,
            firstName,
            lastName,
            email: userData.email,
            role: userData.role,
            tenantId: userData.tenantId,
            createdAt: userData.createdAt,

            // Set defaults for new fields
            jobTitle: '',
            department: '',
            phone: '',
            alternativeEmail: '',
            linkedinUrl: '',
            bio: '',
            timezone: 'UTC',
            language: 'en',
            twoFactorEnabled: false,
            isOwner: true,
            permissions: ['user_management', 'company_settings'],
            onboardingCompleted: false,
            welcomeEmailSent: false
        };
    }

    /**
     * Validate company admin information before saving
     * @param userInfo - The admin information to validate
     * @returns Promise<{ isValid: boolean; errors: Record<string, string> }>
     */
    static async validateAdminInfo(userInfo: Partial<User>): Promise<{
        isValid: boolean;
        errors: Record<string, string>;
    }> {
        const errors: Record<string, string> = {};

        // Required field validations
        if (!userInfo.name?.trim() && (!userInfo.firstName?.trim() || !userInfo.lastName?.trim())) {
            if (!userInfo.firstName?.trim()) {
                errors.firstName = 'First name is required';
            }
            if (!userInfo.lastName?.trim()) {
                errors.lastName = 'Last name is required';
            }
            if (!userInfo.name?.trim()) {
                errors.name = 'Full name is required';
            }
        }

        if (!userInfo.email?.trim()) {
            errors.email = 'Email is required';
        } else {
            // Email format validation
            const emailError = validateEmail(userInfo.email);
            if (emailError) {
                errors.email = emailError;
            }
        }

        if (!userInfo.role?.trim()) {
            errors.role = 'Role is required';
        }

        // Optional field validations
        if (userInfo.phone && userInfo.phone.trim()) {
            const phoneError = validatePhoneNumber(userInfo.phone);
            if (phoneError) {
                errors.phone = phoneError;
            }
        }

        if (userInfo.alternativeEmail && userInfo.alternativeEmail.trim()) {
            const altEmailError = validateEmail(userInfo.alternativeEmail);
            if (altEmailError) {
                errors.alternativeEmail = altEmailError;
            }
        }

        if (userInfo.linkedinUrl && userInfo.linkedinUrl.trim()) {
            const urlError = validateUrl(userInfo.linkedinUrl);
            if (urlError) {
                errors.linkedinUrl = urlError;
            }
        }

        // Job title validation
        if (userInfo.jobTitle && userInfo.jobTitle.trim().length > 100) {
            errors.jobTitle = 'Job title must be less than 100 characters';
        }

        // Bio validation
        if (userInfo.bio && userInfo.bio.trim().length > 500) {
            errors.bio = 'Bio must be less than 500 characters';
        }

        const isValid = Object.keys(errors).length === 0;
        return { isValid, errors };
    }

    /**
     * Generate default permissions based on role
     * @param role - The user role
     * @returns string[] - Array of permission strings
     */
    static getDefaultPermissions(role: string): string[] {
        const permissionMap: Record<string, string[]> = {
            super_admin: ['user_management', 'company_settings', 'billing', 'analytics', 'api_access', 'integrations', 'security', 'audit_logs', 'support', 'data_export'],
            company_admin: ['user_management', 'company_settings', 'billing', 'analytics', 'integrations', 'security', 'data_export'],
            tenant_admin: ['user_management', 'company_settings', 'analytics', 'integrations'],
            manager: ['user_management', 'analytics'],
            team_lead: ['analytics']
        };

        return permissionMap[role] || ['analytics'];
    }

    /**
     * Check if the admin info represents a complete onboarding-numbers
     * @param userInfo - The admin information to check
     * @returns boolean - Whether onboarding-numbers is complete
     */
    static isOnboardingComplete(userInfo: Partial<User>): boolean {
        const requiredFields = ['name', 'email', 'role', 'jobTitle', 'department'];

        return requiredFields.every((field) => {
            const value = userInfo[field as keyof User];
            return value && String(value).trim().length > 0;
        });
    }

    /**
     * Generate a welcome message for the admin user
     * @param userInfo - The admin information
     * @returns string - Welcome message
     */
    static generateWelcomeMessage(userInfo: User): string {
        const name = userInfo.firstName || userInfo.name || 'Admin';
        const jobTitle = userInfo.jobTitle ? ` as ${userInfo.jobTitle}` : '';

        return `Welcome to the platform, ${name}! We're excited to have you${jobTitle} managing your company's account.`;
    }

    /**
     * Sanitize admin info before saving
     * @param userInfo - The admin information to sanitize
     * @returns User - Sanitized admin info
     */
    static sanitizeAdminInfo(userInfo: Partial<User>): Partial<User> {
        const sanitized = { ...userInfo };

        // Trim string fields
        const stringFields = ['name', 'firstName', 'lastName', 'email', 'phone', 'alternativeEmail', 'jobTitle', 'department', 'linkedinUrl', 'bio', 'timezone', 'language'];

        stringFields.forEach((field) => {
            if (sanitized[field as keyof User] && typeof sanitized[field as keyof User] === 'string') {
                (sanitized as any)[field] = (sanitized[field as keyof User] as string).trim();
            }
        });

        // Ensure email is lowercase
        if (sanitized.email) {
            sanitized.email = sanitized.email.toLowerCase();
        }
        if (sanitized.alternativeEmail) {
            sanitized.alternativeEmail = sanitized.alternativeEmail.toLowerCase();
        }

        // Set default values
        if (!sanitized.timezone) {
            sanitized.timezone = 'UTC';
        }
        if (!sanitized.language) {
            sanitized.language = 'en';
        }
        if (!sanitized.role) {
            sanitized.role = 'tenant_admin';
        }

        // Generate permissions if not set
        if (!sanitized.permissions && sanitized.role) {
            sanitized.permissions = this.getDefaultPermissions(sanitized.role);
        }

        return sanitized;
    }
}
