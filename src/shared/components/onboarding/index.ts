// Shared Onboarding Components
export { default as OnboardingHeader } from './OnboardingHeader.vue';
export { default as OnboardingSteps } from './OnboardingSteps.vue';
export { default as OnboardingCard } from './OnboardingCard.vue';
export { default as OnboardingLayout } from './OnboardingLayout.vue';
export { default as FeatureIcon } from './FeatureIcon.vue';

// Types
export interface OnboardingStep {
    id?: string | number;
    title: string;
    description?: string;
    icon?: string;
    completed?: boolean;
    disabled?: boolean;
}

export interface FeatureConfig {
    title: string;
    description?: string;
    icon: string;
    color?: string;
}

// Common feature configurations
export const FEATURE_CONFIGS: Record<string, FeatureConfig> = {
    calendar: {
        title: 'Google Calendar Integration',
        description: 'Connect your Google Calendar to enable seamless appointment booking and management for your business.',
        icon: 'pi pi-google'
    },
    email: {
        title: 'Email Integration Setup',
        description: 'Set up email forwarding for your chatbot to handle email inquiries automatically.',
        icon: 'pi pi-envelope'
    },
    numbers: {
        title: 'Communication Services',
        description: 'Configure SMS and call integration using Twilio and Bland.AI services.',
        icon: 'pi pi-phone'
    },
    prompts: {
        title: 'AI Prompt Configuration',
        description: 'Configure how your AI assistants interact with customers and analyze leads.',
        icon: 'pi pi-sparkles'
    },
    scripts: {
        title: 'AI Scripts Setup',
        description: 'Create personalized scripts for your AI to use when communicating with leads.',
        icon: 'pi pi-file-edit'
    },
    leadConfig: {
        title: 'Lead Configuration',
        description: 'Manage lead actions, sources, and statuses with customizable properties.',
        icon: 'pi pi-cog'
    },
    companyInfo: {
        title: 'Company Information',
        description: 'Set up your company profile and business details.',
        icon: 'pi pi-building'
    },
    userInfo: {
        title: 'User Profile',
        description: 'Configure your personal information and preferences.',
        icon: 'pi pi-user'
    }
};

// Common step configurations
export const COMMON_STEPS = {
    welcome: {
        title: 'Welcome',
        description: 'Get started',
        icon: 'pi pi-flag'
    },
    setup: {
        title: 'Setup',
        description: 'Configure settings',
        icon: 'pi pi-cog'
    },
    validation: {
        title: 'Validation',
        description: 'Verify setup',
        icon: 'pi pi-shield'
    },
    completion: {
        title: 'Complete',
        description: 'All done',
        icon: 'pi pi-check'
    }
};
