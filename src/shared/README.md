# Shared

> https://feature-sliced.design/docs/reference/layers#shared

Isolated modules, components and abstractions that are detached from the specifics of the project or business. Warning: not to be treated like a utility dump!

This layer, unlike others, does not consist of slices, and instead consists of segments directly.

Content examples:

- UI kit
- API client
- Code working with browser APIs
