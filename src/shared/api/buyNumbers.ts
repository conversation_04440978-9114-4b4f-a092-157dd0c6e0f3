import { requestOnBuyBlandNumberFirebase, requestOnBuyTwilioNumberFirebase, requestOnSearchTwilioNumbersFirebase } from '@/shared';

export async function searchTwilioNumbers(params: any) {
    const { data } = await requestOnSearchTwilioNumbersFirebase(params);
    console.log('===3213====');
    console.log(data);
    return data;
}

export async function buyTwilioNumber(params: any) {
    params.countryCode = params.countryCode || 'GB';
    params.statusCallback = 'https://us-central1-wyspre-ai.cloudfunctions.net/chatBots/process-call';
    const { data } = await requestOnBuyTwilioNumberFirebase(params);
    console.log('===3213====');
    console.log(data);
    return data;
}

export async function buyBlandNumber(params: any) {
    const { data } = await requestOnBuyBlandNumberFirebase(params);
    console.log('===3213====');
    console.log(data);
    return data;
}
