import { AppointmentParamsFetchAvailableSlots, requestAddAppointmentToCalendarFirebase, requestCreateCalendarFirebase, requestFetchAvailableSlotsFirebase } from '@/shared/api/firebase-functions';
import { Appointment } from '@/entities/appointments';
import { getUserLoggedInData } from '@/shared';

export async function requestFetchAvailableSlots(params: AppointmentParamsFetchAvailableSlots) {
    const user = await getUserLoggedInData();
    params.tenantId = user?.tenantId ?? 'Lw225tYPYTssrWaZ4ipb';
    const { data } = await requestFetchAvailableSlotsFirebase(params);
    console.log('===3213====');
    console.log(data);
    return data;
}

export async function requestAddAppointmentToCalendar(params: Appointment) {
    const user = await getUserLoggedInData();
    params.tenantId = user?.tenantId ?? 'Lw225tYPYTssrWaZ4ipb';
    if (params.lead) params.lead.tenantId = params.tenantId;
    console.log('====');
    console.log(params);

    const { data } = await requestAddAppointmentToCalendarFirebase(params);
    return data;
}

export async function requestCreateGoogleCalendar(calendarName: string, timezone?: string) {
    const user = await getUserLoggedInData();
    const { data } = await requestCreateCalendarFirebase({ calendarName, tenantId: user?.tenantId ?? 'Lw225tYPYTssrWaZ4ipb', timezone });
    return data;
}
