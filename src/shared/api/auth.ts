import { User as UserApp } from '@/entities/user';
import { User } from 'firebase/auth';
import { requestDeleteUserFirebase, requestDeleteUsersFirebase, requestSaveUserFirebase, UserDeleteParam, UserDeleteParams } from '@/shared';

export async function requestSaveUser(params: UserApp) {
    const { data } = await requestSaveUserFirebase(params);
    return data as User;
}
export async function requestDeleteUser(params: UserDeleteParam) {
    const { data } = await requestDeleteUserFirebase(params);
    return data as boolean;
}
export async function requestDeleteUsers(params: UserDeleteParams) {
    const { data } = await requestDeleteUsersFirebase(params);
    return data as boolean;
}
