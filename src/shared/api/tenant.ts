import { requestCreateTenantFirebase, requestOnValidateFeaturesFirebase, ValidateFeaturesParams } from '@/shared/api/firebase-functions';

export async function requestCreateTenant(params: any) {
    const { data } = await requestCreateTenantFirebase(params);
    console.log('===3213====');
    console.log(data);
    return data;
}
export async function validateFeatures(params: ValidateFeaturesParams) {
    const { data } = await requestOnValidateFeaturesFirebase(params);
    console.log('===3213====');
    console.log(data);
    return data;
}
