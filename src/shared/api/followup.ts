import { FollowUpParams, requestGetFollowUpMessageFirebase, requestSendFollowUpMessageByIdsFirebase, requestSendFollowUpMessageFirebase } from '@/shared/api/firebase-functions';
import { getUserLoggedInData } from '@/shared';

export async function requestGetFollowUpMessage(params: FollowUpParams) {
    const user = await getUserLoggedInData();
    params.tenantId = user?.tenantId ?? '';
    const { data } = await requestGetFollowUpMessageFirebase(params);
    return data;
}

export async function requestSendFollowUpMessage(params: FollowUpParams) {
    const user = await getUserLoggedInData();
    params.tenantId = user?.tenantId ?? '';
    const { data } = await requestSendFollowUpMessageFirebase(params);
    return data;
}

export async function requestSendFollowUpMessageByIds(params: FollowUpParams) {
    const user = await getUserLoggedInData();
    params.tenantId = user?.tenantId ?? '';
    const { data } = await requestSendFollowUpMessageByIdsFirebase(params);
    return data;
}
