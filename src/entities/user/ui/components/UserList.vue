<script setup lang="ts">
import { FilterMatchMode } from '@primevue/core/api';
import { useToast } from 'primevue/usetoast';
import { computed, onMounted, Ref, ref, watch } from 'vue';
import { User, UserFilters, useUsersStore } from '@/entities/user';
import { validateEmail, validateRequired } from '@/utils';
import { dummyUsers, useAuthStore } from '@/entities/auth';

const userStore = useUsersStore();
const authStore = useAuthStore();
const users = ref<User[]>([]);
const totalRecords = ref<number>(0);

onMounted(async () => {
    await loadLazyData();
});

const toast = useToast();
const reset = ref<number>(0);
const dt = ref<any>();
const userDialog = ref<boolean>(false);
const deleteUserDialog = ref<boolean>(false);
const deleteUsersDialog = ref<boolean>(false);
const user = ref<User>({ email: '', password: '', name: '', role: '' });
const userErrors = ref<{ email: string; password: string; name: string; role: string }>({
    email: '',
    password: '',
    name: '',
    role: ''
});
const selectedUsers = ref<User[] | null>(null);
const filters = ref({
    lastFilter: {},
    rows: 10,
    paging: { direction: '' }, // 'next', 'prev', 'first', 'last', or null
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.IN }
});

/**
 * Handles filter operations.
 */
const onFilter = async (): Promise<void> => {
    await loadLazyData();
};

const nextPageCallback = async (): Promise<void> => {
    filters.value.paging.direction = 'next';
    await loadLazyData();
};
const prevPageCallback = async (): Promise<void> => {
    filters.value.paging.direction = 'prev';
    await loadLazyData();
};
/**
 * Loads data based on current filters.
 * @param event Optional event parameter for pagination and sorting.
 */
const loadLazyData = async (event?: any): Promise<void> => {
    const { global, status } = filters.value;

    const searchFilters: UserFilters = {
        name: global?.value ?? '',
        email: global?.value ?? '',
        role: status?.value ?? [],
        sort: { col: 'id', order: 'asc' },
        page: { pageSize: event?.rows || 10, page: event?.page + 1 || 1 }
    };

    if (event && event.sortField) {
        searchFilters.sort = { col: event.sortField, order: event.sortOrder === 1 ? 'asc' : 'desc' };
    } else {
        searchFilters.sort = { col: 'id', order: 'desc' };
    }

    await userStore.fetchUsersFromServer(searchFilters);
    users.value = userStore.users;

    totalRecords.value = userStore.totalUsers;
    if (event !== undefined) filters.value.rows = event.rows;
    if (event !== undefined) filters.value.lastFilter = event;
};

const submitted = ref<boolean>(false);
const statuses = ref([
    { label: 'ADMIN', value: 'admin' }
    // { label: 'USER', value: 'user' },
    // { label: 'AGENT', value: 'agent' }
]);

/**
 * Opens the dialog to create a new user.
 */
const openNew = (): void => {
    user.value = {};
    submitted.value = false;
    userDialog.value = true;
};

/**
 * Hides the user dialog.
 */
const hideDialog = (): void => {
    userDialog.value = false;
    submitted.value = false;
};

const invalidSignUp = computed(() => {
    return validateRequired(user.value.role?.value || user.value.role).length > 0 || validateRequired(user.value.password).length > 0 || validateRequired(user.value.email).length > 0 || validateEmail(user.value.email).length > 0;
});

/**
 * Validates the email field and sets error message.
 */
const validateEmailField = (): void => {
    if (validateRequired(user.value.email) || validateEmail(user.value.email)) {
        userErrors.value.email = validateRequired(user.value.email) || validateEmail(user.value.email);
    } else {
        userErrors.value.email = '';
    }
};

/**
 * Validates the password field and sets error message.
 */
const validatePasswordField = (): void => {
    if (validateRequired(user.value.password) && !user.value.id) {
        userErrors.value.password = validateRequired(user.value.password);
    } else {
        userErrors.value.password = '';
    }
};

/**
 * Validates the name field and sets error message.
 */
const validateNameField = (): void => {
    if (validateRequired(user.value.name)) {
        userErrors.value.name = validateRequired(user.value.name);
    } else {
        userErrors.value.name = '';
    }
};

/**
 * Validates the role field and sets error message.
 */
const validateRoleField = (): void => {
    if (validateRequired(user.value.role?.value)) {
        userErrors.value.role = validateRequired(user.value.role?.value);
    } else {
        userErrors.value.role = '';
    }
};

/**
 * Saves a user to the database.
 */
const saveUser = async (): Promise<void> => {
    submitted.value = true;
    try {
        await authStore.register(user.value);
        userDialog.value = false;
        await loadLazyData();
        toast.add({ severity: 'success', summary: 'Successful', detail: 'User Saved', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${authStore.error}`,
            detail: 'Error',
            life: 3000
        });
        await loadLazyData();
    }
};

/**
 * Prepares the user form for editing.
 * @param prod The user to be edited.
 */
const editUser = (prod: User): void => {
    userDialog.value = true;
    user.value = { ...prod };
};

/**
 * Opens the confirmation dialog to delete a user.
 * @param prod The user to be deleted.
 */
const confirmDeleteUser = (prod: User): void => {
    user.value = prod;
    deleteUserDialog.value = true;
};

/**
 * Deletes a user from the database.
 */
const deleteUser = async (): Promise<void> => {
    try {
        await authStore.deleteUser(user.value.id);
        deleteUserDialog.value = false;
        await loadLazyData();
        toast.add({ severity: 'success', summary: 'Successful', detail: 'User Deleted', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${authStore.error}`,
            detail: 'Error',
            life: 3000
        });
        await loadLazyData();
    }
};

/**
 * Exports the current data view to CSV.
 */
const exportCSV = (): void => {
    dt.value.exportCSV();
};

/**
 * Opens the confirmation dialog to delete selected users.
 */
const confirmDeleteSelected = (): void => {
    deleteUsersDialog.value = true;
};

/**
 * Deletes selected users from the database.
 */
const deleteSelectedUsers = async (): Promise<void> => {
    try {
        const idsToDelete = selectedUsers.value.map((user) => user.id.toString());

        await authStore.deleteUsers(idsToDelete);
        deleteUsersDialog.value = false;
        selectedUsers.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Users Deleted', life: 3000 });
        await loadLazyData();
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${authStore.error}`,
            detail: 'Error',
            life: 3000
        });
        await loadLazyData();
    }
};

/**
 * Returns the label for a given status.
 * @param status The status to get the label for.
 * @returns The label for the status.
 */
const getStatusLabel = (status: string): string | null => {
    switch (status) {
        case 'admin':
            return 'success';

        case 'user':
            return 'warn';

        case 'editor':
            return 'danger';

        default:
            return null;
    }
};
</script>

<template>
    <div>
        <div class="card">
            <Toolbar class="mb-6">
                <template #start>
                    <Button label="New" icon="pi pi-plus" severity="secondary" class="mr-2" @click="openNew" />
                    <Button label="Delete" icon="pi pi-trash" severity="secondary" @click="confirmDeleteSelected" :disabled="!selectedUsers || !selectedUsers.length" />
                </template>

                <template #end>
                    <Button label="Export" icon="pi pi-upload" severity="secondary" @click="exportCSV($event)" />
                </template>
            </Toolbar>

            <DataTable
                ref="dt"
                v-model:selection="selectedUsers"
                :first="reset"
                :value="users"
                dataKey="id"
                :paginator="true"
                :rows="10"
                :totalRecords="totalRecords"
                :lazy="true"
                :loading="userStore.loading"
                @page="loadLazyData"
                @sort="loadLazyData"
                filterDisplay="menu"
                :filters="filters"
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                :rowsPerPageOptions="[5, 10]"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} users"
            >
                <template #header>
                    <div class="flex flex-wrap gap-2 items-center justify-between">
                        <h4 class="m-0">Manage Users</h4>
                        <IconField>
                            <InputIcon>
                                <i class="pi pi-search" />
                            </InputIcon>
                            <InputText v-model="filters['global'].value" placeholder="Search..." @input="onFilter" />
                        </IconField>
                    </div>
                </template>

                <Column selectionMode="multiple" style="width: 3rem" :exportable="false"></Column>
                <Column field="id" header="ID" sortable style="min-width: 12rem"></Column>
                <Column field="name" header="Name" sortable style="min-width: 16rem"></Column>
                <Column field="email" header="Email" sortable style="min-width: 10rem"></Column>
                <Column field="role" header="Role" filterField="status" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" style="min-width: 10rem">
                    <template #body="{ data }">
                        <div class="flex items-center gap-2">
                            <Tag :value="data.role" :severity="getStatusLabel(data.role)" />
                        </div>
                    </template>
                    <template #filter="{ filterModel }">
                        <MultiSelect v-model="filters['status'].value" @value-change="onFilter" :options="statuses" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>

                <Column header="Actions" :exportable="false" style="min-width: 12rem">
                    <template #body="slotProps">
                        <Button icon="pi pi-pencil" outlined rounded class="mr-2" @click="editUser(slotProps.data)" />
                        <Button icon="pi pi-trash" outlined rounded severity="danger" @click="confirmDeleteUser(slotProps.data)" />
                    </template>
                </Column>
            </DataTable>
        </div>

        <Dialog v-model:visible="userDialog" :style="{ width: '450px' }" header="User Details" :modal="true" :closable="!authStore.loading">
            <div class="flex flex-col gap-6">
                <Message v-if="authStore.error && authStore.error?.message" class="flex justify-center" icon="pi pi-exclamation-circle" severity="error" size="small" variant="simple">
                    <span>{{ authStore.error.message }}</span>
                </Message>
            </div>
            <div class="flex flex-col gap-6">
                <div>
                    <label for="name" class="block font-bold mb-3">Name</label>
                    <IconField>
                        <InputText @input="validateNameField" id="name" v-model.trim="user.name" autofocus :invalid="userErrors.name.length > 0" fluid />
                        <InputIcon v-if="userErrors.name" class="pi pi-info-circle error" />
                    </IconField>
                    <small v-if="userErrors.name" class="text-red-500">{{ userErrors.name }}</small>
                </div>
                <div>
                    <label for="email" class="block font-bold mb-3">Email</label>
                    <IconField>
                        <InputText @input="validateEmailField" id="email" v-model.trim="user.email" autofocus :invalid="userErrors.email.length > 0" fluid />
                        <InputIcon v-if="userErrors.email" class="pi pi-info-circle error" />
                    </IconField>
                    <small v-if="userErrors.email" class="text-red-500">{{ userErrors.email }}</small>
                </div>
                <div>
                    <label for="password" class="block font-bold mb-3">Password</label>
                    <IconField>
                        <Password @input="validatePasswordField" id="email" :toggleMask="userErrors.password.length === 0" :invalid="userErrors.password.length > 0" v-model.trim="user.password" autofocus fluid />
                        <InputIcon v-if="userErrors.password" class="pi pi-info-circle error" />
                    </IconField>
                    <small v-if="userErrors.password" class="text-red-500">{{ userErrors.password }}</small>
                </div>
                <div>
                    <label for="role" class="block font-bold mb-3">Role</label>
                    <Select id="role" @change="validateRoleField" v-model="user.role" option-value="value" :options="statuses" optionLabel="label" placeholder="Select a Role" fluid></Select>
                    <small v-if="userErrors.role" class="text-red-500">{{ userErrors.role }}</small>
                </div>
            </div>

            <template #footer>
                <Button label="Cancel" icon="pi pi-times" text @click="hideDialog" :disabled="authStore.loading" />
                <Button :disabled="invalidSignUp" :loading="authStore.loading" label="Save" icon="pi pi-check" @click="saveUser" />
            </template>
        </Dialog>

        <Dialog v-model:visible="deleteUserDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!authStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span v-if="user"
                    >Are you sure you want to delete <b>{{ user.name }}</b
                    >?</span
                >
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteUserDialog = false" :disabled="authStore.loading" />
                <Button label="Yes" icon="pi pi-check" @click="deleteUser" :disabled="authStore.loading" :loading="authStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="deleteUsersDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!authStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span v-if="user">Are you sure you want to delete the selected users?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteUsersDialog = false" :disabled="authStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="deleteSelectedUsers" :loading="authStore.loading" />
            </template>
        </Dialog>
    </div>
</template>
