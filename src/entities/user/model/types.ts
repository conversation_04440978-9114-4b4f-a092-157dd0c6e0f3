import type { Timestamp } from 'firebase/firestore';
import { CompanyUserInfo } from '@/features/company-user-update';

/**
 * @interface User
 * Represents the structure of a user document.
 */
export interface User extends CompanyUserInfo {
    id?: string;

    name: string;
    email: string;
    role: string;
    profile_pic?: string;
    createdAt: Timestamp | Date; // Timestamp of when the tenant was created
    updatedAt?: Timestamp | Date; // Timestamp of the last update
    password?: string;
}

export interface UserFilters {
    name?: string;
    id?: string;
    email?: string;
    tenantId?: string;
    role?: string[];
    sort?: {
        col: string;
        order: 'asc' | 'desc';
    };
    page?: {
        pageSize: number;
        page: number;
    };
    dateRange?: {
        lastMessageTimeStart?: number;
        lastMessageTimeEnd?: number;
        to_followup_dateStart?: number;
        to_followup_dateEnd?: number;
    };
}
