import { firestore } from '@/firebase';
import { collection, doc } from 'firebase/firestore';

export const usersCollection = collection(firestore, 'users');

/**
 * Retrieves a Firestore document reference for a specific user.
 * @param {string} id - The unique identifier for the user.
 * @returns {DocumentReference} Firestore document reference.
 */
export const userDocument = (id: string) => doc(firestore, 'users', id);
