import { defineStore } from 'pinia';
import { ref } from 'vue';
import { updateDoc, deleteDoc, getDocs, query, where, addDoc, getDoc, limit, startAfter, endBefore, orderBy, or, and, writeBatch, doc, getCountFromServer, QueryConstraint } from 'firebase/firestore';
import { User, userDocument, UserFilters, usersCollection } from '@/entities/user';
import { requestFetchUsers } from '@/shared';

/**
 * @function useUsersStore
 * A Pinia store for managing CRUD operations on users with Firebase Firestore integration.
 */
export const useUsersStore = defineStore('users', () => {
    /**
     * @property {User[]} users - The list of users stored in the state.
     */
    const users = ref<User[]>([]);
    const totalUsers = ref<number>(0);
    const loading = ref<boolean>(false); // Loading state
    /**
     * @property {any} lastVisible - Tracks the last visible document for pagination.
     */
    const lastVisible = ref<any>(null);
    const pageNumber = ref<number>(1);
    const firstVisible = ref<any>(null);

    /**
     * Generates Firestore query constraints based on provided filters.
     * @param filters - The filters to apply to the Firestore query.
     * @returns An array of QueryConstraint objects for Firestore querying.
     */
    const generateFirestoreConstraints = (filters: UserFilters = {}) => {
        let q = query(usersCollection);
        // Apply filters
        if (filters.name) {
            const nameStart = filters.name;
            const nameEnd = filters.name + '\uf8ff';
            q = query(q, or(and(where('email', '>=', nameStart), where('email', '<=', nameEnd)), and(where('name', '>=', nameStart), where('name', '<=', nameEnd)), and(where('id', '>=', nameStart), where('id', '<=', nameEnd))));
        }
        if (filters.role && filters.role.length > 0) {
            q = query(q, where('role', 'in', filters.role));
        }
        if (filters.id) {
            q = query(q, where('id', '==', filters.id));
        }

        if (filters.tenantId) {
            q = query(q, where('tenantId', '==', filters.tenantId));
        }

        // Apply date range filters
        if (filters.dateRange?.lastMessageTimeStart && filters.dateRange?.lastMessageTimeEnd) {
            q = query(q, where('lastMessageTime', '>=', filters.dateRange.lastMessageTimeStart), where('lastMessageTime', '<=', filters.dateRange.lastMessageTimeEnd));
        }
        if (filters.dateRange?.to_followup_dateStart && filters.dateRange?.to_followup_dateEnd) {
            q = query(q, where('to_followup_date', '>=', filters.dateRange.to_followup_dateStart), where('to_followup_date', '<=', filters.dateRange.to_followup_dateEnd));
        }

        // Apply sorting
        if (filters.sort?.col && filters.sort?.order) {
            q = query(q, orderBy(filters.sort.col, filters.sort.order));
        }

        return q;
    };

    /**
     * Fetches paginated users from Firestore with optional filters and sorting.
     * @param {number} pageSize - Number of users to fetch per page.
     * @param {Object} filters - Filters to apply to the query.
     * @param {string} [filters.name] - Filter by user name (partial match).
     * @param {string} [filters.id] - Filter by user ID (exact match).
     * @param {string} [filters.number] - Filter by user phone number (partial match).
     * @param {string} [filters.user_source] - Filter by user source (exact match).
     * @param {string} [filters.user_status] - Filter by user status (exact match).
     * @param {boolean} [filters.is_assigned] - Filter by whether the user is assigned.
     * @param {string} [filters.assigned_to] - Filter by assigned user ID (exact match).
     * @param {boolean} [filters.is_converted] - Filter by whether the user is converted.
     * @param {boolean} [filters.is_qualified] - Filter by whether the user is qualified.
     * @param {Object} [filters.dateRange] - Date range filter for timestamp fields.
     * @param {number} [filters.dateRange.lastMessageTimeStart] - Start timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.lastMessageTimeEnd] - End timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.to_followup_dateStart] - Start timestamp for to_followup_date.
     * @param {number} [filters.dateRange.to_followup_dateEnd] - End timestamp for to_followup_date.
     * @param direction
     * @param {string} [sortOptions.field] - Field to sort by ('lastMessageTime' or 'to_followup_date').
     * @param {'asc' | 'desc'} [sortOptions.order] - Sort order ('asc' or 'desc').
     * @returns {Promise<void>}
     */
    const fetchUsers = async (pageSize: number = 10, filters: UserFilters = {}, direction: 'next' | 'prev' | 'first' | 'last' | '' = ''): Promise<void> => {
        try {
            // Build the query
            let q: any = generateFirestoreConstraints(filters);

            if (direction === 'next') {
                if (lastVisible.value) {
                    q = query(q, startAfter(lastVisible.value));
                }
                pageNumber.value++;
            } else if (direction === 'prev') {
                if (firstVisible.value) {
                    q = query(q, endBefore(firstVisible.value));
                }
                if (pageNumber.value > 1) {
                    pageNumber.value--;
                }
            } else if (direction === 'first') {
                pageNumber.value = 1;
                lastVisible.value = null;
                firstVisible.value = null;
            } else if (direction === 'last') {
                const totalCount = await countTotalUsers(filters);
                const lastPage = Math.ceil(totalCount / pageSize);
                pageNumber.value = lastPage;
                const lastPageSize = totalCount % pageSize || pageSize;
                const lastPageQuery = query(q, orderBy('id'), limit(lastPageSize));
                const lastPageSnapshot = await getDocs(lastPageQuery);
                if (!lastPageSnapshot.empty) {
                    lastVisible.value = lastPageSnapshot.docs[lastPageSnapshot.docs.length - 1];
                    q = query(q, startAfter(lastVisible.value));
                }
            } else {
                pageNumber.value = 1;
                lastVisible.value = null;
                firstVisible.value = null;
            }
            q = query(q, limit(pageSize));

            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                firstVisible.value = querySnapshot.docs[0];
                lastVisible.value = querySnapshot.docs[querySnapshot.docs.length - 1];
                const fetchedUsers = querySnapshot.docs.map((doc) => ({ id: doc.id, ...(doc.data() as object) })) as User[];

                users.value = fetchedUsers;
            } else {
                users.value = []; // No users found
            }
        } catch (error) {
            console.error('Error fetching users:', error);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Counts the total number of users in Firestore based on filters.
     * @param {Object} filters - Filters to apply to the query.
     * @param {string} [filters.name] - Filter by user name (partial match).
     * @param {string} [filters.id] - Filter by user ID (exact match).
     * @param {string} [filters.number] - Filter by user phone number (partial match).
     * @param {string} [filters.user_source] - Filter by user source (exact match).
     * @param {string} [filters.user_status] - Filter by user status (exact match).
     * @param {boolean} [filters.is_assigned] - Filter by whether the user is assigned.
     * @param {string} [filters.assigned_to] - Filter by assigned user ID (exact match).
     * @param {boolean} [filters.is_converted] - Filter by whether the user is converted.
     * @param {boolean} [filters.is_qualified] - Filter by whether the user is qualified.
     * @param {Object} [filters.dateRange] - Date range filter for timestamp fields.
     * @param {number} [filters.dateRange.lastMessageTimeStart] - Start timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.lastMessageTimeEnd] - End timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.to_followup_dateStart] - Start timestamp for to_followup_date.
     * @param {number} [filters.dateRange.to_followup_dateEnd] - End timestamp for to_followup_date.
     * @returns {Promise<number>} - The total count of users matching the filters.
     */
    const countTotalUsers = async (filters: UserFilters = {}): Promise<number> => {
        try {
            const countFilterst = filters;
            delete countFilterst.sort;
            loading.value = true;

            const q = generateFirestoreConstraints(countFilterst);
            const snapshot = await getCountFromServer(q);
            totalUsers.value = snapshot.data().count;
            return totalUsers.value;
        } catch (error) {
            console.error('Error counting total users:', error);
            return 0;
        } finally {
            loading.value = false;
        }
    };

    /**
     * Adds a new user to Firestore.
     * @param {Omit<User, 'id'>} userData - The user data to be added (excluding the ID).
     * @returns {Promise<void>}
     */
    const addUser = async (userData: Omit<User, 'id'>): Promise<void> => {
        try {
            const docRef = await addDoc(usersCollection, userData);
            users.value.push({ id: docRef.id, ...userData });
        } catch (error) {
            console.error('Error adding user:', error);
        }
    };

    const fetchUsersFromServer = async (filters: UserFilters = {}): Promise<void> => {
        try {
            loading.value = true;
            const test = await requestFetchUsers({ filters: filters, pageSize: filters.page?.pageSize ?? 10, page: filters.page?.page ?? 1 });
            totalUsers.value = test.totalSiza;
            users.value = test.resulta;
        } catch (error) {
            users.value = [];
            totalUsers.value = 0;
            console.error('Error adding user:', error);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Adds multiple new users to Firestore in a batch.
     * @param {Omit<User, 'id'>[]} usersData - An array of user data to be added (excluding IDs).
     * @returns {Promise<void>}
     */
    const addUsers = async (usersData: Omit<User, 'id'>[]): Promise<void> => {
        try {
            const batch = writeBatch(usersCollection.firestore);
            const newUsers: User[] = [];
            usersData.forEach((userData) => {
                const docRef = doc(usersCollection); // Create a new document reference
                batch.set(docRef, userData); // Add the data to the batch
                newUsers.push({ id: docRef.id, ...userData } as User); // Add to the local array
            });
            await batch.commit(); // Commit the batch write
            users.value.push(...newUsers); // Update the local state
        } catch (error) {
            console.error('Error adding multiple users:', error);
        }
    };

    /**
     * Updates multiple users in Firestore in a batch.
     * @param {Array<{ id: string; updatedData: Partial<User> }>} usersToUpdate - An array of objects, each containing the user ID and the data to update.
     * @returns {Promise<void>}
     */
    const updateUsers = async (usersToUpdate: Array<{ id: string; updatedData: Partial<User> }>): Promise<void> => {
        const batch = writeBatch(usersCollection.firestore);
        usersToUpdate.forEach(({ id, updatedData }) => batch.update(userDocument(id), updatedData));
        await batch.commit();
    };

    /**
     * Updates an existing user in Firestore.
     * @param {string} id - The ID of the user to update.
     * @param {Partial<User>} updatedData - The updated fields of the user.
     * @returns {Promise<void>}
     */
    const updateUser = async (id: string, updatedData: Partial<User>): Promise<void> => {
        try {
            const userRef = userDocument(id);
            await updateDoc(userRef, updatedData);
            const index = users.value.findIndex((user) => user.id === id);
            if (index !== -1) {
                users.value[index] = { ...users.value[index], ...updatedData };
            }
        } catch (error) {
            console.error('Error updating user:', error);
        }
    };

    /**
     * Deletes a user from Firestore.
     * @param {string} id - The ID of the user to delete.
     * @returns {Promise<void>}
     */
    const deleteUser = async (id: string): Promise<void> => {
        try {
            await deleteDoc(userDocument(id));
            users.value = users.value.filter((user) => user.id !== id);
        } catch (error) {
            console.error('Error deleting user:', error);
        }
    };

    /**
     * Fetches a single user by its ID from Firestore.
     * @param {string} id - The ID of the user to fetch.
     * @returns {Promise<User | null>} - The user object or null if not found.
     */
    const getUser = async (id: string): Promise<User | null> => {
        try {
            const userDoc = await getDoc(userDocument(id));
            if (userDoc.exists()) {
                return { id: userDoc.id, ...userDoc.data() } as User;
            }
            return null;
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        }
    };

    return {
        users,
        totalUsers,
        firstVisible,
        lastVisible,
        pageNumber,
        loading,
        fetchUsers,
        fetchUsersFromServer,
        countTotalUsers,
        getUser,
        addUser,
        addUsers,
        updateUsers,
        updateUser,
        deleteUser
    };
});
