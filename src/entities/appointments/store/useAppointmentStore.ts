import { defineStore } from 'pinia';
import { ref } from 'vue';
import { fetchAppointmentsFromFirestore, createAppointmentInFirestore, updateAppointmentInFirestore, deleteAppointmentFromFirestore, getAppointmentFromFirestore, countAppointmentsFromFirestore } from '../services/firestoreService';
import { Appointment, AppointmentFilters } from '../types';

export const useAppointmentEntityStore = defineStore('appointmententity', () => {
    const appointments = ref<Appointment[]>([]);
    const selectedAppointment = ref<Appointment | null>(null);
    const loading = ref(false);
    const error = ref<string | null>(null);
    const total = ref<number>(0);

    // CRUD Operations
    const loadAppointments = async (filters: AppointmentFilters = {}) => {
        loading.value = true;
        try {
            appointments.value = await fetchAppointmentsFromFirestore(filters);
            total.value = await countAppointmentsFromFirestore(filters);
        } catch (err) {
            error.value = 'Failed to load appointments.';
        } finally {
            loading.value = false;
        }
    };

    const searchAppointments = async (filters: AppointmentFilters = {}) => {
        loading.value = true;
        try {
            const appointments = await fetchAppointmentsFromFirestore(filters);
            console.log('Appointments found:', appointments);
            return appointments;
        } catch (err) {
            return [];
            error.value = 'Failed to load appointments.';
        } finally {
            loading.value = false;
        }
    };

    const createAppointment = async (data: Omit<Appointment, 'id'>) => {
        loading.value = true;
        try {
            const id = await createAppointmentInFirestore(data);

            return id;
        } catch (err) {
            handleError(err, 'Failed to create appointment');
            return null;
        } finally {
            loading.value = false;
        }
    };

    const updateAppointment = async (id: string, data: Partial<Appointment>) => {
        loading.value = true;
        try {
            await updateAppointmentInFirestore(id, data);
        } catch (err) {
            handleError(err, 'Failed to update appointment');
        } finally {
            loading.value = false;
        }
    };

    const deleteAppointment = async (id: string) => {
        loading.value = true;
        try {
            console.log('Deleting appointment:', id);
            await deleteAppointmentFromFirestore(id);
        } catch (err) {
            handleError(err, 'Failed to delete appointment');
        } finally {
            loading.value = false;
        }
    };

    const selectAppointment = async (id: string) => {
        loading.value = true;
        try {
            const data = await getAppointmentFromFirestore(id);
            selectedAppointment.value = data;
            return data;
        } catch (err) {
            handleError(err, 'Failed to load appointment details');
            return null;
        } finally {
            loading.value = false;
        }
    };

    // Reset selected appointment
    const clearSelected = () => {
        selectedAppointment.value = null;
    };

    // Helper
    const handleError = (err: unknown, defaultMessage: string) => {
        console.error(err);
        error.value = defaultMessage;
    };

    return {
        // State
        appointments,
        selectedAppointment,
        loading,
        error,

        // Actions
        searchAppointments,
        loadAppointments,
        createAppointment,
        updateAppointment,
        deleteAppointment,
        selectAppointment,
        clearSelected
    };
});
