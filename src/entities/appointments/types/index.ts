import { AppointmentFormData } from '@/features/appointment-booking/types';

export interface Appointment extends AppointmentFormData {}
// types/AppointmentFilters.ts

export interface AppointmentFilters {
    // Text Search
    tenantId?: string;
    searchTerm?: string;
    searchTerms?: string[];

    // Date Range
    dateRange?: {
        appointmentDateStart?: number;
        appointmentDateEnd?: number;
    };

    // Property Filters
    doorType?: string[];
    tradeIn?: boolean;

    // Sorting
    sort?: {
        col: string;
        order: 'asc' | 'desc';
    };
}
