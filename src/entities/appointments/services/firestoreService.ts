import { collection, doc, getDoc, getDocs, addDoc, updateDoc, deleteDoc, query, orderBy, where } from 'firebase/firestore';
import { firestore } from '@/firebase';
import { Appointment, AppointmentFilters } from '../types';

const APPOINTMENTS_COLLECTION = 'appointments';

// Create new appointment
export const createAppointmentInFirestore = async (data: Omit<Appointment, 'id'>) => {
    const now = Date.now();
    const payload = {
        ...data,
        createdAt: now,
        updatedAt: now
    };

    const docRef = await addDoc(collection(firestore, APPOINTMENTS_COLLECTION), payload);
    return docRef.id;
};

// Update existing appointment
export const updateAppointmentInFirestore = async (id: string, data: Partial<Appointment>) => {
    const payload = {
        ...data,
        updatedAt: Date.now()
    };

    await updateDoc(doc(firestore, APPOINTMENTS_COLLECTION, id), payload);
};

// Delete appointment
export const deleteAppointmentFromFirestore = async (id: string) => {
    await deleteDoc(doc(firestore, APPOINTMENTS_COLLECTION, id));
};

// Get single appointment
export const getAppointmentFromFirestore = async (id: string): Promise<Appointment | null> => {
    const docSnap = await getDoc(doc(firestore, APPOINTMENTS_COLLECTION, id));
    if (!docSnap.exists()) return null;

    return {
        id: docSnap.id,
        ...docSnap.data()
    } as Appointment;
};

export const generateFirestoreConstraints = (filters: AppointmentFilters = {}, removeSort: boolean = false) => {
    let q = query(collection(firestore, APPOINTMENTS_COLLECTION));

    // Text Search
    if (filters.searchTerm) {
        q = query(q, where('keywords', 'array-contains', filters.searchTerm));
    }

    if (filters.searchTerms && filters.searchTerms.length > 0) {
        q = query(q, where('keywords', 'array-contains-any', filters.searchTerms));
    }

    // Date Range
    if (filters.dateRange?.appointmentDateStart !== undefined) {
        q = query(q, where('appointmentDate', '>=', filters.dateRange.appointmentDateStart));
    }

    if (filters.dateRange?.appointmentDateEnd !== undefined) {
        q = query(q, where('appointmentDate', '<=', filters.dateRange.appointmentDateEnd));
    }

    // Door Type
    if (filters.doorType && filters.doorType.length > 0) {
        q = query(q, where('doorType', 'in', filters.doorType));
    }

    // Trade-In Filter
    if (typeof filters.tradeIn === 'boolean') {
        q = query(q, where('tradeIn', '==', filters.tradeIn));
    }

    // Sorting
    if (!removeSort && filters.sort?.col && filters.sort.order) {
        q = query(q, orderBy(filters.sort.col, filters.sort.order));
    }

    return q;
};

export const fetchAppointmentsFromFirestore = async (filters: AppointmentFilters = {}): Promise<Appointment[]> => {
    const q = generateFirestoreConstraints(filters);
    const snapshot = await getDocs(q);

    return snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data()
    })) as Appointment[];
};

export const countAppointmentsFromFirestore = async (filters: AppointmentFilters = {}): Promise<number> => {
    const q = generateFirestoreConstraints(filters);
    const snapshot = await getDocs(q);
    return snapshot.size;
};
