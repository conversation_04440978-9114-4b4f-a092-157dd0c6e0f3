<script setup lang="ts">
import { useLayout } from '@/layout/composables/layout';
import AppointmentBooking from '@/features/appointment-booking/AppointmentBooking.vue';
import FloatingConfigurator from '@/components/FloatingConfigurator.vue';
import { useRoute } from 'vue-router';
import { onMounted, Ref, ref } from 'vue';
import { decryptData, Lead } from '@/entities/lead';

const { logo } = useLayout();

const route = useRoute();
const leadData: Ref<Lead | null> = ref(null);

onMounted(() => {
    // decode base64
    try {
        leadData.value = decryptData(String(route.query.lead)) as Lead;
        console.log('leadData', leadData.value);
    } catch (err) {
        console.log(err);
    }
});
</script>

<template>
    <div class="flex items-center justify-center min-h-screen overflow-hidden">
        <FloatingConfigurator />
        <div class="flex flex-col items-center justify-center">
            <img :src="`/logo/${logo}`" class="mb-0 w-16 shrink-0 mx-auto" />
            <div style="border-radius: 56px; padding: 0.3rem; background: linear-gradient(180deg, color-mix(in srgb, var(--primary-color), transparent 60%) 10%, var(--surface-ground) 30%)">
                <div class="w-full bg-surface-0 dark:bg-surface-900 py-5 px-8 sm:px-20 flex flex-col items-center" style="border-radius: 53px">
                    <AppointmentBooking :leadData="leadData" />
                </div>
            </div>
        </div>
    </div>
</template>
