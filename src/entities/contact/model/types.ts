export interface Contact {
    id?: string;
    first_name?: string;
    last_name?: string;
    name?: string;
    email?: string;
    phone?: string;
    gender?: string;
    profile_pic?: string;
    locale?: string;
    timezone?: string;
    address?: string;
}

export interface ContactFilters {
    name?: string;
    id?: string;
    email?: string;
    searchTerm?: string;
    number?: string;
    sort?: {
        col: string;
        order: 'asc' | 'desc';
    };
    page?: {
        pageSize?: number;
        page?: number;
        currentPage?: number;
    };
    dateRange?: {
        lastMessageTimeStart?: number;
        lastMessageTimeEnd?: number;
    };
}
