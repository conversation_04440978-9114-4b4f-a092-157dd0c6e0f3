/**
 * Formats a timestamp into a human-readable relative time string.
 * @param {string | number | Date} timestamp The timestamp to format.
 * @returns {string} A formatted string representing the relative time.
 */
export const formatDate = (timestamp: number): string => {
    const now = new Date();
    const date = new Date(timestamp * 1000);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        // Show the full date if the difference is more than a week
        return date.toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true // Use 12-hour format with AM/PM
        });
    }
};

/**
 * Converts a timestamp into a human-readable date string.
 * @param {number} timestamp The timestamp to convert.
 * @returns {string} A formatted date string.
 */
export const getHumanReadableDate = (timestamp: number): string => {
    const now = new Date(timestamp * 1000); // Get the current date from Date.now()
    return now.toLocaleString('en-US', {
        // Example format for US
        weekday: 'long',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true // Use 12-hour format with AM/PM
    });
};
export const getShortHumanReadableDate = (timestamp: number): string => {
    const now = new Date(timestamp * 1000); // Get the current date from Date.now()
    return now.toLocaleString('en-US', {
        // Example format for US
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true // Use 12-hour format with AM/PM
    });
};

// Helper function to calculate a date 10 days ahead or behind a given date
export const getDatePlusMinusDays = (date: Date | number | string, addDays: number) => {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + addDays);
    return newDate;
};

export const getDateDifferenceInDays = (date1: Date, date2: Date) => {
    return Math.abs((date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24));
};

export const chartOptions = () => {
    const documentStyle = getComputedStyle(document.documentElement);
    const borderColor = documentStyle.getPropertyValue('--surface-border');
    const textMutedColor = documentStyle.getPropertyValue('--text-color-secondary');
    return {
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        responsive: true,
        borderColor: 'transparent',
        scales: {
            x: {
                stacked: true,
                grid: {
                    color: 'transparent',
                    borderColor: 'transparent'
                }
            },
            y: {
                stacked: true,
                ticks: {
                    color: textMutedColor
                },

                grid: {
                    color: borderColor,
                    borderColor: 'transparent',
                    drawTicks: false
                }
            }
        }
    };
};
