import { firestore } from '@/firebase';
import { collection, doc } from 'firebase/firestore';

export const contactsCollection = collection(firestore, 'contacts');

/**
 * Retrieves a Firestore document reference for a specific contact.
 * @param {string} id - The unique identifier for the contact.
 * @returns {DocumentReference} Firestore document reference.
 */
export const contactDocument = (id: string) => doc(firestore, 'contacts', id);
