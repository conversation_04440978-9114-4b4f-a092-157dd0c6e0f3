import { defineStore } from 'pinia';
import { ref } from 'vue';
import { setDoc, deleteDoc, getDocs, query, where, addDoc, getDoc, limit, limitToLast, startAfter, startAt, endBefore, endAt, orderBy, writeBatch, doc, getCountFromServer, or, and } from 'firebase/firestore';
import { Contact, contactDocument, ContactFilters, contactsCollection } from '@/entities/contact';
import { AuthError } from '@/entities/auth';

/**
 * @function useContactsStore
 * A Pinia store for managing CRUD operations on contacts with Firebase Firestore integration.
 */
export const useContactsStore = defineStore('contacts', () => {
    /**
     * @property {Contact[]} contacts - The list of contacts stored in the state.
     */
    const contacts = ref<Contact[]>([]);
    const totalContacts = ref<number>(0);
    const loading = ref<boolean>(false); // Loading state
    // Define the required columns (case-insensitive)
    const requiredUploadColumns = ref(['first_name', 'last_name', 'phone', 'email', 'postcode']);

    /**
     * @property {any} lastVisible - Tracks the last visible document for pagination.
     */
    const previousLastVisible = ref<any>(null);
    const lastVisible = ref<any>(null);
    const pageNumber = ref<number>(1);
    const firstVisible = ref<any>(null);
    const error = ref<AuthError | null>(null); // Error state
    const breadCrumbs = ref<any[]>([]); // Error state
    /**
     * Generates Firestore query constraints based on provided filters.
     * @param filters - The filters to apply to the Firestore query.
     * @returns An array of QueryConstraint objects for Firestore querying.
     */
    const generateFirestoreConstraints = (filters: ContactFilters = {}, removeSort: boolean = false) => {
        let q = query(contactsCollection);

        // Apply filters
        // Search:
        if (filters.searchTerm) {
            q = query(q, where('keywords', 'array-contains', filters.searchTerm));
        }

        // Apply date range filters
        if (filters.dateRange?.lastMessageTimeStart && filters.dateRange?.lastMessageTimeEnd) {
            q = query(q, where('lastMessageTime', '>=', filters.dateRange.lastMessageTimeStart));
            q = query(q, where('lastMessageTime', '<=', filters.dateRange.lastMessageTimeEnd));
        }

        // Apply sorting
        if (filters.sort?.col && filters.sort?.order && !removeSort) {
            q = query(q, orderBy(filters.sort.col, filters.sort.order));
        }

        return q;
    };

    /**
     * Fetches paginated contacts from Firestore with optional filters and sorting.
     * @param {number} pageSize - Number of contacts to fetch per page.
     * @param {Object} filters - Filters to apply to the query.
     * @param {string} [filters.name] - Filter by contact name (partial match).
     * @param {string} [filters.id] - Filter by contact ID (exact match).
     * @param {string} [filters.number] - Filter by contact phone number (partial match).
     * @param {string} [filters.contact_source] - Filter by contact source (exact match).
     * @param {string} [filters.contact_status] - Filter by contact status (exact match).
     * @param {boolean} [filters.is_assigned] - Filter by whether the contact is assigned.
     * @param {string} [filters.assigned_to] - Filter by assigned contact ID (exact match).
     * @param {boolean} [filters.is_converted] - Filter by whether the contact is converted.
     * @param {boolean} [filters.is_qualified] - Filter by whether the contact is qualified.
     * @param {Object} [filters.dateRange] - Date range filter for timestamp fields.
     * @param {number} [filters.dateRange.lastMessageTimeStart] - Start timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.lastMessageTimeEnd] - End timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.to_followup_dateStart] - Start timestamp for to_followup_date.
     * @param {number} [filters.dateRange.to_followup_dateEnd] - End timestamp for to_followup_date.
     * @param direction
     * @param {string} [sortOptions.field] - Field to sort by ('lastMessageTime' or 'to_followup_date').
     * @param {'asc' | 'desc'} [sortOptions.order] - Sort order ('asc' or 'desc').
     * @returns {Promise<void>}
     */
    const fetchContacts = async (pageSize: number = 10, filters: ContactFilters = {}, direction: 'next' | 'prev' | 'first' | 'last' | 'current' | '' = ''): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            contacts.value = [];
            let q = generateFirestoreConstraints(filters);

            if (direction === 'current') {
                if (previousLastVisible.value) {
                    q = query(q, startAt(previousLastVisible.value));
                }
            } else if (direction === 'next') {
                if (lastVisible.value) {
                    previousLastVisible.value = lastVisible.value;
                    q = query(q, startAfter(lastVisible.value));
                }
                pageNumber.value++;
            } else if (direction === 'prev') {
                if (firstVisible.value) {
                    q = query(q, endBefore(firstVisible.value));
                    q = query(q, limitToLast(pageSize));
                }
                pageNumber.value = Math.max(1, pageNumber.value - 1);
            } else if (direction === 'first') {
                breadCrumbs.value = [];
                pageNumber.value = 1;
                lastVisible.value = null;
                firstVisible.value = null;
                previousLastVisible.value = null;
            } else if (direction === 'last') {
                breadCrumbs.value = [];

                const totalCount = totalContacts.value;
                const lastPage = Math.ceil(totalCount / pageSize);
                pageNumber.value = lastPage;

                // Calculate how many items to skip
                const offset = (lastPage - 1) * pageSize;
                if (offset > 0) {
                    // Get the first document of the last page to use as startAfter point
                    const offsetQuery = query(q, limit(offset));

                    const offsetSnapshot = await getDocs(offsetQuery);
                    if (!offsetSnapshot.empty) {
                        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
                        q = query(q, startAfter(lastDoc), limit(pageSize));
                    }
                } else {
                    q = query(q, limit(pageSize));
                }
            } else {
                // Default case (first page)
                pageNumber.value = 1;
                lastVisible.value = null;
                firstVisible.value = null;
                previousLastVisible.value = null;
                if (pageSize > 0) q = query(q, limit(pageSize));
            }
            if (direction !== 'last' && direction !== 'prev') {
                q = query(q, limit(pageSize));
            }

            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                firstVisible.value = querySnapshot.docs[0];
                lastVisible.value = querySnapshot.docs[querySnapshot.docs.length - 1];

                const fetchedContacts = querySnapshot.docs.map((doc) => {
                    const data = doc.data() as any;

                    let conversations = data.conversations || [];
                    if (!Array.isArray(conversations)) {
                        // If conversations is not an array, convert it to an array
                        conversations = Object.entries(conversations).map(([key, value], index) => ({
                            // type assertion here
                            id: key,
                            ...(value as any)
                        }));
                    }

                    return {
                        id: doc.id,
                        conversations: conversations,
                        conversations_count: conversations.length || 0,
                        ...data
                    };
                }) as Contact[];
                contacts.value = fetchedContacts;
            } else {
                contacts.value = [];
            }
        } catch (err: any) {
            console.error('Error fetching contacts:', err);
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            contacts.value = [];
        } finally {
            loading.value = false;
        }
    };

    /**
     * Counts the total number of contacts in Firestore based on filters.
     * @param {Object} filters - Filters to apply to the query.
     * @param {string} [filters.name] - Filter by contact name (partial match).
     * @param {string} [filters.id] - Filter by contact ID (exact match).
     * @param {string} [filters.number] - Filter by contact phone number (partial match).
     * @param {string} [filters.contact_source] - Filter by contact source (exact match).
     * @param {string} [filters.contact_status] - Filter by contact status (exact match).
     * @param {boolean} [filters.is_assigned] - Filter by whether the contact is assigned.
     * @param {string} [filters.assigned_to] - Filter by assigned contact ID (exact match).
     * @param {boolean} [filters.is_converted] - Filter by whether the contact is converted.
     * @param {boolean} [filters.is_qualified] - Filter by whether the contact is qualified.
     * @param {Object} [filters.dateRange] - Date range filter for timestamp fields.
     * @param {number} [filters.dateRange.lastMessageTimeStart] - Start timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.lastMessageTimeEnd] - End timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.to_followup_dateStart] - Start timestamp for to_followup_date.
     * @param {number} [filters.dateRange.to_followup_dateEnd] - End timestamp for to_followup_date.
     * @returns {Promise<number>} - The total count of contacts matching the filters.
     */
    const countTotalContacts = async (filters: ContactFilters = {}): Promise<void> => {
        try {
            const countFilterst = filters;
            delete countFilterst.sort;
            loading.value = true;
            error.value = null;

            const q = generateFirestoreConstraints(countFilterst);
            const snapshot = await getCountFromServer(q);
            totalContacts.value = snapshot.data().count;
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error counting total contacts:', err);
        } finally {
            loading.value = false;
        }
    };
    const getContactsForDashboard = async (filters: ContactFilters = {}): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;

            const q = generateFirestoreConstraints(filters);
            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                const fetchedContacts = querySnapshot.docs.map((doc) => {
                    const data = doc.data() as any;

                    let conversations = data.conversations || [];
                    if (!Array.isArray(conversations)) {
                        // If conversations is not an array, convert it to an array
                        conversations = Object.entries(conversations).map(([key, value], index) => ({
                            // type assertion here
                            id: key,
                            ...(value as any)
                        }));
                    }

                    return {
                        id: doc.id,
                        conversations: conversations,
                        conversations_count: conversations.length || 0,
                        ...data
                    };
                }) as Contact[];
                contacts.value = fetchedContacts;
            } else {
                contacts.value = [];
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error counting total contacts:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Adds a new contact to Firestore.
     * @param {Omit<Contact, 'id'>} contactData - The contact data to be added (excluding the ID).
     * @returns {Promise<void>}
     */
    const addContact = async (contactData: Omit<Contact, 'id'>): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const docRef = await addDoc(contactsCollection, contactData);
            contacts.value.push({ id: docRef.id, ...contactData });
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error adding contact:', err);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Adds multiple new contacts to Firestore in a batch.
     * @param {Omit<Contact, 'id'>[]} contactsData - An array of contact data to be added (excluding IDs).
     * @returns {Promise<void>}
     */
    const addContacts = async (contactsData: Omit<Contact, 'id'>[]): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const batch = writeBatch(contactsCollection.firestore);
            const newContacts: Contact[] = [];
            contactsData.forEach((contactData) => {
                const docRef = doc(contactsCollection); // Create a new document reference
                batch.set(docRef, contactData); // Add the data to the batch
                newContacts.push({ id: docRef.id, ...contactData } as Contact); // Add to the local array
            });
            await batch.commit(); // Commit the batch write
            contacts.value.push(...newContacts); // Update the local state
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error adding multiple contacts:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Syncs all contacts in Firestore by setting the ai_conversation_summary to an empty string.
     * @returns {Promise<void>}
     */
    const syncContacts = async (): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;

            const q = query(contactsCollection);
            const querySnapshot = await getDocs(q);
            const contactsToUpdate = querySnapshot.docs.map((doc) => {
                const data = doc.data() as any;

                // let conversations = data.conversations || [];
                // if (!Array.isArray(conversations)) {
                //     // If conversations is not an array, convert it to an array
                //     conversations = Object.entries(conversations).map(([key, value], index) => ({
                //         // type assertion here
                //         id: key,
                //         ...(value as any)
                //     }));
                // }
                data.keywords = [...(data.name ? [data.name] : []), ...(data.email ? [data.email] : []), ...(data.phone ? [data.phone] : []), ...(data.id ? [data.id] : [])];
                return {
                    id: doc.id,
                    ...data
                    // conversations: conversations,
                    // conversations_count: conversations.length || 0,
                    //  lastMessageTime: Math.floor(data!.contact_details?.dateCreated?.toMillis() / 1000) || data!.lastMessageTime,
                    // mark_for_deletion: data?.mark_for_deletion || false
                };
            });

            console.log('Syncing...');
            console.log(contactsToUpdate.length);
            const batch = writeBatch(contactsCollection.firestore);
            contactsToUpdate.forEach((data) => batch.set(contactDocument(data.id), data, { merge: true }));
            await batch.commit();
            console.log('All contacts synced successfully.');
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error adding multiple contacts:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Updates multiple contacts in Firestore in a batch.
     * @param {Array<{ id: string; updatedData: Partial<Contact> }>} contactsToUpdate - An array of objects, each containing the contact ID and the data to update.
     * @returns {Promise<void>}
     */
    const updateContacts = async (contactsToUpdate: Array<{ id: string; updatedData: Partial<Contact> }>): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const batch = writeBatch(contactsCollection.firestore);
            contactsToUpdate.forEach(({ id, updatedData }) => batch.update(contactDocument(id), updatedData));
            await batch.commit();
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error updating multiple contacts:', err);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Updates an existing contact in Firestore.
     * @param {string} id - The ID of the contact to update.
     * @param {Partial<Contact>} updatedData - The updated fields of the contact.
     * @returns {Promise<void>}
     */
    const updateContact = async (id: string, updatedData: Partial<Contact>): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const contactRef = contactDocument(id);

            // Remove undefined values from updatedData
            const filteredUpdatedData = Object.fromEntries(Object.entries(updatedData).filter(([_, v]) => v !== undefined));
            await setDoc(contactRef, filteredUpdatedData, { merge: true });
            const index = contacts.value.findIndex((contact) => contact.id === id);
            if (index !== -1) {
                contacts.value[index] = { ...contacts.value[index], ...updatedData };
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error updating contact:', err);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Deletes a contact from Firestore.
     * @param {string} id - The ID of the contact to delete.
     * @returns {Promise<void>}
     */
    const deleteContact = async (id: string): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            await deleteDoc(contactDocument(id));
            contacts.value = contacts.value.filter((contact) => contact.id !== id);
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
        } finally {
            loading.value = false;
        }
    };

    const deleteContacts = async (contactIds: string[]) => {
        const batch = writeBatch(contactsCollection.firestore);
        try {
            loading.value = true;
            error.value = null;
            contactIds.forEach((id) => {
                batch.delete(contactDocument(id));
            });
            await batch.commit();
            contacts.value = contacts.value.filter((contact) => !contactIds.includes(contact.id as string));
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting contacts:', err);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Fetches a single contact by its ID from Firestore.
     * @param {string} id - The ID of the contact to fetch.
     * @returns {Promise<Contact | null>} - The contact object or null if not found.
     */
    const getContact = async (id: string): Promise<Contact | null> => {
        try {
            loading.value = true;
            error.value = null;
            const contactDoc = await getDoc(contactDocument(id));
            if (contactDoc.exists()) {
                return { id: contactDoc.id, ...contactDoc.data() } as Contact;
            }
            return null;
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error fetching contact:', err);
            return null;
        } finally {
            loading.value = false;
        }
    };
    const importContactsFromExcelFile = async (data: any[]): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;

            // Validate the Excel columns
            const columns = Object.keys(data[0] || {}).map((col) => col.toLowerCase());
            const missingColumns = requiredUploadColumns.value.filter((requiredCol) => !columns.includes(requiredCol));

            if (missingColumns.length > 0) {
                throw new Error(`Missing or invalid columns: ${missingColumns.join(', ')}`);
            }

            // Convert column headers to lowercase for consistency
            const normalizedData = data.map((row: any) => Object.fromEntries(Object.entries(row).map(([key, value]) => [key.toLowerCase(), value])));

            const batch = writeBatch(contactsCollection.firestore); // Create a Firestore batch

            // Track skipped records
            const skippedRecords: string[] = [];

            // Process all records in parallel using Promise.all
            await Promise.all(
                normalizedData.map(async (item) => {
                    // Validate if the record already exists based on phone or email
                    let existingDoc;

                    const q = query(contactsCollection, or(where('phone', '==', item.phone), where('email', '==', item.email)));

                    const querySnapshot = await getDocs(q);
                    if (!querySnapshot.empty) {
                        existingDoc = querySnapshot.docs[0]; // Return the first matching document
                    }

                    if (existingDoc) {
                        // Merge the data into the existing document
                        const docRef = contactDocument(existingDoc.id); // Reference the existing document
                        batch.set(docRef, item, { merge: true }); // Merge the new data into the existing document
                        console.log(`Merged record with phone ${item.phone} and email ${item.email}`);
                    } else {
                        // Add a new document if no match is found
                        const docRef = doc(contactsCollection); // Generate a new document reference
                        batch.set(docRef, item); // Create a new document
                        console.log(`Added new record with phone ${item.phone} and email ${item.email}`);
                    }
                })
            );

            // Commit the batch to Firestore
            await batch.commit();
            console.log('Batch committed successfully');

            if (skippedRecords.length > 0) {
                console.warn(`Skipped ${skippedRecords.length} records:`, skippedRecords);
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error fetching contact:', err);
        } finally {
            loading.value = false;
        }
    };

    return {
        requiredUploadColumns,
        contacts,
        totalContacts,
        firstVisible,
        lastVisible,
        previousLastVisible,
        pageNumber,
        loading,
        error,
        getContactsForDashboard,
        fetchContacts,
        countTotalContacts,
        getContact,
        addContact,
        addContacts,
        syncContacts,
        updateContacts,
        updateContact,
        deleteContact,
        importContactsFromExcelFile,
        deleteContacts
    };
});
