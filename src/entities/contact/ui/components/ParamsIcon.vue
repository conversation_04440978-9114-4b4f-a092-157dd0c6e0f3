<script setup lang="ts">
import { ParamsDetails } from '@/entities/lead';

const props = withDefaults(
    defineProps<{
        title?: string;
        text?: string | object;
        icon?: string;
        icon_color?: string;
        no_border?: boolean;
    }>(),
    {
        title: 'N/A',
        text: 'N/A',
        icon: 'pi-users',
        icon_color: 'yellow',
        no_border: false
    }
);
</script>

<template>
    <div :class="`col-span-12 md:col-span-12 lg:col-span-4 p-0 lg:pr-8 ${props.no_border ? '' : ' lg:pb-8 mb-4 lg:mt-0'} `" v-if="props.text">
        <div :class="typeof props.text === 'object' || props.no_border ? '' : 'border-surface-300 dark:border-surface-500 border-b'">
            <div class="bg-surface-0 dark:bg-surface-900">
                <div class="flex items-center justify-start">
                    <div :class="`flex items-center justify-center bg-${props.icon_color}-200 mb-2`" style="width: 3rem; height: 3rem; border-radius: 10px">
                        <i :class="`pi ${props.icon} !text-2xl text-${props.icon_color}-700`"></i>
                    </div>
                    <h2 class="ml-4 mb-2 text-surface-900 dark:text-surface-0">{{ props.title }}</h2>
                </div>
                <div>
                    <ParamsDetails v-if="typeof props.text === 'object'" :properties="props.text" />
                    <div v-else :class="`text-surface-600 dark:text-surface-200 ${props.no_border ? '' : 'pb-2'}`">{{ props.text }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss"></style>
