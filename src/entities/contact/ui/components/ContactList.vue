<script setup lang="ts">
import { FilterMatchMode } from '@primevue/core/api';
import { useToast } from 'primevue/usetoast';
import { computed, onMounted, Ref, ref, watch } from 'vue';
import { Contact, ContactFilters, ParamsIcon, useContactsStore, getHumanReadableDate, getDatePlusMinusDays, ImportContact } from '@/entities/contact';
import { validateEmail, validateRequired } from '@/utils';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

const contactStore = useContactsStore();
const contacts = ref<Contact[]>([]);
const totalRecords = ref<number>(0);
const currentPage = ref<number>(0);

onMounted(async () => {
    // await contactStore.syncContacts();
    await loadLazyData();
});

const toast = useToast();
const reset = ref<number>(0);
const reloadTable = ref<boolean>(false);
const dt = ref<any>();
const contactDialog = ref<boolean>(false);
const contactAiDialog = ref<boolean>(false);
const contactDetailsDialog = ref<boolean>(false);
const deleteContactDialog = ref<boolean>(false);
const deleteContactsDialog = ref<boolean>(false);
const importContactsDialog = ref<boolean>(false);
const contact = ref<Contact | null>(null);
const rows = ref<number>(10);
const directionTrigger = ref<string>('');
const contactErrors = ref<{ email: string; name: string }>({
    email: '',
    name: ''
});
const selectedContacts = ref<Contact[] | null>(null);
const filters = ref();
const sortbackup = ref({
    sortField: 'lastMessageTime',
    sortOrder: 2
});

const initFilters = () => {
    selectedContacts.value = null;
    totalRecords.value = 0;
    rows.value = 10;
    reset.value = 0;
    contacts.value = [];
    filters.value = {
        lastFilter: {},
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        dateFrom: { value: null, matchMode: FilterMatchMode.EQUALS },
        dateTo: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};
initFilters();
const clearFilter = async () => {
    initFilters();
    reloadTable.value = true;
    directionTrigger.value = '';
    await loadLazyData();
};
const invalidSignUp = computed(() => {
    return validateRequired(contact?.value?.email as string).length > 0 || validateEmail(contact?.value?.email as string).length > 0;
});

/**
 * Validates the email field and sets error message.
 */
const validateEmailField = (): void => {
    if (validateRequired(contact?.value?.email as string) || validateEmail(contact?.value?.email as string)) {
        contactErrors.value.email = validateRequired(contact?.value?.email as string) || validateEmail(contact?.value?.email as string);
    } else {
        contactErrors.value.email = '';
    }
};

/**
 * Validates the name field and sets error message.
 */
const validateNameField = (): void => {
    if (validateRequired(contact?.value?.name as string)) {
        contactErrors.value.name = validateRequired(contact?.value?.name as string);
    } else {
        contactErrors.value.name = '';
    }
};

/**
 * Handles filter operations.
 */
const onFilter = async (): Promise<void> => {
    reloadTable.value = true;
    directionTrigger.value = '';
    await loadLazyData();
};

/**
 * Loads data based on current filters.
 * @param event Optional event parameter for pagination and sorting.
 */
const loadLazyData = async (event?: any): Promise<void> => {
    const { global, lastFilter, dateFrom, dateTo } = filters.value;

    const searchFilters: ContactFilters = {
        searchTerm: global?.value ?? '',
        dateRange: {
            lastMessageTimeStart: dateFrom?.value ? Math.floor(dateFrom?.value?.getTime() / 1000) : null,
            lastMessageTimeEnd: dateTo?.value ? Math.floor(dateTo?.value?.getTime() / 1000) : null
        },
        sort: { col: event?.sortField || 'lastMessageTime', order: event?.sortOrder !== 1 ? 'desc' : 'asc' },
        page: { pageSize: event?.rows || rows.value, page: event?.page + 1 || 1, currentPage: event?.page || 0 }
    };

    await contactStore.countTotalContacts(searchFilters);
    totalRecords.value = contactStore.totalContacts;
    const lastPage = Math.ceil(totalRecords.value / rows.value) - 1;

    let direction = 'first';

    if (event && event.sortField) {
        searchFilters.sort = { col: event.sortField, order: event.sortOrder === 1 ? 'asc' : 'desc' };
    } else {
        searchFilters.sort = { col: sortbackup.value.sortField, order: sortbackup.value.sortOrder === 1 ? 'asc' : 'desc' };
    }

    if (lastFilter?.sort?.order !== searchFilters?.sort?.order || lastFilter?.sort?.col !== searchFilters?.sort?.col || lastFilter?.page?.pageSize !== searchFilters?.page?.pageSize || reloadTable.value === true) {
        reloadTable.value = true;
        directionTrigger.value = '';
        direction = 'first';
        reset.value = 0;
        searchFilters.page.currentPage = 0;
        currentPage.value = 0;
        if (event?.page) event.page = 0;
    }

    if (directionTrigger?.value) {
        direction = 'current';
    } else if (event?.page === 0) {
        direction = 'first';
    } else if (event?.page === lastPage) {
        direction = 'last';
    } else if (event?.page > currentPage.value) {
        direction = 'next';
    } else if (event?.page < currentPage.value) {
        direction = 'prev';
    }

    await contactStore.fetchContacts(event?.rows || rows.value, searchFilters, direction);

    contacts.value = contactStore.contacts;
    if (!directionTrigger?.value) {
        currentPage.value = event?.page || 0;
    }

    filters.value.lastFilter = searchFilters;
    if (typeof event === 'object') {
        if (event.sortField) {
            sortbackup.value = {
                sortField: event.sortField,
                sortOrder: event.sortOrder
            };
        } else {
            sortbackup.value = {
                sortField: 'lastMessageTime',
                sortOrder: 2
            };
        }
    }
    reloadTable.value = false;
    rows.value = event?.rows || lastFilter?.page?.pageSize || 10;
    directionTrigger.value = '';
};

const submitted = ref<boolean>(false);

/**
 * Hides the contact dialog.
 */
const hideDialog = (): void => {
    contactDialog.value = false;
    submitted.value = false;
};

/**
 * Saves a contact to the database.
 */
const saveContact = async (): Promise<void> => {
    submitted.value = true;
    try {
        await contactStore.updateContact(contact?.value?.id as string, contact.value as Contact);
        contactDialog.value = false;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Contact Saved', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${contactStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = contactStore.previousLastVisible;

        await loadLazyData();
    }
};

/**
 * Prepares the contact form for editing.
 * @param prod The contact to be edited.
 */
const editContact = (prod: Contact): void => {
    contactDialog.value = true;
    contact.value = { ...prod, address: prod.address || prod.ai_conversation_summary?.address };
};

const showContact = (prod: Contact): void => {
    contactDetailsDialog.value = true;
    contact.value = { ...prod };
};

/**
 * Opens the confirmation dialog to delete a contact.
 * @param prod The contact to be deleted.
 */
const confirmDeleteContact = (prod: Contact): void => {
    contact.value = prod;
    deleteContactDialog.value = true;
};

/**
 * Deletes a contact from the database.
 */
const deleteContact = async (): Promise<void> => {
    try {
        await contactStore.deleteContact(contact?.value?.id as string);
        deleteContactDialog.value = false;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Contact Deleted', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${contactStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = contactStore.previousLastVisible;
        await loadLazyData();
    }
};

/**
 * Exports the current data view to CSV.
 */
const exportCSV = (): void => {
    dt.value.exportCSV();
};

/**
 * Opens the confirmation dialog to delete selected contacts.
 */
const confirmDeleteSelected = (): void => {
    deleteContactsDialog.value = true;
};

/**
 * Deletes selected contacts from the database.
 */
const deleteSelectedContacts = async (): Promise<void> => {
    try {
        const idsToDelete = selectedContacts.value.map((contact) => contact.id.toString());

        await contactStore.deleteContacts(idsToDelete);
        deleteContactsDialog.value = false;
        selectedContacts.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Contacts Deleted', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${contactStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        await clearFilter();
    }
};

const refreshCurrent = async (): Promise<void> => {
    directionTrigger.value = contactStore.previousLastVisible;
    await loadLazyData();
};

// Update constraints when dateFrom changes
// Function to validate and constrain the dates to a 10-day range
const checkDates = async (trigger: string) => {
    const { dateFrom, dateTo } = filters.value;

    // Ensure dateFrom is not greater than dateTo
    if (trigger === 'dateFrom') {
        if (dateFrom.value && dateTo.value && dateFrom.value > dateTo.value) {
            dateTo.value = getDatePlusMinusDays(dateFrom.value, 30); // Set dateTo to 10 days after dateFrom
        }
    } else {
        // Ensure dateTo is not less than dateFrom
        if (dateTo.value && dateFrom.value && dateTo.value < dateFrom.value) {
            dateFrom.value = getDatePlusMinusDays(dateTo.value, -30); // Set dateFrom to 10 days before dateTo
        }
    }

    // If dateTo is missing, set it to 10 days after dateFrom
    if (!dateTo.value && dateFrom.value) {
        dateTo.value = getDatePlusMinusDays(dateFrom.value, 30);
    }

    // If dateFrom is missing, set it to 10 days before dateTo
    if (!dateFrom.value && dateTo.value) {
        dateFrom.value = getDatePlusMinusDays(dateTo.value, -30);
    }
    await onFilter();
};
</script>

<template>
    <div>
        <div class="card">
            <Toolbar class="mb-6">
                <template #start>
                    <Button
                        v-tooltip="'Delete selected contacts'"
                        :loading="contactStore.loading"
                        label="Delete"
                        icon="pi pi-trash"
                        severity="secondary"
                        @click="confirmDeleteSelected"
                        :disabled="!selectedContacts || !selectedContacts.length || contactStore.loading"
                    />
                </template>

                <template #end>
                    <Button label="Import" icon="pi pi-upload" class="mr-4" severity="secondary" @click="importContactsDialog = true" />
                    <Button label="Export" icon="pi pi-download" severity="secondary" @click="exportCSV($event)" />
                </template>
            </Toolbar>

            <DataTable
                ref="dt"
                v-model:selection="selectedContacts"
                v-model:first="reset"
                v-model:value="contacts"
                dataKey="id"
                :paginator="true"
                v-model:rows="rows"
                v-model:totalRecords="totalRecords"
                :lazy="true"
                :loading="contactStore.loading"
                @page="loadLazyData"
                @sort="loadLazyData"
                filterDisplay="menu"
                :filters="filters"
                :rowsPerPageOptions="[5, 10]"
                paginatorTemplate="FirstPageLink PrevPageLink  NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} contacts"
                sort-mode="single"
            >
                <template #header>
                    <div class="flex flex-wrap gap-2 items-center justify-between">
                        <div class="flex flex-wrap gap-3 items-center justify-center">
                            <h4 class="capitalize">Contacts</h4>
                            <Button type="button" icon="pi pi-filter-slash" label="Clear" outlined @click="clearFilter" />
                            <Button type="button" icon="pi pi-refresh" label="Refresh" outlined @click="refreshCurrent" />
                        </div>
                        <div class="flex flex-wrap gap-3 items-center justify-center">
                            <!-- DatePicker for dateFrom -->
                            <DatePicker :showIcon="true" :showButtonBar="true" v-model="filters['dateFrom'].value" @date-select="checkDates('dateFrom')"></DatePicker>

                            <!-- DatePicker for dateTo -->
                            <DatePicker :showIcon="true" :showButtonBar="true" v-model="filters['dateTo'].value" @date-select="checkDates('dateTo')"></DatePicker>
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search" />
                                </InputIcon>
                                <InputText v-model="filters['global'].value" placeholder="Search..." @input="onFilter" />
                            </IconField>
                        </div>
                    </div>
                </template>

                <Column selectionMode="multiple" style="width: 3rem" :exportable="false"></Column>

                <Column header="Tools" field="conversations_count" sortable align="center" :exportable="false" style="min-width: 10.5rem">
                    <template #header="slotProps"> <i class="pi pi-fw pi-cog !text-2xl"></i> </template>
                    <template #body="slotProps">
                        <div class="flex gap-1">
                            <Button icon="pi pi-eye" v-tooltip="'Contact Details'" placeholder="Top" outlined rounded class="mr-1" @click="showContact(slotProps.data)" />
                        </div>
                    </template>
                </Column>

                <Column field="phone" header="Phone" sortable style="min-width: 12rem"></Column>
                <Column field="name" header="Name" sortable style="min-width: 12rem"></Column>
                <Column field="email" header="Email" sortable style="min-width: 10rem"></Column>
                <Column field="address" header="Address" style="min-width: 10rem">
                    <template #body="{ data }"> {{ data.address || data.ai_conversation_summary.address }} </template>
                </Column>
                <Column field="lastMessageTime" header="Message Date" sortable style="min-width: 10rem">
                    <template #body="{ data }"> {{ getHumanReadableDate(data.lastMessageTime) }} </template>
                </Column>
                <Column header="Actions" :exportable="false" style="min-width: 12rem">
                    <template #body="slotProps">
                        <Button icon="pi pi-pencil" v-tooltip="'Edit'" placeholder="Top" outlined rounded class="mr-2" @click="editContact(slotProps.data)" />
                        <Button icon="pi pi-trash" v-tooltip="'Delete'" placeholder="Top" outlined rounded severity="danger" @click="confirmDeleteContact(slotProps.data)" />
                    </template>
                </Column>
            </DataTable>
        </div>

        <Dialog header="AI Insights" v-model:visible="contactAiDialog" :breakpoints="{ '960px': '75vw' }" :style="{ width: '30vw' }" :modal="true">
            <ParamsIcon icon="pi-sparkles" title="Conversation Summary" :text="contact.ai_conversation_summary?.conversation_summary || contact.ai_conversation_summary?.ai_conversation_summary" />
            <ParamsIcon icon="pi-calendar-plus" title="Follow-up Strategies" :text="contact.ai_conversation_summary?.sales_advice?.follow_up_strategies" />
            <ParamsIcon icon="pi-lightbulb" title="Insights For Conversion" :text="contact.ai_conversation_summary?.sales_advice?.insights_for_conversion" />
            <ParamsIcon icon="pi-bullseye" title="Qualification Strategy" :text="contact.ai_conversation_summary?.sales_advice?.qualification_strategy" />

            <template #footer>
                <Button label="Close" @click="contactAiDialog = false" />
            </template>
        </Dialog>

        <Dialog header="Contact Details" v-model:visible="contactDetailsDialog" :breakpoints="{ '960px': '75vw' }" :style="{ width: '30vw' }" :modal="true">
            <ParamsIcon icon="pi-user" title="Contact" :text="[contact]" />
            <ParamsIcon icon="pi-ellipsis-h" title="More Details..." :text="[contact.contact_details]" />
            <template #footer>
                <Button label="Close" @click="contactDetailsDialog = false" />
            </template>
        </Dialog>

        <Dialog v-model:visible="contactDialog" :style="{ width: '450px' }" header="Contact Details" :modal="true" :closable="!contactStore.loading">
            <div class="flex flex-col gap-6">
                <Message v-if="contactStore.error && contactStore.error?.message" class="flex justify-center" icon="pi pi-exclamation-circle" severity="error" size="small" variant="simple">
                    <span>{{ contactStore.error.message }}</span>
                </Message>
            </div>
            <div class="flex flex-col gap-6">
                <!--
<div>
    <label for="name" class="block font-bold mb-3">Name</label>
    <IconField>
        <InputText @input="validateNameField" id="name" v-model.trim="contact.name" autofocus :invalid="contactErrors.name.length > 0" fluid />
        <InputIcon v-if="contactErrors.name" class="pi pi-info-circle error" />
    </IconField>
    <small v-if="contactErrors.name" class="text-red-500">{{ contactErrors.name }}</small>
</div>

<div>
    <label for="email" class="block font-bold mb-3">Email</label>
    <IconField>
        <InputText @input="validateEmailField" id="email" v-model.trim="contact.email" autofocus :invalid="contactErrors.email.length > 0" fluid />
        <InputIcon v-if="contactErrors.email" class="pi pi-info-circle error" />
    </IconField>
    <small v-if="contactErrors.email" class="text-red-500">{{ contactErrors.email }}</small>
</div>
-->
                <div>
                    <label for="name" class="block font-bold mb-3">Name</label>
                    <IconField>
                        <InputText id="name" v-model.trim="contact.name" autofocus fluid />
                    </IconField>
                </div>
                <div>
                    <label for="email" class="block font-bold mb-3">Email</label>
                    <IconField>
                        <InputText id="email" v-model.trim="contact.email" autofocus fluid />
                    </IconField>
                </div>
                <div>
                    <label for="email" class="block font-bold mb-3">Phone</label>
                    <IconField>
                        <InputText id="phone" v-model.trim="contact.phone" autofocus fluid />
                    </IconField>
                </div>
                <div>
                    <label for="address" class="block font-bold mb-3">Address</label>
                    <IconField>
                        <InputText id="address" v-model.trim="contact.address" autofocus fluid />
                    </IconField>
                </div>
            </div>

            <template #footer>
                <Button label="Cancel" icon="pi pi-times" text @click="hideDialog" :disabled="contactStore.loading" />
                <!--
                <Button :disabled="invalidSignUp" :loading="contactStore.loading" label="Save" icon="pi pi-check" @click="saveContact" />
                -->
                <Button :disabled="contactStore.loading" :loading="contactStore.loading" label="Save" icon="pi pi-check" @click="saveContact" />
            </template>
        </Dialog>

        <Dialog v-model:visible="deleteContactDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!contactStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span v-if="contact"
                    >Are you sure you want to delete <b>{{ contact.name }}</b
                    >?</span
                >
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteContactDialog = false" :disabled="contactStore.loading" />
                <Button label="Yes" icon="pi pi-check" @click="deleteContact" :disabled="contactStore.loading" :loading="contactStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="deleteContactsDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!contactStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to delete the selected contacts?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteContactsDialog = false" :disabled="contactStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="deleteSelectedContacts" :loading="contactStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="importContactsDialog" :style="{ width: '450px' }" header="Import Contacts from Excel File" :modal="true" :closable="!contactStore.loading">
            <div>
                <ImportContact />
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="importContactsDialog = false" :disabled="contactStore.loading" />
            </template>
        </Dialog>
    </div>
</template>
