<script setup lang="ts">
import { getHumanReadableDate } from '@/entities/lead';

const props = defineProps<{
    properties: Record<string, any>[];
}>();
const keysToRemove = [
    'queueTime',
    'apiVersion',
    'uri',
    'toFormatted',
    'fromFormatted',
    'parentCallSid',
    'accountSid',
    'phoneNumberSid',
    'Body',
    'SmsMessageSid',
    'NumMedia',
    'ReferralNumMedia',
    'AccountSid',
    'SmsSid',
    'ApiVersion',
    'SmsStatus',
    'MessageSid',
    'MessageType',
    'NumSegments',
    'text',
    'mark_for_deletion',
    'conversations_count',
    'channel'
];
const dateKeys = ['lastMessageTime'];
// Ensure props.properties is always an array
const safeProperties = Array.isArray(props.properties) ? props.properties : [];

// Preprocess the properties to filter out non-string keys
const filteredProperties = safeProperties.map((property) => {
    const filtered = {};
    for (const [key, value] of Object.entries(property)) {
        if (typeof value !== 'object' && value && !keysToRemove.includes(key) && isNaN(Number(key))) {
            let arrKey = key;
            if (key === 'lastMessageTime') arrKey = 'Last_Interaction';
            filtered[arrKey] = dateKeys.includes(key) ? getHumanReadableDate(value) : value;
        }
    }
    return filtered;
});
</script>

<template>
    <div v-for="(property, index) in filteredProperties" :key="index">
        <!-- Loop through each property object -->
        <div v-for="(value, key) in property" :key="key" class="flex items-center py-2 border-surface-300 dark:border-surface-500 border-b">
            <span class="flex justify-center items-center">
                <strong class="lg:text-sm capitalize">{{ key.replace(/_/g, ' ') }}:</strong>
            </span>
            <span class="ml-6 flex flex-col break-all">
                <span class="text-surface-600 dark:text-surface-200 lg:text-sm capitalize" style="word-break: break-word" v-html="value" />
            </span>
        </div>
    </div>
</template>

<style scoped lang="scss"></style>
