<script setup lang="ts">
import { ref } from 'vue';
import * as XLSX from 'xlsx';
import { useLeadsStore } from '@/entities/lead';

const fileInput = ref<HTMLInputElement | null>(null);
const isLoading = ref(false);
const errorMessage = ref<string | null>(null);

const leadsStore = useLeadsStore();

const handleFileUpload = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (!target.files || target.files.length === 0) return;

    errorMessage.value = null;
    isLoading.value = true;

    try {
        const file = target.files[0];
        const data = await readFileAsArrayBuffer(file);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        await leadsStore.importLeadsFromExcelFile(jsonData);
    } catch (err: any) {
        errorMessage.value = err.message || 'An error occurred while processing the file';
    } finally {
        isLoading.value = false;
    }
};

const onUpload = async (target: Event): Promise<ArrayBuffer> => {
    if (!target.files || target.files.length === 0) return;
    try {
        const file = target.files[0];
        console.log('----');

        const data = await readFileAsArrayBuffer(file);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        console.log(jsonData);
        // await leadsStore.importLeadsFromExcelFile(jsonData);
    } catch (err: any) {
        errorMessage.value = err.message || 'An error occurred while processing the file';
    } finally {
        isLoading.value = false;
    }
};
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as ArrayBuffer);
        reader.onerror = () => reject(reader.error);
        reader.readAsArrayBuffer(file);
    });
};
</script>

<template>
    <FileUpload name="leads" @uploader="onUpload($event)" :multiple="false" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" :maxFileSize="10000000" customUpload />
</template>

<style scoped lang="scss"></style>
