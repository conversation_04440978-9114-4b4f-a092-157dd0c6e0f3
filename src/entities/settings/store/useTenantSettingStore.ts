import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getTenantSettingFromFirestore } from '../services/firestoreService';
import { Lead, renderMarkdown } from '@/entities/lead';

export const useTenantSettingStore = defineStore('tenantsetting', () => {
    const loading = ref(false);
    const error = ref<string | null>(null);
    const conversationSetting = ref<any | null>(null);

    const getConversationSettings = async () => {
        loading.value = true;
        try {
            const data = await getTenantSettingFromFirestore();
            conversationSetting.value = data;

            return data;
        } catch (err) {
            handleError(err, 'Failed to load conversation details');
            return null;
        } finally {
            loading.value = false;
        }
    };

    const getCallScript = async (selectedScript: 'inquired' | 'booked' | 'quoted', selectedVoice: string, lead: Lead) => {
        loading.value = true;
        try {
            const data = conversationSetting.value ?? (await getTenantSettingFromFirestore());
            const scriptString = data.scripts[selectedScript];
            if (!scriptString) {
                throw new Error('Script could not be found!');
            }

            const srciptParams = await buildCallScriptJSONParams(selectedScript, lead);
            const markedConversations = srciptParams?.parsed_conversations?.map((convo) => ({
                message: convo.message.replace(/<[^>]*>/g, '').replace(/[_*~`>#-]/g, '')
            }));

            const customer_name = lead.name ?? '';
            const agent_name = selectedVoice ?? '';
            const jsonContent = JSON.stringify(markedConversations || {}, null, 2);

            const jsonMarkdown: string = `
\`\`\`
\n
\n
${jsonContent}
\n
\`\`\``;

            const script = scriptString
                .replace(/\[customer_name]/gi, customer_name)
                .replace(/\[agent_name]/gi, agent_name)
                .replace(/\[summary_of_parsed_conversations]/gi, jsonMarkdown)
                .replace(/\[filtered_appointment_dates]/gi, srciptParams?.filtered_appointment_dates ?? '')
                .replace(/\[quote_details_doorType]/gi, srciptParams?.quote_details?.door_size ?? '')
                .replace(/\[quote_details_colour\]/gi, srciptParams?.quote_details?.[`${srciptParams?.quote_details?.door_size?.toLowerCase?.()}_door_colour`].split('|')[0] ?? '')
                .replace(/\[quote_details_amount]/gi, srciptParams?.quote_details?.total_calc ?? '')
                .replace(/\[booking_details_date]/gi, srciptParams?.booking_details_date ?? '')
                .replace(/\[booking_details_address]/gi, srciptParams?.booking_details_address ?? '');

            return script;
        } catch (err) {
            handleError(err, 'Failed to load conversation details');
            return null;
        } finally {
            loading.value = false;
        }
    };

    const buildCallScriptJSONParams = async (selectedScript: 'inquired' | 'booked' | 'quoted', lead: Lead) => {
        loading.value = true;
        try {
            const data = conversationSetting.value ?? (await getTenantSettingFromFirestore());
            const groupedDates = data?.call?.availableAppointmentDates.reduce((acc: any, dateStr: string) => {
                const date = new Date(dateStr as string);
                const monthKey = date.toLocaleString('en-GB', { month: 'long', year: 'numeric' });
                if (!acc[monthKey]) acc[monthKey] = [];
                acc[monthKey].push(date.getDate());
                return acc;
            }, {});

            const formattedDates = Object.entries(groupedDates)
                .map(([month, days]) => {
                    // Explicitly type days as number[]
                    const sortedDays = [...new Set(days as number[])].sort((a, b) => a - b);
                    let range = [],
                        i = 0;
                    while (i < sortedDays.length) {
                        let start = sortedDays[i];
                        while (sortedDays[i + 1] === sortedDays[i] + 1) i++;
                        range.push(start === sortedDays[i] ? start : `${start}–${sortedDays[i]}`);
                        i++;
                    }
                    return `- **${month}**: ${range.join(', ')}`;
                })
                .join('\n');

            const customer_details = {
                customer_name: lead.name ?? '',
                phone: lead.phone ?? '',
                email: lead.email ?? '',
                address: lead.address ?? ''
            };

            switch (selectedScript) {
                case 'inquired':
                    return {
                        lead_details: customer_details,
                        parsed_conversations: lead.conversations ?? [],
                        filtered_appointment_dates: formattedDates ?? []
                    };
                case 'booked':
                    const currentTime = Math.floor(Date.now() / 1000);
                    const getLatestAppointment = Array.isArray(lead?.appointmentHistory)
                        ? (lead?.appointmentHistory?.filter((app: any) => app.appointmentDate >= currentTime).sort((a: any, b: any) => a.appointmentDate - b.appointmentDate)[0] ?? null)
                        : null;
                    const formattedDateString = getLatestAppointment?.appointmentDate
                        ? new Date(getLatestAppointment?.appointmentDate * 1000)
                              .toLocaleString('en-US', {
                                  weekday: 'long',
                                  month: 'long',
                                  day: 'numeric',
                                  hour: 'numeric',
                                  minute: '2-digit',
                                  hour12: true
                              })
                              .replace(/:\d{2}\s/, ' ') // Remove minutes if you want just the hour and AM/PM
                              .replace(',', ' at')
                        : '';
                    return {
                        lead_details: customer_details,
                        booking_details_date: formattedDateString ?? '',
                        booking_details_address: getLatestAppointment?.location_string ?? lead.address ?? '',
                        parsed_conversations: lead.conversations ?? [],
                        filtered_appointment_dates: formattedDates ?? []
                    };
                case 'quoted':
                default:
                    return {
                        lead_details: customer_details,
                        quote_details: lead.lead_details ?? {},
                        parsed_conversations: lead.conversations ?? [],
                        filtered_appointment_dates: formattedDates ?? []
                    };
            }
        } catch (err) {
            handleError(err, 'Failed to build json scripts');
            return null;
        } finally {
            loading.value = false;
        }
    };

    const getWhatsAppTemplate = async (customer_name: string) => {
        loading.value = true;
        try {
            const data = conversationSetting.value ?? (await getTenantSettingFromFirestore());
            let scriptString = data.whatsapp.templateContent;
            if (!scriptString) {
                scriptString =
                    "\nThanks for reaching out to us about our electric roller garage doors. At Liftt, we pride ourselves on offering premium features like smart app control and a wide range of customisation options to perfectly match your home.\n\nTo get started, let's lock in your free survey today. It only takes a couple of minutes to book your preferred time. Just click https://crm.liftt.co.uk/book-appointment to secure your slot.\nCould you please provide your full name, email address, and installation address? This will ensure we tailor everything to your needs.\n\nFeel free to explore more about our offerings at https://liftt.co.uk/electric-garage-doors/.\n\nIf you have any questions or need assistance, just reply here or give us a call at +441454801444. Ask for Jamie Hancock when you do!\n\nLooking forward to helping you enhance your home! 🚪✨";
            }

            const script = `Hi ${customer_name || 'there'}! 😊 ${scriptString}`;
            return script;
        } catch (err) {
            handleError(err, 'Failed to load whatsapp template details');
            return null;
        } finally {
            loading.value = false;
        }
    };
    // Helper
    const handleError = (err: unknown, defaultMessage: string) => {
        console.error(err);
        error.value = defaultMessage;
    };

    return {
        // State
        loading,
        error,
        conversationSetting,

        // Actions
        getWhatsAppTemplate,
        getConversationSettings,
        buildCallScriptJSONParams,
        getCallScript
    };
});
