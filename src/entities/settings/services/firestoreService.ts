import { collection, doc, getDoc, query, orderBy, where } from 'firebase/firestore';
import { firestore } from '@/firebase';
import { SettingsFilters } from '../types';
import { getUserLoggedInData } from '@/shared';

let SETTINGS_COLLECTION = 'companies';

export const getTenantSettingFromFirestore = async (): Promise<any | null> => {
    const user = await getUserLoggedInData();

    const docSnap = await getDoc(doc(firestore, SETTINGS_COLLECTION, user?.tenantId));
    if (!docSnap.exists()) return null;

    return docSnap.data();
};

export const generateFirestoreConstraints = async (filters: SettingsFilters = {}, removeSort: boolean = false) => {
    const user = await getUserLoggedInData();

    let q = query(collection(firestore, SETTINGS_COLLECTION));

    // Text Search
    if (filters.searchTerm) {
        q = query(q, where('keywords', 'array-contains', filters.searchTerm));
    }

    if (filters.searchTerms && filters.searchTerms.length > 0) {
        q = query(q, where('keywords', 'array-contains-any', filters.searchTerms));
    }

    // Date Range
    if (filters.dateRange?.appointmentDateStart !== undefined) {
        q = query(q, where('appointmentDate', '>=', filters.dateRange.appointmentDateStart));
    }

    if (filters.dateRange?.appointmentDateEnd !== undefined) {
        q = query(q, where('appointmentDate', '<=', filters.dateRange.appointmentDateEnd));
    }

    // Door Type
    if (filters.doorType && filters.doorType.length > 0) {
        q = query(q, where('doorType', 'in', filters.doorType));
    }

    // Trade-In Filter
    if (typeof filters.tradeIn === 'boolean') {
        q = query(q, where('tradeIn', '==', filters.tradeIn));
    }

    // Sorting
    if (!removeSort && filters.sort?.col && filters.sort.order) {
        q = query(q, orderBy(filters.sort.col, filters.sort.order));
    }

    return q;
};
