import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Tenant } from '../model';
import { createTenantAPI, deleteTenantAPI, fetchTenantsAPI, updateTenantAPI, generateFirestoreConstraints, fetchTenantAPI } from '../api';
import type { QueryConstraint } from 'firebase/firestore';
import { doc, onSnapshot } from 'firebase/firestore';
import { firestore } from '@/firebase';

export const useTenantStore = defineStore('tenant', () => {
    // State
    const tenants = ref<Tenant[]>([]);
    const isLoading = ref(false);
    const error = ref<string | null>(null);
    const totalRecords = ref(0);

    // Email validation subscription state
    const emailValidationSubscriptions = ref<Map<string, () => void>>(new Map());

    // General tenant subscription state
    const tenantSubscriptions = ref<Map<string, () => void>>(new Map());

    // Options for pagination, sorting, and filtering
    const options = ref({
        search: '',
        sortField: 'createdAt',
        sortOrder: -1, // 'desc'
        rows: 10, // Page size
        first: 0 // Starting index
    });

    // Computed value for Firestore query constraints
    const firestoreConstraints = computed(() => {
        return generateFirestoreConstraints(options.value);
    });

    // Actions
    const fetchTenants = async () => {
        isLoading.value = true;
        error.value = null;
        try {
            const fetchedTenants = await fetchTenantsAPI(firestoreConstraints.value as QueryConstraint[]);
            tenants.value = fetchedTenants;
            // Note: Firestore SDK does not directly return total count with a query.
            // A separate query would be needed for the total count, which can be costly.
            // For simplicity, we'll use the length of the fetched array.
            totalRecords.value = fetchedTenants.length;
        } catch (e: any) {
            error.value = e.message;
        } finally {
            isLoading.value = false;
        }
    };

    const getTenant = async (id: string) => {
        isLoading.value = true;
        error.value = null;
        try {
            const tenant = await fetchTenantAPI(id);

            return tenant;
        } catch (e: any) {
            console.error('Error fetching tenant:', e);
            error.value = e.message;
            return null;
        } finally {
            isLoading.value = false;
        }
    };

    const createTenant = async (tenantData: Omit<Tenant, 'id'>): Promise<string | undefined> => {
        try {
            const newTenant = await createTenantAPI(tenantData);
            await fetchTenants(); // Refresh list
            return newTenant.id;
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    const updateTenant = async (id: string, tenantData: Partial<Tenant>) => {
        isLoading.value = true;
        try {
            await updateTenantAPI(id, tenantData);
            await fetchTenants(); // Refresh list
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    const deleteTenant = async (id: string) => {
        isLoading.value = true;
        try {
            await deleteTenantAPI(id);
            await fetchTenants(); // Refresh list
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    const approveTenant = async (id: string) => {
        isLoading.value = true;
        try {
            await updateTenantAPI(id, { approved: true });
            await fetchTenants(); // Refresh list
        } catch (e: any) {
            error.value = e.message;
            throw e;
        } finally {
            isLoading.value = false;
        }
    };

    /**
     * Subscribe to tenant email validation changes
     * @param tenantId - The tenant ID to subscribe to
     * @param onValidationChange - Callback function called when email.validated changes
     * @returns Unsubscribe function
     */
    const subscribeToEmailValidation = (
        tenantId: string,
        onValidationChange: (validated: boolean, tenant: Tenant) => void
    ): (() => void) => {
        // Clean up any existing subscription for this tenant
        unsubscribeFromEmailValidation(tenantId);

        const tenantDocRef = doc(firestore, 'companies', tenantId);

        const unsubscribe = onSnapshot(tenantDocRef, (doc) => {
            if (doc.exists()) {
                const tenantData = { id: doc.id, ...doc.data() } as Tenant;
                const isValidated = tenantData.email?.validated || false;

                // Call the callback with the validation status and full tenant data
                onValidationChange(isValidated, tenantData);
            }
        }, (error) => {
            console.error('Error in email validation subscription:', error);
        });

        // Store the unsubscribe function
        emailValidationSubscriptions.value.set(tenantId, unsubscribe);

        return unsubscribe;
    };

    /**
     * Unsubscribe from tenant email validation changes
     * @param tenantId - The tenant ID to unsubscribe from
     */
    const unsubscribeFromEmailValidation = (tenantId: string): void => {
        const unsubscribe = emailValidationSubscriptions.value.get(tenantId);
        if (unsubscribe) {
            unsubscribe();
            emailValidationSubscriptions.value.delete(tenantId);
        }
    };

    /**
     * Unsubscribe from all email validation subscriptions
     */
    const unsubscribeFromAllEmailValidations = (): void => {
        emailValidationSubscriptions.value.forEach((unsubscribe) => {
            unsubscribe();
        });
        emailValidationSubscriptions.value.clear();
    };

    /**
     * Subscribe to tenant changes
     * @param tenantId - The tenant ID to subscribe to
     * @param onTenantChange - Callback function called when tenant data changes
     * @returns Unsubscribe function
     */
    const subscribeToTenantChanges = (
        tenantId: string,
        onTenantChange: (tenant: Tenant | null) => void
    ): (() => void) => {
        // Clean up any existing subscription for this tenant
        unsubscribeFromTenantChanges(tenantId);

        const tenantDocRef = doc(firestore, 'companies', tenantId);

        const unsubscribe = onSnapshot(tenantDocRef, (doc) => {
            if (doc.exists()) {
                const tenantData = { id: doc.id, ...doc.data() } as Tenant;
                onTenantChange(tenantData);
            } else {
                onTenantChange(null);
            }
        }, (error) => {
            console.error('Error in tenant subscription:', error);
            onTenantChange(null);
        });

        // Store the unsubscribe function
        tenantSubscriptions.value.set(tenantId, unsubscribe);

        return unsubscribe;
    };

    /**
     * Unsubscribe from tenant changes
     * @param tenantId - The tenant ID to unsubscribe from
     */
    const unsubscribeFromTenantChanges = (tenantId: string): void => {
        const unsubscribe = tenantSubscriptions.value.get(tenantId);
        if (unsubscribe) {
            unsubscribe();
            tenantSubscriptions.value.delete(tenantId);
        }
    };

    /**
     * Unsubscribe from all tenant subscriptions
     */
    const unsubscribeFromAllTenantSubscriptions = (): void => {
        tenantSubscriptions.value.forEach((unsubscribe) => {
            unsubscribe();
        });
        tenantSubscriptions.value.clear();
    };

    // Function to update options and refetch tenants
    const setOptions = (newOptions: Partial<typeof options.value>) => {
        options.value = { ...options.value, ...newOptions };
        fetchTenants();
    };

    return {
        tenants,
        isLoading,
        error,
        totalRecords,
        options,
        fetchTenants,
        getTenant,
        createTenant,
        updateTenant,
        deleteTenant,
        setOptions,
        approveTenant,
        subscribeToEmailValidation,
        unsubscribeFromEmailValidation,
        unsubscribeFromAllEmailValidations,
        subscribeToTenantChanges,
        unsubscribeFromTenantChanges,
        unsubscribeFromAllTenantSubscriptions
    };
});
