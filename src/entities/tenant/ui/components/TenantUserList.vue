<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useUsersStore } from '@/entities/user';
import type { User } from '@/entities/user';
import { useToast } from 'primevue/usetoast';
import { storeToRefs } from 'pinia';
import TenantUserForm from './TenantUserForm.vue';

const props = defineProps<{ tenantId: string }>();

const toast = useToast();
const userStore = useUsersStore();
const { users, loading: isLoading } = storeToRefs(userStore);

const userDialog = ref(false);
const deleteUserDialog = ref(false);
const user = ref<Partial<User>>({});

const fetchUsers = () => {
    userStore.fetchUsers(100, { tenantId: props.tenantId }); // Fetch all users for the tenant
};

onMounted(() => {
    fetchUsers();
});

watch(() => props.tenantId, () => {
    fetchUsers();
});

const openNew = () => {
    user.value = {};
    userDialog.value = true;
};

const editUser = (prod: User) => {
    user.value = { ...prod };
    userDialog.value = true;
};

const confirmDeleteUser = (prod: User) => {
    user.value = prod;
    deleteUserDialog.value = true;
};

const deleteUser = async () => {
    if (user.value.id) {
        await userStore.deleteUser(user.value.id);
        deleteUserDialog.value = false;
        user.value = {};
        toast.add({ severity: 'success', summary: 'Successful', detail: 'User Deleted', life: 3000 });
    }
};

const saveUser = async (userData: User) => {
    if (userData.id) {
        await userStore.updateUser(userData.id, userData);
        toast.add({ severity: 'success', summary: 'Successful', detail: 'User Updated', life: 3000 });
    } else {
        await userStore.addUser({ ...userData, tenantId: props.tenantId });
        toast.add({ severity: 'success', summary: 'Successful', detail: 'User Created', life: 3000 });
    }
    userDialog.value = false;
    user.value = {};
};

const getRoleLabel = (role: string) => {
    const roleMap: { [key: string]: string } = {
        tenant_admin: 'Admin',
        tenant_sales: 'Sales Representative'
    };
    return roleMap[role] || role;
};
</script>

<template>
    <div class="card">
        <Toolbar class="mb-4">
            <template #start>
                <Button label="New User" icon="pi pi-plus" severity="success" @click="openNew" />
            </template>
        </Toolbar>

        <DataTable :value="users" :loading="isLoading" dataKey="id">
            <template #header>
                <div class="flex justify-between items-center">
                    <h5 class="m-0">Manage Users</h5>
                </div>
            </template>

            <Column field="name" header="Name" :sortable="true"></Column>
            <Column field="email" header="Email" :sortable="true"></Column>
            <Column field="role" header="Role" :sortable="true">
                 <template #body="slotProps">
                    <span>{{ getRoleLabel(slotProps.data.role) }}</span>
                </template>
            </Column>
            <Column headerStyle="min-width:10rem;">
                <template #body="slotProps">
                    <Button icon="pi pi-pencil" v-tooltip.top="'Edit'" class="mr-2" severity="info" rounded @click="editUser(slotProps.data)"></Button>
                    <Button icon="pi pi-trash" v-tooltip.top="'Delete'" severity="danger" rounded @click="confirmDeleteUser(slotProps.data)"></Button>
                </template>
            </Column>
        </DataTable>

        <TenantUserForm :visible="userDialog" :user="user" @update:visible="userDialog = $event" @save="saveUser" />

        <Dialog v-model:visible="deleteUserDialog" :style="{ width: '450px' }" header="Confirm" :modal="true">
            <div class="flex items-center">
                <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
                <span v-if="user">Are you sure you want to delete <b>{{ user.name }}</b>?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteUserDialog = false"></Button>
                <Button label="Yes" icon="pi pi-check" text @click="deleteUser"></Button>
            </template>
        </Dialog>
    </div>
</template>
