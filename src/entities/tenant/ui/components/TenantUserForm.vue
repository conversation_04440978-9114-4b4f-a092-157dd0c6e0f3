<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import type { User } from '@/entities/user';
import { validateEmail, validateRequired } from '@/utils';
import { CompanyUserUpdate } from '@/features/company-user-update';
import { CompanyUserService } from '@/features/company-user-update/services/companyUserService';
// Component props
const props = defineProps<{
    visible: boolean;
    user: Partial<User>;
    enhanced?: boolean; // Enable enhanced admin user fields
}>();

// Component emits
const emits = defineEmits(['update:visible', 'save']);

// Local state
const internalUser = ref<Partial<User>>({});
const submitted = ref(false);
const errors = ref({
    name: '',
    email: ''
});

const roleOptions = ref([
    { label: 'Admin', value: 'tenant_admin' },
    { label: 'Sales Representative', value: 'tenant_sales' }
]);

// Watch for changes in the user prop to update the local state
watch(
    () => props.user,
    (newVal) => {
        internalUser.value = { ...newVal };
        submitted.value = false; // Reset submission state on new user
    },
    { deep: true, immediate: true }
);

// Validation functions
const validateForm = () => {
    errors.value.name = validateRequired(internalUser.value.name || '');
    errors.value.email = validateEmail(internalUser.value.email || '');
    return !errors.value.name && !errors.value.email;
};

// Handle form submission for simple mode
const saveUser = () => {
    submitted.value = true;
    if (validateForm()) {
        emits('save', internalUser.value);
    }
};

// Reference to CompanyUserUpdate component
const companyUserUpdateRef = ref();

// Handle form submission for enhanced mode
const saveEnhancedUser = async () => {
    if (companyUserUpdateRef.value) {
        // Trigger save on the CompanyUserUpdate component
        await companyUserUpdateRef.value.saveAdminInfo();
    }
};

// Close the dialog
const closeDialog = () => {
    emits('update:visible', false);
};
</script>

<template>
    <Dialog :visible="visible" @update:visible="closeDialog" :style="{ width: enhanced ? '1000px' : '450px' }" header="User Details" :modal="true" class="p-fluid">
        <!-- Simple Form (Original) -->
        <div v-if="!enhanced" class="flex flex-col gap-6">
            <div class="field">
                <label for="name" class="block font-bold mb-3">Name</label>
                <IconField>
                    <InputIcon class="pi pi-user" />
                    <InputText id="name" v-model.trim="internalUser.name" required :class="{ 'p-invalid': errors.name && submitted }" autofocus fluid />
                </IconField>
                <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
            </div>

            <div class="field">
                <label for="email" class="block font-bold mb-3">Email</label>
                <IconField>
                    <InputIcon class="pi pi-envelope" />
                    <InputText id="email" v-model.trim="internalUser.email" required :class="{ 'p-invalid': errors.email && submitted }" fluid />
                </IconField>
                <small class="p-error" v-if="errors.email && submitted">{{ errors.email }}</small>
            </div>

            <div class="field">
                <label for="role" class="block font-bold mb-3">Role</label>
                <Dropdown id="role" v-model="internalUser.role" :options="roleOptions" optionLabel="label" optionValue="value" placeholder="Select a Role" fluid></Dropdown>
            </div>
        </div>

        <!-- Enhanced Form using CompanyUserUpdate component -->
        <div v-else class="enhanced-user-form">
            <CompanyUserUpdate ref="companyUserUpdateRef" :user-id="internalUser.id" :show-header="false" :show-save-button="false" @save="(data) => emits('save', CompanyUserService.convertToUserData(data))" />
        </div>

        <template #footer>
            <Button label="Cancel" icon="pi pi-times" text @click="closeDialog"></Button>
            <Button label="Save" icon="pi pi-check" @click="enhanced ? saveEnhancedUser() : saveUser()" />
        </template>
    </Dialog>
</template>

<style scoped>
.p-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
}

.enhanced-user-form :deep(.company-admin-form) {
    /* Adjust styling for dialog context */
    max-width: none;
    margin: 0;
}
</style>
