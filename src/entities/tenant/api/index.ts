import { collection, addDoc, getDocs, updateDoc, deleteDoc, doc, query, where, getDoc, QueryConstraint, orderBy, startAt, endAt, limit, startAfter } from 'firebase/firestore';
import { firestore } from '@/firebase';
import type { Tenant } from '../model/types';

const tenantsCollection = collection(firestore, 'companies');

// Generate Firestore constraints for querying tenants
export const generateFirestoreConstraints = (options: any): QueryConstraint[] => {
    const constraints: QueryConstraint[] = [];
    if (options.search) {
        constraints.push(orderBy('name'));
        constraints.push(startAt(options.search));
        constraints.push(endAt(options.search + '\uf8ff'));
    }
    if (options.sortField && options.sortOrder) {
        constraints.push(orderBy(options.sortField, options.sortOrder === 1 ? 'asc' : 'desc'));
    }
    if (options.rows) {
        constraints.push(limit(options.rows));
    }
    if (options.first) {
        // This is a simplified pagination. For cursor-based pagination, you'd pass the last document snapshot.
        // constraints.push(startAfter(options.first));
    }
    return constraints;
};

// Fetch tenants from Firestore
export const fetchTenantsAPI = async (constraints: QueryConstraint[] = []) => {
    const q = query(tenantsCollection, ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as Tenant);
};

// Fetch a single tenant by ID
export const fetchTenantAPI = async (id: string) => {
    const tenantDoc = await getDoc(doc(tenantsCollection, id));

    if (!tenantDoc.exists()) {
        throw new Error('Tenant not found');
    }
    return { id: tenantDoc.id, ...tenantDoc.data() } as Tenant;
};

// Create a new tenant in Firestore
export const createTenantAPI = async (tenant: Omit<Tenant, 'id'>) => {
    const docRef = await addDoc(tenantsCollection, tenant);
    return { ...tenant, id: docRef.id };
};

// Update a tenant in Firestore
export const updateTenantAPI = async (id: string, tenant: Partial<Tenant>) => {
    await updateDoc(doc(tenantsCollection, id), tenant);
    return { ...tenant, id };
};

// Delete a tenant from Firestore
export const deleteTenantAPI = async (id: string) => {
    await deleteDoc(doc(tenantsCollection, id));
};
