<script setup lang="ts">
import { FilterMatchMode } from '@primevue/core/api';
import { useToast } from 'primevue/usetoast';
import { computed, onMounted, onUnmounted, Ref, ref, watch } from 'vue';
import {
    Lead,
    LeadFilters,
    LeadConversations,
    LeadAIInsigths,
    LeadMobileInfo,
    Notes,
    LeadTools,
    useLeadsStore,
    getHumanReadableDate,
    getDatePlusMinusDays,
    leadActionsArray,
    ImportLead,
    LeadDetails,
    LeadEdit,
    LeadInfo,
    LeadStatusView,
    LeadMobileExpander,
    LeadPopOverDetails,
    CountdownTimer,
    LeadBooking,
    followUpParams
} from '@/entities/lead';
import { validateEmail, validateRequired } from '@/utils';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const leadCategory: Ref<string> = ref(route.params.id);

const leadStore = useLeadsStore();
const leads = ref<Lead[]>([]);
const totalRecords = ref<number>(0);
const currentPage = ref<number>(0);

const props = defineProps<{
    combinedLead?: string[];
}>();

const expandedRows = ref({});
const isMobile = ref(window.matchMedia('(max-width: 960px)').matches);

// Update isMobile value when the screen size changes
const updateIsMobile = () => {
    isMobile.value = window.matchMedia('(max-width: 960px)').matches;
};

onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
});

onMounted(async () => {
    if (!leadActionsArray.includes(leadCategory.value)) {
        await router.push({ name: 'notfound-auth' });
    }
    window.addEventListener('resize', updateIsMobile);
    // await leadStore.syncLeads();
    await loadLazyData();
});

const isEmbeddedList = computed(() => {
    return !!(props?.combinedLead && props?.combinedLead?.length);
});
const toast = useToast();
const reset = ref<number>(0);
const followUpMessage = ref<string>('');
const reloadTable = ref<boolean>(false);
const dt = ref<any>();
const leadDialog = ref<boolean>(false);
const leadAiDialog = ref<boolean>(false);
const leadConversationsDialog = ref<boolean>(false);
const leadBookingDetailDialog = ref<boolean>(false);
const leadAddBookingDialog = ref<boolean>(false);
const leadNotesDialog = ref<boolean>(false);
const leadDetailsDialog = ref<boolean>(false);
const deleteLeadDialog = ref<boolean>(false);
const deleteLeadsDialog = ref<boolean>(false);
const analyzeLeadsDialog = ref<boolean>(false);
const markAsOutOfScopeDialog = ref<boolean>(false);
const markAsQualifiedDialog = ref<boolean>(false);
const importLeadsDialog = ref<boolean>(false);
const markForDeletion = ref<boolean>(false);
const lead = ref<Lead | null>(null);
const rows = ref<number>(10);
const directionTrigger = ref<string>('');
const leadErrors = ref<{ email: string; name: string }>({
    email: '',
    name: ''
});
const selectedLeads = ref<Lead[] | null>(null);
const filters = ref();
const defaultSort = computed(() => (leadCategory.value === 'booked' && !isEmbeddedList.value ? 'latestAppointmentDate' : 'lastMessageTime'));
const isQualified = computed(() => {
    return !['qualified', 'unqualified', 'list', 'irrelevant'].includes(leadCategory?.value) ? 'none' : leadCategory?.value !== 'unqualified';
});

const sortbackup = ref({
    sortField: defaultSort.value,
    sortOrder: 2
});

const initFilters = () => {
    selectedLeads.value = null;
    totalRecords.value = 0;
    rows.value = 10;
    reset.value = 0;
    leads.value = [];
    filters.value = {
        lastFilter: {},
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        leadAction: { value: null, matchMode: FilterMatchMode.IN },
        leadSource: { value: null, matchMode: FilterMatchMode.IN },
        leadStatus: { value: null, matchMode: FilterMatchMode.IN },
        dateFrom: { value: null, matchMode: FilterMatchMode.EQUALS },
        dateTo: { value: null, matchMode: FilterMatchMode.EQUALS },
        isQualified: { value: isQualified.value, matchMode: FilterMatchMode.EQUALS }
    };
};
initFilters();
const clearFilter = async () => {
    initFilters();
    reloadTable.value = true;
    directionTrigger.value = '';
    await loadLazyData();
};
const invalidSignUp = computed(() => {
    return validateRequired(lead?.value?.email as string).length > 0 || validateEmail(lead?.value?.email as string).length > 0;
});

// Watch for changes in leadCategory
watch(
    () => route.params.id,
    async (newId) => {
        if (leadActionsArray.includes(newId)) {
            leadCategory.value = newId;
            await clearFilter();
        } else {
            router.push({ name: 'notfound-auth' });
        }
    }
);

// Reset followUpMessage when lead changes
watch(
    () => lead.value,
    (newLead) => {
        followUpMessage.value = '';
    }
);

/**
 * Validates the email field and sets error message.
 */
const validateEmailField = (): void => {
    if (validateRequired(lead?.value?.email as string) || validateEmail(lead?.value?.email as string)) {
        leadErrors.value.email = validateRequired(lead?.value?.email as string) || validateEmail(lead?.value?.email as string);
    } else {
        leadErrors.value.email = '';
    }
};

/**
 * Validates the name field and sets error message.
 */
const validateNameField = (): void => {
    if (validateRequired(lead?.value?.name as string)) {
        leadErrors.value.name = validateRequired(lead?.value?.name as string);
    } else {
        leadErrors.value.name = '';
    }
};

/**
 * Handles filter operations.
 */
const onFilter = async (): Promise<void> => {
    reloadTable.value = true;
    directionTrigger.value = '';
    await loadLazyData();
};

/**
 * Loads data based on current filters.
 * @param event Optional event parameter for pagination and sorting.
 */
const loadLazyData = async (event?: any): Promise<void> => {
    const { global, leadAction, leadSource, leadStatus, lastFilter, dateFrom, dateTo, isQualified } = filters.value;

    const searchFilters: LeadFilters = {
        searchTerm: global?.value ?? '',
        lead_actions: leadAction?.value ?? [],
        lead_status: leadStatus?.value ?? [],
        lead_source: leadSource?.value ?? [],
        is_qualified: isQualified.value,
        failed_sync: false,
        dateRange: {
            lastMessageTimeStart: dateFrom?.value ? Math.floor(new Date(dateFrom.value.setHours(12, 0, 0, 0)).getTime() / 1000) : null,
            lastMessageTimeEnd: dateTo?.value ? Math.floor(new Date(dateTo.value.setHours(23, 59, 59, 999)).getTime() / 1000) : null
        },
        sort: { col: event?.sortField || defaultSort.value, order: event?.sortOrder !== 1 ? 'desc' : 'asc' },
        page: { pageSize: event?.rows || rows.value, page: event?.page + 1 || 1, currentPage: event?.page || 0 },
        mark_for_deletion: markForDeletion?.value ?? false
    };

    if (['qualified', 'unqualified'].includes(leadCategory.value as string)) {
        searchFilters.is_qualified = leadCategory.value === 'qualified';
    } else if (['quoted', 'inquired', 'converted', 'booked'].includes(leadCategory.value as string)) {
        searchFilters.lead_actions = [leadCategory.value as string];
    } else if (['failed'].includes(leadCategory.value as string)) {
        searchFilters.failed_sync = true;
    } else if (['irrelevant'].includes(leadCategory.value as string)) {
        searchFilters.mark_for_deletion = true;
    }

    if (isEmbeddedList.value) {
        delete searchFilters.mark_for_deletion;
        searchFilters.searchTerm = '';
        searchFilters.searchTerms = props.combinedLead;
    }
    if (searchFilters.is_qualified === 'none' || isEmbeddedList.value) {
        delete searchFilters.is_qualified;
    }

    await leadStore.countTotalLeads(searchFilters);
    totalRecords.value = leadStore.totalLeads;
    const lastPage = Math.ceil(totalRecords.value / rows.value) - 1;

    let direction = 'first';

    if (event && event.sortField) {
        searchFilters.sort = { col: event.sortField, order: event.sortOrder === 1 ? 'asc' : 'desc' };
    } else {
        searchFilters.sort = { col: sortbackup.value.sortField, order: sortbackup.value.sortOrder === 1 ? 'asc' : 'desc' };
    }
    // check if leadCategory is booked and the sort is lastMessageTime, then change it to latestAppointmentDate
    if (leadCategory.value === 'booked' && searchFilters.sort.col === 'lastMessageTime') {
        searchFilters.sort.col = 'latestAppointmentDate';
    } else if (leadCategory.value !== 'booked' && searchFilters.sort.col === 'latestAppointmentDate') {
        searchFilters.sort.col = 'lastMessageTime';
    }

    if (lastFilter?.sort?.order !== searchFilters?.sort?.order || lastFilter?.sort?.col !== searchFilters?.sort?.col || lastFilter?.page?.pageSize !== searchFilters?.page?.pageSize || reloadTable.value === true) {
        reloadTable.value = true;
        directionTrigger.value = '';
        direction = 'first';
        reset.value = 0;
        searchFilters.page.currentPage = 0;
        currentPage.value = 0;
        if (event?.page) event.page = 0;
    }

    if (directionTrigger?.value) {
        direction = 'current';
    } else if (event?.page === 0) {
        direction = 'first';
    } else if (event?.page === lastPage) {
        direction = 'last';
    } else if (event?.page > currentPage.value) {
        direction = 'next';
    } else if (event?.page < currentPage.value) {
        direction = 'prev';
    }

    await leadStore.fetchLeads(event?.rows || rows.value, searchFilters, direction);

    leads.value = leadStore.leads.filter((lead) => lead.id !== '64tKak7khn3au4cYuJqb');

    if (!directionTrigger?.value) {
        currentPage.value = event?.page || 0;
    }

    filters.value.lastFilter = searchFilters;
    if (typeof event === 'object') {
        if (event.sortField) {
            sortbackup.value = {
                sortField: event.sortField,
                sortOrder: event.sortOrder
            };
        } else {
            sortbackup.value = {
                sortField: defaultSort.value,
                sortOrder: 2
            };
        }
    }
    reloadTable.value = false;
    rows.value = event?.rows || lastFilter?.page?.pageSize || 10;
    directionTrigger.value = '';
    selectedLeads.value = null;
};

const submitted = ref<boolean>(false);
const leadActions = ref([
    { label: 'Inquired', value: 'inquired' },
    { label: 'Quoted', value: 'quoted' },
    { label: 'Booked', value: 'booked' },
    { label: 'Converted', value: 'converted' }
]);
const leadSources = ref([
    { label: 'WhatsApp', value: 'whatsapp' },
    { label: 'Calls', value: 'call' },
    { label: 'AI Calls', value: 'ai_call' },
    { label: 'Facebook', value: 'facebook' },
    { label: 'Telegram', value: 'telegram' },
    { label: 'Email', value: 'email' },
    { label: 'Gravity Forms', value: 'gravity_form' }
]);
const leadStatus = ref([
    { label: 'Cold', value: 'cold' },
    { label: 'Warm', value: 'warm' },
    { label: 'Hot', value: 'hot' }
]);

const leadQualifications = ref();
leadQualifications.value = [
    { label: 'All', value: 'none' },
    { label: 'Qualified', value: true },
    { label: 'Un-Qualified', value: false }
];

/**
 * Hides the lead dialog.
 */
const hideDialog = (): void => {
    leadDialog.value = false;
    submitted.value = false;
};

/**
 * Saves a lead to the database.
 */
const saveLead = async (leadToSave: Lead | object): Promise<void> => {
    submitted.value = true;

    lead.value = leadToSave;
    try {
        if (lead.value.lead_actions === 'converted') {
            lead.value.to_followup_date = 0;
        }
        lead.value.keywords = [...(lead.value.name ? [lead.value.name] : []), ...(lead.value.email ? [lead.value.email] : []), ...(lead.value.phone ? [lead.value.phone] : []), ...(lead.value.id ? [lead.value.id] : [])];
        await leadStore.updateLead(lead?.value?.id as string, lead.value as Lead);

        if (!lead.value) {
            throw new Error('Lead not found');
        }
        const leadId = lead.value.id;
        const index = leads.value.findIndex((leadToFInd) => leadToFInd.id === leadId);

        if (index !== -1) {
            leads.value[index] = { ...leads.value[index], ...lead.value };
        } else {
            throw new Error('Lead not found');
        }

        leadDialog.value = false;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Lead Saved', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
    }
};
const saveLeadNotes = async (notes: []): Promise<void> => {
    try {
        if (!lead.value && !notes.length) {
            throw new Error('Lead not found');
        }
        const leadId = lead.value.id;
        const index = leads.value.findIndex((leadToFInd) => leadToFInd.id === leadId);

        if (index !== -1) {
            const updatedLead = {
                ...leads.value[index],
                notes: notes,
                notes_count: notes.length || 0
            };
            leads.value[index] = { ...leads.value[index], ...updatedLead };
        } else {
            throw new Error('Lead not found');
        }

        toast.add({ severity: 'success', summary: 'Successful', detail: 'Lead Notes Saved', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. Lead not found`,
            detail: 'Error',
            life: 3000
        });
    }
};

/**
 * Prepares the lead form for editing.
 * @param prod The lead to be edited.
 */
const editLead = (prod: Lead): void => {
    leadDialog.value = true;
    lead.value = { ...prod, address: prod.address || prod.ai_conversation_summary?.address };
};

const showLeadAI = (prod: Lead): void => {
    leadAiDialog.value = true;
    lead.value = { ...prod };
};
const showLead = (prod: Lead): void => {
    leadDetailsDialog.value = true;
    lead.value = { ...prod };
};
const showLeadConversations = (prod: Lead): void => {
    leadConversationsDialog.value = true;
    followUpMessage.value = '';
    lead.value = { ...prod };
};

const showLeadNotes = (prod: Lead): void => {
    leadNotesDialog.value = true;
    lead.value = { ...prod };
};
/**
 * Opens the confirmation dialog to delete a lead.
 * @param prod The lead to be deleted.
 */
const confirmDeleteLead = (prod: Lead): void => {
    lead.value = prod;
    deleteLeadDialog.value = true;
};

/**
 * Deletes a lead from the database.
 */
const deleteLead = async (): Promise<void> => {
    try {
        await leadStore.deleteLead(lead?.value?.id as string);
        deleteLeadDialog.value = false;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Lead Deleted', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};

/**
 * Exports the current data view to CSV.
 */
const exportCSV = (): void => {
    dt.value.exportCSV();
};

/**
 * Opens the confirmation dialog to delete selected leads.
 */
const confirmDeleteSelected = (): void => {
    deleteLeadsDialog.value = true;
};

const confirmAnalyzeLeadsSelected = (): void => {
    analyzeLeadsDialog.value = true;
};
const confirmAnalyzeLeadSelected = (lead?: Lead): void => {
    selectedLeads.value = [lead];
    analyzeLeadsDialog.value = true;
};
const confirmMarkAsOutOfScope = (): void => {
    markAsOutOfScopeDialog.value = true;
};

const confirmMarkAsQualified = (): void => {
    markAsQualifiedDialog.value = true;
};

/**
 * Deletes selected leads from the database.
 */
const deleteSelectedLeads = async (): Promise<void> => {
    try {
        const idsToDelete = selectedLeads.value.map((lead) => lead.id);

        await leadStore.deleteLeads(idsToDelete);
        deleteLeadsDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Leads Deleted', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        await clearFilter();
    }
};
const analyzeSelectedLeads = async (): Promise<void> => {
    try {
        const ids = selectedLeads.value.map((lead) => lead.id);

        await leadStore.analyzeLeads(ids);
        analyzeLeadsDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Leads Analyzed', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};
const markOutOfScopeSelectedLeads = async (isOutOfScope: boolean = true): Promise<void> => {
    try {
        const ids = selectedLeads?.value?.map((lead) => lead.id.toString());
        await leadStore.outOfScopeLeads(isOutOfScope, ids);
        markAsOutOfScopeDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: `Leads Marked as ${leadCategory.value !== 'irrelevant' ? 'Blocked' : 'Unblocked'}`, life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};

const markQualifiedSelectedLeads = async (isQualified: boolean = true): Promise<void> => {
    try {
        const ids = selectedLeads.value.map((lead) => lead.id);

        await leadStore.qualifiedLeads(isQualified, ids);
        markAsOutOfScopeDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: `Leads Marked as ${leadCategory.value !== 'qualified' ? 'Qualified' : 'Unqualified'}`, life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};
const refreshCurrent = async (): Promise<void> => {
    directionTrigger.value = leadStore.previousLastVisible;
    await loadLazyData();
};
const getFollowUpMessage = async (id: string, messageParams: followUpParams): Promise<void> => {
    try {
        followUpMessage.value = await leadStore.getLeadFollowUpMessage([id], messageParams);
        console.log('followUpMessage', followUpMessage.value);
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Message Generated', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore?.error || 'Please try again'}`,
            detail: 'Error',
            life: 3000
        });
    }
};
const sendFollowUpMessage = async (id: string, messageParams: followUpParams): Promise<void> => {
    try {
        followUpMessage.value = await leadStore.sendLeadFollowUpMessage([id], messageParams);

        if (!lead.value) {
            throw new Error('Lead not found');
        }
        const leadId = lead.value.id;
        const index = leads.value.findIndex((leadToFInd) => leadToFInd.id === leadId);

        if (index !== -1) {
            leads.value[index] = { ...leads.value[index], ...lead.value };
        } else {
            throw new Error('Lead not found');
        }

        toast.add({ severity: 'success', summary: 'Successful', detail: 'Message Sent', life: 3000 });
        if (messageParams?.channel === 'call' || messageParams?.channel === 'ai_call') {
            toast.add({ severity: 'info', summary: 'Info', detail: 'AI is currently calling the lead.', life: 3000 });
        }
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore?.error || 'Lead not found'}`,
            detail: 'Error',
            life: 3000
        });
    }
};
/**
 * Returns the label for a given status.
 * @param status The status to get the label for.
 * @returns The label for the status.
 */
const getLeadActionLabel = (status: string): string | null => {
    switch (status) {
        case 'inquired':
            return 'primary';

        case 'quoted':
            return 'warn';

        case 'booked':
            return 'info';

        case 'converted':
            return 'success';

        default:
            return null;
    }
};
const getLeadSourceIcon = (status: string): string | null => {
    switch (status) {
        case 'call':
        case 'ai_call':
            return 'phone';

        case 'email':
            return 'envelope';

        case 'gravity_form':
            return 'calculator';

        case 'google_calendar_booking':
        case 'appointment_booking':
            return 'pi pi-calendar-plus';

        default:
            return status;
    }
};

// Update constraints when dateFrom changes
// Function to validate and constrain the dates to a 10-day range
const checkDates = async (trigger: string) => {
    const { dateFrom, dateTo } = filters.value;

    // Ensure dateFrom is not greater than dateTo
    if (trigger === 'dateFrom') {
        if (dateFrom.value && dateTo.value && dateFrom.value > dateTo.value) {
            dateTo.value = getDatePlusMinusDays(dateFrom.value, 30); // Set dateTo to 10 days after dateFrom
        }
    } else {
        // Ensure dateTo is not less than dateFrom
        if (dateTo.value && dateFrom.value && dateTo.value < dateFrom.value) {
            dateFrom.value = getDatePlusMinusDays(dateTo.value, -30); // Set dateFrom to 10 days before dateTo
        }
    }

    // If dateTo is missing, set it to 10 days after dateFrom
    if (!dateTo.value && dateFrom.value) {
        dateTo.value = getDatePlusMinusDays(dateFrom.value, 30);
    }

    // If dateFrom is missing, set it to 10 days before dateTo
    if (!dateFrom.value && dateTo.value) {
        dateFrom.value = getDatePlusMinusDays(dateTo.value, -30);
    }
    await onFilter();
};

const setExpandedRow = ($event: any) => {
    if (isMobile.value) {
        lead.value = $event && $event.data ? $event.data : null;

        if (!expandedRows.value) expandedRows.value = [];

        if (expandedRows.value && expandedRows.value[lead.value.id]) {
            delete expandedRows.value[lead.value.id];
            lead.value = null;
        } else {
            expandedRows.value = {};
            expandedRows.value[lead.value.id] = true;
        }
    }
};

const setLeadBookingDetailDialog = (trigger: boolean) => {
    leadBookingDetailDialog.value = trigger;
};

const saveNewLeadBooking = async (appointment: any) => {
    // toast.add({ severity: 'success', summary: 'Successful', detail: 'Lead Booking Saved', life: 3000 });
    // await loadLazyData();
};
</script>

<template>
    <LeadInfo
        v-if="!isEmbeddedList && lead"
        @closeModal="
            leadAiDialog = false;
            leadConversationsDialog = false;
            leadNotesDialog = false;
            leadDetailsDialog = false;
        "
        :leadBookingDetailDialog="leadBookingDetailDialog"
        @leadBookingDetailDialog="setLeadBookingDetailDialog"
        @leadAiDialog="leadAiDialog = true"
        @leadConversationsDialog="leadConversationsDialog = true"
        @leadNotesDialog="leadNotesDialog = true"
        @leadDetailsDialog="leadDetailsDialog = true"
        :leadInfoDialog="leadAiDialog || leadConversationsDialog || leadNotesDialog || leadDetailsDialog"
        :lead="lead"
    />
    <LeadBooking
        :lead="{
            booking_action: 'add'
        }"
        v-if="leadCategory === 'booked'"
        @saveFormData="saveNewLeadBooking"
        :leadBookingDialog="leadAddBookingDialog"
        @openModal="leadAddBookingDialog = true"
        @closeModal="leadAddBookingDialog = false"
    />

    <div>
        <div class="card">
            <Toolbar class="mb-6 responsive-toolbar" v-if="!isEmbeddedList">
                <template #start>
                    <div class="toolbar-buttons">
                        <Button
                            v-tooltip="'Book an appointment for a Lead'"
                            :loading="leadStore.loading"
                            label="Book Appointment"
                            icon="pi pi-calendar-plus"
                            severity="secondary"
                            @click="leadAddBookingDialog = true"
                            v-if="leadCategory === 'booked'"
                            :disabled="leadStore.loading"
                        />
                        <Button
                            v-tooltip="'Delete selected leads'"
                            :loading="leadStore.loading"
                            label="Delete"
                            icon="pi pi-trash"
                            severity="secondary"
                            @click="confirmDeleteSelected"
                            :disabled="!selectedLeads || !selectedLeads.length || leadStore.loading"
                        />
                        <Button
                            v-tooltip="'Analyze selected leads using AI'"
                            label="Analyze Leads"
                            icon="pi pi-sparkles"
                            severity="secondary"
                            @click="confirmAnalyzeLeadsSelected"
                            :disabled="!selectedLeads || !selectedLeads.length || leadStore.loading"
                        />
                        <Button
                            v-tooltip="`${leadCategory === 'irrelevant' ? 'Unblock' : 'Block'}`"
                            :label="leadCategory === 'irrelevant' ? 'Unblock' : 'Block'"
                            :icon="leadCategory === 'irrelevant' ? 'pi pi-check' : 'pi pi-ban'"
                            severity="secondary"
                            @click="confirmMarkAsOutOfScope"
                            :disabled="!selectedLeads || !selectedLeads.length || leadStore.loading"
                        />
                        <Button
                            v-tooltip="`${filters['isQualified'].value === true ? 'Mark as Unqualified' : 'Mark as Qualified'}`"
                            :label="filters['isQualified'].value === true ? 'Mark as Unqualified' : 'Mark as Qualified'"
                            :icon="filters['isQualified'].value === true ? 'pi pi-thumbs-down' : 'pi pi-thumbs-up'"
                            severity="secondary"
                            @click="confirmMarkAsQualified"
                            :disabled="!selectedLeads || !selectedLeads.length || leadStore.loading"
                        />
                    </div>
                </template>

                <template #end>
                    <div class="toolbar-buttons-end">
                        <Button label="Import" icon="pi pi-upload" severity="secondary" @click="importLeadsDialog = true" />
                        <Button label="Export" icon="pi pi-download" severity="secondary" @click="exportCSV($event)" />
                    </div>
                </template>
            </Toolbar>

            <DataTable
                ref="dt"
                v-model:selection="selectedLeads"
                v-model:first="reset"
                v-model:value="leads"
                dataKey="id"
                :paginator="true"
                v-model:rows="rows"
                v-model:totalRecords="totalRecords"
                :lazy="true"
                :loading="leadStore.loading"
                @page="loadLazyData"
                @sort="loadLazyData"
                filterDisplay="menu"
                :filters="filters"
                :rowsPerPageOptions="[5, 10]"
                paginatorTemplate="FirstPageLink PrevPageLink NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} leads"
                sort-mode="single"
                selectionMode="multiple"
                v-model:expandedRows="expandedRows"
                @row-click="setExpandedRow"
            >
                <!-- Header Template -->
                <template #header v-if="!isEmbeddedList">
                    <div class="surface-card p-4 border-round shadow-1 w-full flex flex-wrap gap-2 items-center justify-between">
                        <!-- Header Section -->
                        <div class="flex flex-column align-items-start gap-3 mb-4">
                            <h4 class="m-0 text-lg font-semibold capitalize">{{ leadCategory }} Leads</h4>

                            <!-- Buttons -->
                            <div class="flex flex-column sm:flex-row gap-2 w-full">
                                <Button type="button" icon="pi pi-filter-slash" label="Clear" outlined @click="clearFilter" class="w-full sm:w-auto" />
                                <Button type="button" icon="pi pi-refresh" label="Refresh" outlined @click="refreshCurrent" class="w-full sm:w-auto" />
                            </div>
                        </div>

                        <!-- Filter & Search Section -->
                        <div class="flex flex-wrap gap-3">
                            <!-- Lead Qualifications -->
                            <IconField v-if="!['qualified', 'unqualified'].includes(leadCategory as string)" class="search-inputs">
                                <Select v-model="filters['isQualified'].value" @change="onFilter" option-value="value" :options="leadQualifications" optionLabel="label" placeholder="Qualified" class="w-full" />
                            </IconField>

                            <!-- Search Input -->
                            <IconField class="search-inputs">
                                <InputIcon>
                                    <i class="pi pi-search" />
                                </InputIcon>
                                <InputText v-model="filters['global'].value" placeholder="Search..." @input="onFilter" class="w-full" />
                            </IconField>

                            <!-- Date Range -->
                            <div class="flex flex-column gap-2">
                                <DatePicker :showIcon="true" :showButtonBar="true" v-model="filters['dateFrom'].value" @date-select="checkDates('dateFrom')" class="w-full" />
                                <DatePicker :showIcon="true" :showButtonBar="true" v-model="filters['dateTo'].value" @date-select="checkDates('dateTo')" class="w-full" />
                            </div>
                        </div>
                    </div>
                </template>

                <!-- Columns visible only in desktop -->
                <Column v-if="!isMobile && !isEmbeddedList" selectionMode="multiple" style="width: 3rem" :exportable="false"></Column>

                <!-- AI Column -->
                <Column header="AI" :exportable="false" v-if="!isMobile && !isEmbeddedList" style="min-width: 3.5rem">
                    <template #header>
                        <i class="pi pi-fw pi-sparkles !text-2xl"></i>
                    </template>
                    <template #body="slotProps">
                        <Button icon="pi pi-sparkles" outlined rounded @click="confirmAnalyzeLeadSelected(slotProps.data)" v-tooltip="'Analyze this lead using AI'" placeholder="Top" />
                    </template>
                </Column>

                <Column v-if="!isMobile && !embeddedList && leadCategory === 'booked'" field="latestAppointmentDate" sortable header="Appointment" :sortable="false" align="center" :exportable="false" style="min-width: 13.5rem">
                    <template #body="slotProps">
                        <LeadPopOverDetails
                            v-if="slotProps.data?.appointmentHistory?.length > 0 && slotProps.data?.appointmentHistory.at(-1)?.appointmentDate"
                            icon="pi-calendar-plus"
                            title="Appointment"
                            :lead="slotProps.data"
                            :paramTextItems="[
                                {
                                    ...slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event,
                                    description: slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event?.description.substring(0, 300) + '...',

                                    appointment: slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event?.start.dateTime
                                }
                            ]"
                        >
                            <div
                                @click="
                                    leadBookingDetailDialog = true;
                                    lead = slotProps.data;
                                "
                            >
                                <CountdownTimer class="mb-7" type="booking" :target-timestamp="slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event.start.dateTime" />
                            </div>
                        </LeadPopOverDetails>
                    </template>
                </Column>
                <!-- Tools Column -->
                <Column v-if="!isMobile" header="Tools" field="conversations_count" sortable align="center" :exportable="false" style="min-width: 13.5rem">
                    <template #header="slotProps">
                        <i class="pi pi-fw pi-cog !text-2xl"></i>
                    </template>
                    <template #body="slotProps">
                        <LeadTools
                            :loading="leadStore.loading"
                            :error="leadStore.error?.message"
                            :lead="slotProps.data"
                            :hidden-tools="['analyze']"
                            @leadAiDialog="showLeadAI"
                            @leadConversationsDialog="showLeadConversations"
                            @leadNotesDialog="showLeadNotes"
                            @leadDetailsDialog="showLead"
                        />
                    </template>
                </Column>

                <!-- Regular Columns -->
                <Column v-if="!isMobile" field="lead_status" header="Lead Status" filterField="leadStatus" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" style="min-width: 10rem">
                    <template #body="{ data }">
                        <LeadStatusView :lead="data" />
                    </template>
                    <template #filter="{ filterModel }" v-if="!isEmbeddedList">
                        <MultiSelect v-model="filters['leadStatus'].value" @value-change="onFilter" :options="leadStatus" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>

                <Column field="phone" v-if="!isMobile" header="Phone" sortable style="min-width: 12rem" />
                <Column field="name" v-if="!isMobile" header="Name" sortable style="min-width: 12rem" />
                <Column field="email" v-if="!isMobile" header="Email" sortable style="min-width: 10rem" />
                <Column v-if="!isEmbeddedList && !isMobile" field="address" header="Address" style="min-width: 10rem">
                    <template #body="{ data }">
                        {{ data?.address || data?.ai_conversation_summary?.address || '' }}
                    </template>
                </Column>
                <Column
                    v-if="(isMobile && !isEmbeddedList) || (isMobile && isEmbeddedList)"
                    filterField="leadSource"
                    :filterField="leadCategory === 'booked' ? 'latestAppointmentDate' : 'leadSource'"
                    :showFilterMatchModes="false"
                    sortable
                    :filterMenuStyle="{ width: '12rem' }"
                    :field="leadCategory === 'booked' ? 'latestAppointmentDate' : 'lastMessageTime'"
                    header="Lead Info"
                    style="min-width: 12rem"
                >
                    <template #body="{ data }">
                        <LeadMobileInfo :lead="data" :dateToDisplay="leadCategory === 'booked' ? 'latestAppointmentDate' : 'lastMessageTime'" :isSelected="selectedLeads && selectedLeads.some((lead) => lead.id === data.id)" />
                    </template>
                    <template #filter="{ filterModel }" v-if="!isEmbeddedList">
                        <MultiSelect v-model="filters['leadSource'].value" @value-change="onFilter" :options="leadSources" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>

                <Column field="lastMessageTime" v-if="!isEmbeddedList && !isMobile" header="Message Date" sortable style="min-width: 10rem">
                    <template #body="{ data }">
                        {{ getHumanReadableDate(data.lastMessageTime) }}
                    </template>
                </Column>

                <Column field="lead_actions" header="Lead Action" filterField="leadAction" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" style="min-width: 10rem" v-if="!isEmbeddedList && !isMobile">
                    <template #body="{ data }">
                        <div class="flex items-center gap-2">
                            <Tag :value="data.lead_actions" :severity="getLeadActionLabel(data.lead_actions)" />
                        </div>
                    </template>
                    <template #filter="{ filterModel }" v-if="!['inquired', 'quoted', 'booked', 'converted'].includes(leadCategory)">
                        <MultiSelect v-model="filters['leadAction'].value" @value-change="onFilter" :options="leadActions" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>

                <Column field="lead_source" header="Lead Source" v-if="!isMobile" filterField="leadSource" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" style="min-width: 12rem">
                    <template #body="{ data }">
                        <div class="flex items-center gap-2">
                            <Chip>
                                <span class="capitalize">
                                    <i :class="`pi pi-${getLeadSourceIcon(data.lead_source)} mr-2`" />
                                    {{
                                        ['google_calendar_booking', 'appointment_booking'].includes(data.lead_source)
                                            ? 'Booking'
                                            : ['google_calendar_booking', 'appointment_booking'].includes(data.lead_source)
                                              ? 'Booking'
                                              : data.lead_source.replace(/_/g, ' ')
                                    }}
                                </span>
                            </Chip>
                        </div>
                    </template>
                    <template #filter="{ filterModel }">
                        <MultiSelect v-model="filters['leadSource'].value" @value-change="onFilter" :options="leadSources" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>

                <Column header="Actions" :exportable="false" style="min-width: 12rem" v-if="!isMobile">
                    <template #body="slotProps">
                        <Button icon="pi pi-pencil" v-tooltip="'Edit'" placeholder="Top" outlined rounded class="mr-2" @click="editLead(slotProps.data)" />
                        <Button icon="pi pi-trash" v-tooltip="'Delete'" placeholder="Top" outlined rounded severity="danger" @click="confirmDeleteLead(slotProps.data)" />
                    </template>
                </Column>

                <!-- Expansion Slot for Mobile View -->
                <template #expansion="slotProps" v-if="isMobile">
                    <LeadPopOverDetails
                        v-if="leadCategory === 'booked' && slotProps.data?.appointmentHistory?.length > 0 && slotProps.data?.appointmentHistory.at(-1)?.appointmentDate"
                        icon="pi-calendar-plus"
                        title="Last Appointment"
                        :lead="slotProps.data"
                        :paramTextItems="[
                            {
                                ...slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event,
                                description: slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event?.description.substring(0, 50) + '...',
                                appointment: slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event?.start.dateTime
                            }
                        ]"
                    >
                        <div
                            @click="
                                leadBookingDetailDialog = true;
                                lead = slotProps.data;
                            "
                        >
                            <CountdownTimer class="mb-7" type="booking" :target-timestamp="slotProps.data?.appointmentHistory[slotProps.data?.appointmentHistory?.length - 1]?.calendar_event.start.dateTime" />
                        </div>
                    </LeadPopOverDetails>

                    <LeadMobileExpander
                        @leadAiDialog="showLeadAI"
                        @leadConversationsDialog="showLeadConversations"
                        @leadNotesDialog="showLeadNotes"
                        @leadDetailsDialog="showLead"
                        @leadAnalyzeDialog="confirmAnalyzeLeadsSelected"
                        @leadEditDialog="editLead"
                        @leadDeleteDialog="confirmDeleteLead"
                        :lead="slotProps.data"
                    />
                </template>
            </DataTable>
        </div>
        <LeadAIInsigths v-if="lead" :leadAiDialog="leadAiDialog" :lead="lead" @openModal="leadAiDialog = true" @closeModal="leadAiDialog = false" />
        <LeadDetails v-if="lead" :leadDetailsDialog="leadDetailsDialog" :lead="lead" @openModal="leadDetailsDialog = true" @closeModal="leadDetailsDialog = false" />

        <LeadConversations
            v-if="lead"
            :lead="lead"
            :leadConversationsDialog="leadConversationsDialog"
            :conversations="lead.conversations"
            :defaultDate="lead.lastMessageTime"
            :followUpMessage="followUpMessage"
            :loading="leadStore.loading"
            :error="leadStore.error?.message"
            @openModal="leadConversationsDialog = true"
            @closeModal="leadConversationsDialog = false"
            @getFollowUpMessage="getFollowUpMessage"
            @sendFollowUpMessage="sendFollowUpMessage"
        />

        <!-- Scrollable container -->

        <Notes v-if="lead" :leadNotesDialog="leadNotesDialog" @openModal="leadNotesDialog = true" @closeModal="leadNotesDialog = false" :error="leadStore.error?.message" :lead="lead" :notes="lead?.notes || []" @notesUpdated="saveLeadNotes" />

        <LeadEdit
            v-if="lead"
            :leadDialog="leadDialog"
            :leadStatus="leadStatus"
            :leadActions="leadActions"
            :lead="lead"
            :loading="leadStore.loading"
            :error="leadStore.error?.message"
            @openModal="leadDialog = true"
            @closeModal="leadDialog = false"
            @saveLead="saveLead"
        />

        <Dialog v-model:visible="deleteLeadDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span v-if="lead"
                    >Are you sure you want to delete <b>{{ lead.name }}</b
                    >?</span
                >
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteLeadDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" @click="deleteLead" :disabled="leadStore.loading" :loading="leadStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="deleteLeadsDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to delete the selected leads?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteLeadsDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="deleteSelectedLeads" :loading="leadStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="analyzeLeadsDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to analyze the selected lead/s using AI?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="analyzeLeadsDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="analyzeSelectedLeads" :loading="leadStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="markAsOutOfScopeDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to Mark the selected lead/s as {{ leadCategory !== 'irrelevant' ? 'Block' : 'Unblock' }}?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="markAsOutOfScopeDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="markOutOfScopeSelectedLeads(leadCategory !== 'irrelevant')" :loading="leadStore.loading" />
            </template>
        </Dialog>
        <Dialog v-model:visible="markAsQualifiedDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to Mark the selected lead/s as {{ filters['isQualified'].value === false ? 'Qualified' : 'Un-Qualified' }}?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="markAsOutOfScopeDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="markQualifiedSelectedLeads(filters['isQualified'].value)" :loading="leadStore.loading" />
            </template>
        </Dialog>
        <Dialog v-model:visible="importLeadsDialog" :style="{ width: '450px' }" header="Import Leads from Excel File" :modal="true" :closable="!leadStore.loading">
            <div>
                <ImportLead />
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="importLeadsDialog = false" :disabled="leadStore.loading" />
            </template>
        </Dialog>
    </div>
</template>

<style scoped lang="scss">
@media (max-width: 767px) {
    .search-inputs {
        flex: 1 1 100%;
        width: 100%;
    }
}
</style>
