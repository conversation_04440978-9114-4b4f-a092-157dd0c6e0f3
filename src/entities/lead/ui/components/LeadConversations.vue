<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed, onUnmounted } from 'vue';
import { useLayout } from '@/layout/composables/layout';
import { formatDate, Lead, renderMarkdown } from '@/entities/lead';
import { marked } from 'marked';
import { useToast } from 'primevue/usetoast';
import { useTenantSettingStore } from '@/entities/settings';

const emits = defineEmits<{
    openModal: [];
    closeModal: [];
    getFollowUpMessage: (leadId: string) => void;
    sendFollowUpMessage: (leadId: string) => void;
    updateFollowUpMessage: (message: string) => void;
}>();
const props = defineProps<{
    leadConversationsDialog: boolean;
    followUpMessage: string | null | undefined;
    error?: any | null | undefined;
    loading: boolean;
    lead: Lead | object;
    conversations: Record<string, any>[] | object;
    defaultDate: number | string;
}>();

const colors = ref({
    user: '#673AB7',
    assistant: '#758093',
    speaker: '#673AB7',
    speakerA: '#673AB7', // Sales person (LIFTT)
    speakerB: '#2196F3', // Client
    speakerC: '#FF9800', // Additional speaker if needed
    speakerD: '#4CAF50' // Additional speaker if needed
});

const speakerLabels = {
    A: 'Speaker A',
    B: 'Speaker B',
    C: 'Speaker C',
    D: 'Speaker D'
};
// use setting store
const settingStore = useTenantSettingStore();

// Computed property to check if the device is mobile
const isMobile = ref(window.matchMedia('(max-width: 960px)').matches);

const toast = useToast();
const { logo, layoutConfig } = useLayout();
const dialog = ref(props.leadConversationsDialog);
const conversationContainer = ref(null);
const showSpeakerLabels = ref(!isMobile.value);
const followUpMessageContent = ref(props.followUpMessage || '');
const followUpMessageContentForPhone = ref<string>('');

const localLead = ref<Lead | object>(props.lead);

const whatsAppMessagePolicyWarning = ref<string>('');
const whatsappTemplateContent = ref<string | null>('');
const scripts = ref({
    inquired: 'Test Prompt Inquired',
    quoted: 'Test Prompt Quoted',
    booked: 'Test Prompt Booked'
});

// Add voice selection options
const voiceOptions = ref([{ label: 'Estella', id: 'Estella' }]);

// Add selected voice ref
const selectedVoice = ref(voiceOptions.value[0]);

// Get speaker color based on speaker ID
const getSpeakerColor = (speaker) => {
    if (!speaker) return colors.value.user;
    return colors.value[`speaker${speaker}`] || colors.value.speaker;
};

// Get speaker label based on speaker ID
const getSpeakerLabel = (speaker) => {
    return speakerLabels[speaker] || `Speaker ${speaker}`;
};

// Determine if conversation is a call with multiple speakers
const isCallWithSpeakers = computed(() => {
    if (!props.conversations || !Array.isArray(props.conversations)) return false;
    return props.conversations.some((item) => item.speaker);
});

// Update isMobile value when the screen size changes
const updateIsMobile = () => {
    isMobile.value = window.matchMedia('(max-width: 960px)').matches;
    showSpeakerLabels.value = showTemplateMessage.value = !isMobile.value;
};

onMounted(async () => {
    await settingStore.getConversationSettings();
    whatsappTemplateContent.value = await settingStore.getWhatsAppTemplate(props.lead.name);
    scripts.value = {
        inquired: await settingStore.getCallScript('inquired', selectedVoice.value.id, props.lead),
        quoted: await settingStore.getCallScript('quoted', selectedVoice.value.id, props.lead),
        booked: await settingStore.getCallScript('booked', selectedVoice.value.id, props.lead)
    };
    window.addEventListener('resize', updateIsMobile);
    chipOverflow.value = null;
});
onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
    chipOverflow.value = null;
});
// Sync dialog visibility with prop
watch(
    () => props.leadConversationsDialog,
    (newVal) => {
        dialog.value = newVal;
    }
);

// Emit event when dialog opens/closes
watch(
    () => dialog.value,
    (isVisible) => {
        if (isVisible) {
            emits('openModal');
        } else {
            emits('closeModal');
        }
    }
);

// Function to convert Markdown to HTML

const removeHtmlFromFollowUpMessage = (text: any) => {
    followUpMessageContent.value = renderMarkdown(text);
};

const expandedContent = ref<string | null>(null); // Stores the full content for the dialog
const showDialog = ref(false); // Controls the visibility of the dialog
const chipOverflow = ref<Array | undefined>(undefined); // Tracks if the Chip content overflows
const isScrolled = ref(false);

const scrollToBottom = () => {
    nextTick(async () => {
        const dialogContent = conversationContainer.value?.content;

        if (dialogContent) {
            dialogContent.scrollTo({ top: dialogContent.scrollHeight, behavior: 'smooth' });
        }
    });
};

// Function to check if the Chip content overflows
const checkChipOverflow = (el: HTMLElement, index: number) => {
    if (el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth) {
        if (!chipOverflow.value) {
            chipOverflow.value = [];
        }
        chipOverflow.value[index] = true;
    }
};

// Function to open the dialog with full content
const openFullViewDialog = (content: string) => {
    expandedContent.value = content;
    showDialog.value = true;
};

// Toggle speaker labels visibility
const toggleSpeakerLabels = () => {
    showSpeakerLabels.value = !showSpeakerLabels.value;
};

// Add these to the script section
const showEmojiPicker = ref(false);
const selectedChannel = ref(props.lead.lead_source);

// Common emojis for quick selection
const commonEmojis = ['😊', '👍', '🙏', '👋', '🎉', '✅', '⭐', '🔥', '💯', '📞', '📅', '🏠', '💼', '📝', '🤔', '👀'];

// Computed property to determine available communication channels
const availableChannels = computed(() => {
    const channels = [];

    // Add phone-based channels if phone exists
    if (localLead.value?.phone) {
        channels.push({
            label: 'WhatsApp',
            value: 'whatsapp',
            icon: 'pi pi-whatsapp'
        });
        channels.push({
            label: 'SMS',
            value: 'sms',
            icon: 'pi pi-comment'
        });
        channels.push({
            label: 'Call',
            value: 'call',
            icon: 'pi pi-phone'
        });
    }

    // Add email if it exists

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if ((localLead.value?.email && emailRegex.test(localLead.value.email)) || ['email', 'gravity_form', 'google_calendar_booking', 'appointment_booking'].includes(localLead.value?.lead_source)) {
        channels.push({
            label: 'Email',
            value: 'email',
            icon: 'pi pi-envelope'
        });
    }

    // Add other channels based on lead source
    if (localLead.value?.lead_source === 'facebook') {
        channels.push({
            label: 'Messenger',
            value: 'facebook',
            icon: 'pi pi-facebook'
        });
    }

    if (localLead.value?.lead_source === 'telegram') {
        channels.push({
            label: 'Telegram',
            value: 'telegram',
            icon: 'pi pi-telegram'
        });
    }

    return channels;
});

// check if the selected channel is whatsapp. It whatsapp, then we need to check the last message by user and if it's more than 24 hours, then we need to use the template
const isWhatsappTemplate = computed(() => {
    if (selectedChannel.value !== 'whatsapp') return false;
    let isMessageOld = false;
    if (selectedChannel.value === 'whatsapp') {
        if (!props.conversations || !Array.isArray(props.conversations)) return true;
        if (localLead.value?.lead_source === 'whatsapp') {
            const lastUserMessage = props.conversations.find((item) => item.role === 'user');
            if (!lastUserMessage) return true;
            const timeDifference = Date.now() - lastUserMessage.lastMessageTime * 1000;
            isMessageOld = timeDifference > 24 * 60 * 60 * 1000;
            whatsAppMessagePolicyWarning.value = "WhatsApp template is required due to Facebook's 24-hour messaging policy. It has been more than 24 hours since the last customer message.";
        } else {
            whatsAppMessagePolicyWarning.value = `This lead originally contacted us via ${getChannelName()}. Since this is their first WhatsApp message, we must use a template message.`;
            isMessageOld = true;
        }

        if (isMessageOld) {
            followUpMessageContentForPhone.value = whatsappTemplateContent.value;
        }
    }

    return isMessageOld;
});

// Set default channel based on lead source or available channels
watch(
    () => props.lead,
    async (newLead, oldLead) => {
        localLead.value = { ...newLead };
        selectedChannel.value = localLead.value.lead_source;
        selectedScriptKey.value = localLead.value.lead_actions;
        whatsappTemplateContent.value = await settingStore.getWhatsAppTemplate(localLead.value.name);
        scripts.value = {
            inquired: await settingStore.getCallScript('inquired', selectedVoice.value.id, localLead.value),
            quoted: await settingStore.getCallScript('quoted', selectedVoice.value.id, localLead.value),
            booked: await settingStore.getCallScript('booked', selectedVoice.value.id, localLead.value)
        };
        followUpMessageContentForPhone.value = '';
        followUpMessageContent.value = '';
    },
    { deep: true }
);
// Sync followUpMessageContent with prop
watch(
    () => props.followUpMessage,
    (followUpMessage) => {
        if (followUpMessage) {
            if (selectedChannel.value !== 'email') {
                followUpMessageContentForPhone.value = followUpMessage;
            } else {
                followUpMessageContent.value = renderMarkdown(followUpMessage);
            }
        } else {
            followUpMessageContent.value = followUpMessageContentForPhone.value = followUpMessage;
        }
    },
    { deep: true, immediate: true }
);

// Check if multiple channels are available
const hasMultipleChannels = computed(() => {
    return availableChannels.value.length > 1;
});

// Check if selected channel is phone-based
const isPhoneBasedChannel = computed(() => {
    return ['whatsapp', 'sms', 'telegram', 'facebook'].includes(selectedChannel.value);
});

// Check if selected channel is email-based
const isEmailBasedChannel = computed(() => {
    return ['email', 'gravity_form'].includes(selectedChannel.value);
});

// Check if selected channel is call-based
const isCallChannel = computed(() => {
    return ['call', 'ai_call'].includes(selectedChannel.value);
});

// Get channel name for display
const getChannelName = (channelName: string) => {
    switch (channelName) {
        case 'whatsapp':
            return 'WhatsApp';
        case 'sms':
            return 'SMS';
        case 'telegram':
            return 'Telegram';
        case 'facebook':
            return 'Messenger';
        case 'email':
            return 'Email';
        case 'call':
        case 'ai_call':
            return 'Call';
        case 'booking':
        case 'google_calendar_booking':
            return 'Booking';
        default:
            return 'Message';
    }
};

// Toggle emoji picker visibility
const toggleEmojiPicker = () => {
    showEmojiPicker.value = !showEmojiPicker.value;
};

// Add emoji to message
const addEmoji = (emoji) => {
    if (followUpMessageContentForPhone.value) {
        followUpMessageContentForPhone.value += emoji;
    } else {
        followUpMessageContentForPhone.value = emoji;
    }
};

// Get appropriate button label based on lead source
const getMessageButtonLabel = (leadSource) => {
    switch (leadSource) {
        case 'whatsapp':
            return 'Send WhatsApp';
        case 'telegram':
            return 'Send Telegram';
        case 'facebook':
            return 'Send Messenger';
        case 'email':
        case 'gravity_form':
            return 'Send Email';
        default:
            return 'Send';
    }
};

const selectedScriptKey = ref<string | null>(props.lead.lead_actions);

const scriptOptions = computed(() => {
    return Object.entries(scripts.value).map(([key, value]) => ({
        label: key.charAt(0).toUpperCase() + key.slice(1),
        value: key
    }));
});
const selectedScript = computed(() => {
    const key = selectedScriptKey.value || localLead.value?.lead_actions;
    return scripts.value[key] || '';
});

// Add these to your script section
const showTemplateMessage = ref(!isMobile.value);

// Function to copy template message to clipboard
const copyTemplateToClipboard = () => {
    if (followUpMessageContentForPhone.value) {
        navigator.clipboard
            .writeText(followUpMessageContentForPhone.value)
            .then(() => {
                // Show success toast or notification
                toast.add({
                    severity: 'success',
                    summary: 'Copied!',
                    detail: 'Template copied to clipboard',
                    life: 3000
                });
            })
            .catch((err) => {
                console.error('Failed to copy text: ', err);
                // Show error toast or notification
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to copy to clipboard',
                    life: 3000
                });
            });
    }
};
const sendMessage = (message: string) => {
    const messageParams = {
        channel: selectedChannel.value,
        message: message,
        voice: selectedVoice.value.id ?? '',
        script: selectedScriptKey.value ?? ''
    };

    emits('sendFollowUpMessage', localLead.value.id, messageParams);
    const currentTime = Math.floor(Date.now() / 1000);
    props.conversations.push({
        role: 'user',
        message: message,
        lastMessageTime: currentTime
    });
};
</script>

<template>
    <Dialog
        header="Lead Conversations"
        :closable="!props.loading"
        v-model:visible="dialog"
        :dismissableMask="!props.loading"
        :draggable="true"
        position="center"
        :breakpoints="{ '960px': '75vw' }"
        :style="{ width: lead.lead_source === 'email' ? '50vw' : '30vw' }"
        :modal="true"
        class="conversation-dialog list-dialog"
        :contentStyle="{ minHeight: 'calc(100vh - 600px)' }"
        ref="conversationContainer"
        @show="scrollToBottom"
    >
        <!-- Floating container for error, audio player, and speaker legend -->
        <div class="floating-elements" :class="{ 'is-scrolled': isScrolled }">
            <!-- Error message -->
            <Message v-if="props.error" severity="error" icon="pi pi-info-circle" class="floating-error">{{ props.error.message }}</Message>

            <!-- Audio player -->
            <div v-if="localLead.lead_details?.mediaUrl" class="audio-container">
                <audio controls class="audio-player w-full">
                    <source :src="localLead.lead_details?.mediaUrl" />
                    Your browser does not support the audio element.
                </audio>
            </div>

            <!-- Speaker legend for calls -->
            <div v-if="isCallWithSpeakers" class="speaker-legend">
                <div class="legend-header">
                    <h4>Speaker Legend</h4>
                    <Button
                        :icon="showSpeakerLabels ? 'pi pi-eye-slash' : 'pi pi-eye'"
                        :class="['p-button-rounded p-button-text p-button-sm', showSpeakerLabels ? 'p-button-info' : 'p-button-secondary']"
                        @click="toggleSpeakerLabels"
                        v-tooltip.bottom="showSpeakerLabels ? 'Hide Speaker Labels' : 'Show Speaker Labels'"
                    />
                </div>
                <Transition name="fade">
                    <div class="legend-items" v-if="showSpeakerLabels">
                        <div class="legend-item" v-for="(label, key) in speakerLabels" :key="key">
                            <div class="legend-color" :style="{ 'background-color': getSpeakerColor(key) }"></div>
                            <span>{{ label }}</span>
                        </div>
                    </div>
                </Transition>
            </div>
        </div>

        <!-- Spacer to prevent content jump when elements become fixed -->
        <div v-if="isScrolled && (props.error || localLead.lead_details?.mediaUrl || isCallWithSpeakers)" class="floating-elements-spacer"></div>

        <!-- Scrollable container -->
        <div class="conversation-container">
            <div class="conversation-timeline">
                <Timeline :value="props.conversations" :align="isCallWithSpeakers ? 'left' : 'alternate'" class="customized-timeline" :unstyled="true">
                    <template #content="slotProps">
                        <div
                            v-if="slotProps.item.message"
                            :class="[
                                'message-wrapper',
                                {
                                    'user-message': (slotProps.item.role === 'user' && !slotProps.item.speaker) || slotProps.item.speaker === 'A',
                                    'assistant-message': (slotProps.item.role === 'assistant' && !slotProps.item.speaker) || (slotProps.item.speaker && slotProps.item.speaker !== 'A')
                                }
                            ]"
                        >
                            <!-- Speaker label (for calls) -->
                            <div v-if="slotProps.item.speaker && showSpeakerLabels" class="speaker-label" :style="{ 'background-color': getSpeakerColor(slotProps.item.speaker) }">
                                {{ getSpeakerLabel(slotProps.item.speaker) }}
                            </div>

                            <div class="message-content">
                                <!-- User/Speaker A Avatar -->
                                <Avatar
                                    v-if="(slotProps.item.role === 'user' && !slotProps.item?.speaker) || slotProps.item.speaker === 'A'"
                                    :icon="'pi pi-user'"
                                    size="large"
                                    class="message-avatar"
                                    :style="{ 'background-color': slotProps.item.speaker ? getSpeakerColor(slotProps.item.speaker) : colors[slotProps.item.role] }"
                                    shape="circle"
                                />

                                <!-- Message bubble -->
                                <div class="message-bubble-container">
                                    <div
                                        class="message-bubble"
                                        :style="{
                                            'background-color': slotProps.item.speaker
                                                ? getSpeakerColor(slotProps.item.speaker)
                                                : layoutConfig.darkTheme
                                                  ? 'var(--surface-card)'
                                                  : slotProps.item.role === 'assistant'
                                                    ? colors.assistant
                                                    : colors[slotProps.item.role]
                                        }"
                                    >
                                        <!-- Expand button for overflow -->
                                        <Button v-if="chipOverflow && chipOverflow[slotProps.index]" icon="pi pi-window-maximize" class="expand-button" severity="secondary" @click="openFullViewDialog(slotProps.item.message)" text rounded />

                                        <!-- Message content -->
                                        <div
                                            class="message-text"
                                            @mouseover="checkChipOverflow($event.target as HTMLElement, slotProps.index)"
                                            @click="checkChipOverflow($event.target as HTMLElement, slotProps.index)"
                                            v-html="renderMarkdown(slotProps.item.message)"
                                        ></div>
                                    </div>

                                    <!-- Message timestamp -->
                                    <div class="message-time">
                                        {{ formatDate(slotProps.item.lastMessageTime ?? props.defaultDate) }}
                                    </div>
                                </div>

                                <!-- Assistant/Other speakers Avatar -->
                                <Avatar
                                    v-if="(slotProps.item.role === 'assistant' && !slotProps.item?.speaker) || (slotProps.item.speaker && slotProps.item.speaker !== 'A')"
                                    :image="slotProps.item.role === 'assistant' && !slotProps.item?.speaker ? `/logo/${logo}` : ''"
                                    :icon="slotProps.item.speaker && slotProps.item.speaker !== 'A' ? 'pi pi-user' : ''"
                                    class="message-avatar"
                                    :style="{ 'background-color': slotProps.item.speaker ? getSpeakerColor(slotProps.item.speaker) : '' }"
                                    size="large"
                                    shape="circle"
                                />
                            </div>
                        </div>
                    </template>
                </Timeline>
            </div>
        </div>

        <!-- Footer -->
        <template #footer>
            <div class="footer-container w-full">
                <!-- Communication channel selector -->
                <div v-if="hasMultipleChannels" class="channel-selector">
                    <span class="channel-label">Send via:</span>
                    <div class="channel-options">
                        <Button
                            :disabled="props.loading"
                            v-for="channel in availableChannels"
                            :key="channel.value"
                            :icon="channel.icon"
                            :label="channel.label"
                            :class="['channel-button', selectedChannel === channel.value ? 'channel-active' : '']"
                            @click="selectedChannel = channel.value"
                            text
                        />
                    </div>
                </div>

                <!-- WhatsApp/SMS/Telegram input (for phone-based channels) -->
                <div v-if="isPhoneBasedChannel" class="messaging-input-group">
                    <div class="emoji-textarea-container" v-if="!isWhatsappTemplate">
                        <Textarea
                            @update:modelValue="removeHtmlFromFollowUpMessage"
                            :contenteditable="true"
                            :placeholder="`Type your ${getChannelName(selectedChannel)} message...`"
                            v-model="followUpMessageContentForPhone"
                            :disabled="props.loading"
                            style="height: 200px; overflow-y: auto"
                            rows="2"
                            class="message-textarea"
                        />
                        <Button icon="pi pi-face-smile" class="emoji-button" @click="toggleEmojiPicker" v-tooltip.top="'Add Emoji'" text rounded />
                    </div>

                    <div v-if="showEmojiPicker" class="emoji-picker">
                        <div class="emoji-grid">
                            <Button v-for="emoji in commonEmojis" :key="emoji" class="emoji-item" @click="addEmoji(emoji)" text>
                                {{ emoji }}
                            </Button>
                        </div>
                    </div>

                    <div v-if="isWhatsappTemplate" class="whatsapp-template-container p-3 border-1 border-orange-200 bg-orange-50 dark:bg-orange-900/20 dark:border-orange-800 rounded-md my-3">
                        <div class="flex items-center gap-2 mb-2">
                            <i class="pi pi-exclamation-triangle text-orange-500 text-xl"></i>
                            <div class="flex-1 text-orange-700 dark:text-orange-300 font-medium">
                                {{ whatsAppMessagePolicyWarning }}
                            </div>
                            <Button
                                :icon="showTemplateMessage ? 'pi pi-eye-slash' : 'pi pi-eye'"
                                class="p-button-rounded p-button-text p-button-sm"
                                @click="showTemplateMessage = !showTemplateMessage"
                                v-tooltip.left="showTemplateMessage ? 'Hide Template' : 'Show Template'"
                            />
                        </div>

                        <Transition name="fade">
                            <div v-if="showTemplateMessage" class="template-content mt-2 border-1 border-dashed border-orange-400 dark:border-orange-600 p-3 rounded-md bg-white dark:bg-gray-900">
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="font-bold text-orange-700 dark:text-orange-300">Message Template</span>
                                    <Button icon="pi pi-copy" class="p-button-rounded p-button-text p-button-sm ml-auto" @click="copyTemplateToClipboard" v-tooltip.left="'Copy to clipboard'" />
                                </div>
                                <div class="template-text whitespace-pre-line text-sm">
                                    {{ whatsappTemplateContent }}
                                </div>
                            </div>
                        </Transition>
                    </div>

                    <div class="messaging-actions">
                        <Button
                            icon="pi pi-fw pi-sparkles"
                            label="Generate"
                            class="generate-button"
                            v-if="!isWhatsappTemplate"
                            @click="
                                emits('getFollowUpMessage', localLead.id, {
                                    channel: selectedChannel,
                                    message: followUpMessageContentForPhone
                                })
                            "
                            :loading="props.loading"
                        />
                        <Button
                            icon="pi pi-fw pi-send"
                            :label="`Send ${getChannelName(selectedChannel)}`"
                            class="send-button"
                            severity="success"
                            :disabled="props.loading || !followUpMessageContentForPhone"
                            :loading="props.loading"
                            @click="sendMessage(followUpMessageContentForPhone)"
                        />
                    </div>
                </div>

                <!-- Email input (for email-based channels) -->
                <div v-else-if="isEmailBasedChannel" class="email-input-group">
                    <Editor v-model="followUpMessageContent" @update:modelValue="removeHtmlFromFollowUpMessage" :readonly="props.loading" editorStyle="height: 200px" class="rich-text-editor" placeholder="Compose your email message...">
                        <template #toolbar>
                            <span class="ql-formats">
                                <button class="ql-bold" v-tooltip.top="'Bold'"></button>
                                <button class="ql-italic" v-tooltip.top="'Italic'"></button>
                                <button class="ql-underline" v-tooltip.top="'Underline'"></button>
                            </span>
                            <span class="ql-formats">
                                <button class="ql-list" value="ordered" v-tooltip.top="'Numbered List'"></button>
                                <button class="ql-list" value="bullet" v-tooltip.top="'Bullet List'"></button>
                            </span>
                            <span class="ql-formats">
                                <button class="ql-link" v-tooltip.top="'Insert Link'"></button>
                            </span>
                        </template>
                    </Editor>

                    <div class="email-actions">
                        <Button
                            icon="pi pi-fw pi-sparkles"
                            label="Generate"
                            class="generate-button"
                            @click="
                                emits('getFollowUpMessage', localLead.id, {
                                    channel: selectedChannel,
                                    message: followUpMessageContent
                                })
                            "
                            :loading="props.loading"
                        />
                        <Button icon="pi pi-fw pi-send" label="Send Email" severity="success" class="send-button" :loading="props.loading" :disabled="props.loading || !followUpMessageContent" @click="sendMessage(followUpMessageContent)" />
                    </div>
                </div>

                <!-- Call platforms with voice selection -->
                <div v-else-if="isCallChannel">
                    <div class="voice-selection-container w-full">
                        <label class="voice-label">Select Voice:</label>
                        <Dropdown v-model="selectedVoice" :options="voiceOptions" optionLabel="label" placeholder="Select Voice" class="voice-dropdown" />
                    </div>

                    <!-- Script Selection -->
                    <div class="voice-selection-container w-full">
                        <label class="voice-label">Select Script:</label>
                        <Dropdown v-model="selectedScriptKey" :options="scriptOptions" optionLabel="label" optionValue="value" placeholder="Select a Script" class="voice-dropdown" />
                    </div>

                    <div v-if="selectedScript" class="whatsapp-template-container p-3 border-1 border-orange-200 bg-orange-50 dark:bg-orange-900/20 dark:border-orange-800 rounded-md my-3">
                        <div class="flex items-center gap-2 mb-2">
                            <div class="flex-1 text-orange-700 dark:text-orange-300 font-medium">
                                {{ showTemplateMessage ? 'Hide Script' : 'Show Script' }}
                            </div>
                            <Button
                                :icon="showTemplateMessage ? 'pi pi-eye-slash' : 'pi pi-eye'"
                                class="p-button-rounded p-button-text p-button-sm"
                                @click="showTemplateMessage = !showTemplateMessage"
                                v-tooltip.left="showTemplateMessage ? 'Hide Script' : 'Show Script'"
                            />
                            <Button icon="pi pi-window-maximize" severity="secondary" @click="openFullViewDialog(selectedScript)" text rounded />
                        </div>

                        <Transition name="fade">
                            <div v-if="showTemplateMessage" class="template-content mt-2 border-1 border-dashed border-orange-400 dark:border-orange-600 p-3 rounded-md bg-white dark:bg-gray-900">
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="font-bold text-orange-700 dark:text-orange-300">Script Template</span>
                                </div>

                                <div class="template-text whitespace-pre-line text-sm" v-html="renderMarkdown(selectedScript)" />
                            </div>
                        </Transition>
                    </div>

                    <Button severity="success" class="call-button mt-4 w-full" icon="pi pi-fw pi-phone" label="Make AI Follow-Up Call" :loading="props.loading" @click="sendMessage(followUpMessageContent)" />
                </div>

                <!-- Notification for unsupported channels  -->
                <div v-else-if="!hasMultipleChannels && !isCallChannel && !isEmailBasedChannel && !isPhoneBasedChannel">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="pi pi-exclamation-triangle text-red-500 text-xl"></i>
                        <div class="flex-1 text-red-700 dark:text-red-300 font-medium">There are no messaging options available for this lead. Please contact the lead manually.</div>
                    </div>
                </div>

                <!-- Fallback for other channels -->
                <div v-else class="messaging-input-group">
                    <Textarea placeholder="Send Follow-Up Message..." v-model="followUpMessageContentForPhone" :disabled="props.loading" autoResize rows="2" style="height: 200px; overflow-y: auto" class="message-textarea" />
                    <div class="messaging-actions">
                        <Button
                            icon="pi pi-fw pi-sparkles"
                            label="Generate"
                            class="generate-button"
                            @click="
                                emits('getFollowUpMessage', localLead.id, {
                                    channel: selectedChannel,
                                    message: followUpMessageContentForPhone
                                })
                            "
                            :loading="props.loading"
                        />
                        <Button icon="pi pi-fw pi-send" label="Send" class="send-button" severity="success" :disabled="props.loading || !followUpMessageContentForPhone" :loading="props.loading" @click="sendMessage(followUpMessageContentForPhone)" />
                    </div>
                </div>
            </div>
        </template>
    </Dialog>

    <!-- Full View Dialog -->
    <Dialog header="Full Message Content" v-model:visible="showDialog" :style="{ width: 'auto', maxWidth: '90vw' }" :breakpoints="{ '960px': '90vw' }" :modal="true" class="full-content-dialog">
        <div class="full-content" v-html="renderMarkdown(expandedContent)"></div>
    </Dialog>
</template>

<style lang="scss">
// Timeline customization
.p-timeline-event-separator {
    display: none;
}

// Dialog styling
.conversation-dialog {
    .p-dialog-content {
        padding: 0;
        display: flex;
        flex-direction: column;
    }
}

// Audio player
.audio-container {
    padding: 0.75rem 1rem;
    background-color: var(--surface-section);
    border-bottom: 1px solid var(--surface-border);

    .is-scrolled & {
        padding: 0.5rem 1rem;
        border-bottom: none;
    }
}

// Speaker legend
.speaker-legend {
    padding: 0.75rem 1rem;
    background-color: var(--surface-ground);
    border-bottom: 1px solid var(--surface-border);

    padding: 0.75rem 1rem;
    background-color: var(--surface-ground);
    border-bottom: 1px solid var(--surface-border);

    .legend-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        h4 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-color-secondary);
        }
    }

    .legend-items {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
}

// Conversation container
.conversation-container {
    position: relative;
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: var(--surface-ground);

    .conversation-timeline {
        padding-top: 0.5rem;
    }
}

// Message styling
.message-wrapper {
    margin-bottom: 1.5rem;
    position: relative;

    &.user-message {
        .message-content {
            justify-content: flex-start;
        }

        .message-bubble {
            border-radius: 18px 18px 18px 4px;
        }
    }

    &.assistant-message {
        .message-content {
            justify-content: flex-end;
        }

        .message-bubble {
            border-radius: 18px 18px 4px 18px;
        }
    }
}

.speaker-label {
    position: absolute;
    top: -18px;
    left: 50px;
    font-size: 0.7rem;
    padding: 2px 8px;
    border-radius: 10px;
    color: white;
    z-index: 1;
}

.message-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.message-avatar {
    flex-shrink: 0;
}

.message-bubble-container {
    max-width: 80%;
}

.message-bubble {
    padding: 0.75rem 1rem;
    color: white;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-text {
    word-break: break-word;
    max-height: 400px;
    overflow-y: auto;

    p {
        margin: 0 0 0.5rem 0;

        &:last-child {
            margin-bottom: 0;
        }
    }

    a {
        color: inherit;
        text-decoration: underline;
    }
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-color-secondary);
    margin-top: 0.25rem;
    text-align: right;
}

.expand-button {
    position: absolute !important;
    top: 0.25rem;
    right: 0.25rem;
    width: 2rem !important;
    height: 2rem !important;
    z-index: 2;
}

// Footer styling
.footer-container {
    padding: 0.5rem;
    border-top: 1px solid var(--surface-border);
    background-color: var(--surface-section);
}

.channel-selector {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0 0.5rem;

    .channel-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-color-secondary);
        margin-right: 0.75rem;
    }

    .channel-options {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .channel-button {
        border-radius: 1rem;
        padding: 0.25rem 0.75rem;
        font-size: 0.875rem;

        &.channel-active {
            background-color: var(--primary-color);
            color: var(--primary-color-text);
        }
    }
}

.messaging-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.emoji-textarea-container {
    position: relative;

    .message-textarea {
        width: 100%;
        border-radius: 8px;
        padding-right: 40px;
    }

    .emoji-button {
        position: absolute;
        right: 8px;
        top: 8px;
        width: 2rem;
        height: 2rem;
        font-size: 1.2rem;
    }
}
.whatsapp-template-container {
    padding: 0.5rem;
    background-color: var(--surface-section);
    border-bottom: 1px solid var(--surface-border);
    color: var(--text-color-secondary);
    font-size: 0.875rem;
}

.emoji-picker {
    background-color: var(--surface-card);
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.25rem;

    .emoji-item {
        width: 2rem;
        height: 2rem;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 4px;

        &:hover {
            background-color: var(--surface-hover);
        }
    }
}

.messaging-actions {
    display: flex;
    gap: 0.5rem;

    .generate-button,
    .send-button {
        flex: 1;
        border-radius: 8px;
    }
}

.email-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .rich-text-editor {
        border-radius: 8px;
        overflow: hidden;
    }
}

.email-actions {
    display: flex;
    gap: 0.5rem;

    .generate-button,
    .send-button {
        flex: 1;
        border-radius: 8px;
    }
}

.call-action-container {
    display: flex;
    flex-direction: column;
    padding: 0.5rem;
    align-items: center;

    .call-button {
        width: 100%;
        max-width: 300px;
        border-radius: 8px;
        margin: 0 auto;
    }
}

// Full content dialog
.full-content-dialog {
    .full-content {
        max-height: 80vh;
        overflow-y: auto;
        padding: 1rem;
        background-color: var(--surface-card);
        border-radius: 8px;
        color: var(--text-color);
    }
}

// Dark mode adjustments
:deep(.app-dark) {
    .message-bubble {
        background-color: var(--surface-card);
    }

    .speaker-legend {
        background-color: var(--surface-card);
    }

    .audio-container,
    .footer-container {
        background-color: var(--surface-card);
    }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
    .message-bubble-container {
        max-width: 75%;
    }

    .message-actions {
        flex-direction: column;
    }

    .speaker-legend .legend-items {
        flex-direction: column;
        gap: 0.5rem;
    }

    .message-text {
        max-height: 300px;
        font-size: 0.9rem;
    }

    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .messaging-actions,
    .email-actions {
        flex-direction: column;
    }

    .rich-text-editor {
        max-height: 150px;
    }
}

// Add these to the style section
.floating-elements {
    position: relative;
    z-index: 5;
    transition: all 0.3s ease;

    &.is-scrolled {
        position: sticky;
        top: 0;
        background-color: var(--surface-overlay);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid var(--surface-border);
        padding-bottom: 0.5rem;
        margin-bottom: 0.5rem;
        border-radius: 0 0 8px 8px;
    }

    .floating-error {
        margin-bottom: 0.5rem;
    }
}

.floating-elements-spacer {
    height: 0;

    &.with-error {
        height: calc(var(--message-height, 60px) + 0.5rem);
    }

    &.with-audio {
        height: calc(var(--audio-height, 80px) + 0.5rem);
    }

    &.with-legend {
        height: calc(var(--legend-height, 100px) + 0.5rem);
    }
}

/* Add styles for voice selection */
.voice-selection-container {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0 0.5rem;
    justify-content: center;

    .voice-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-color-secondary);
        margin-right: 0.75rem;
    }

    .voice-dropdown {
        flex: 1;
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;

    .custom-spinner {
        width: 100% !important;
        height: 200px !important;
    }
}

.whatsapp-template-container {
    transition: all 0.3s ease;

    .template-content {
        transition: all 0.3s ease;

        .template-text {
            max-height: 200px;
            overflow-y: auto;
            line-height: 1.5;
        }
    }
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
    transition:
        opacity 0.3s ease,
        max-height 0.3s ease;
    max-height: 500px;
    overflow: hidden;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    max-height: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .whatsapp-template-container {
        padding: 0.75rem;

        .template-content {
            padding: 0.75rem;
        }
    }
}
</style>
