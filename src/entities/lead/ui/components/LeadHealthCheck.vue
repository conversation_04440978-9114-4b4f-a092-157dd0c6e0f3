<script setup lang="ts">
import { computeLeadStatus, getLeadIcon, getLeadIconColor, Lead, LeadPopOverDetails, ParamsIcon } from '@/entities/lead';
import { computed, ref } from 'vue';

const props = defineProps<{
    lead: Lead | object;
    is_popover: boolean;
}>();

const leadStatus = computed(() => computeLeadStatus(props.lead));

const leadIconColor = computed(() => {
    return getLeadIconColor(leadStatus.value);
});

const leadIcon = computed(() => {
    return getLeadIcon(leadStatus.value);
});

const isSpam = computed(() => {
    if (typeof props.lead?.ai_conversation_summary?.spam_detection === undefined) return 'NA';
    return props.lead?.ai_conversation_summary?.spam_detection?.is_spam === true ? 'No' : 'Yes';
});

const details = ref([
    {
        qualified: props.lead.is_qualified == null ? 'NA' : props.lead.is_qualified ? 'Yes' : 'No',
        not_spam: isSpam.value,
        not_blocked: props.lead?.mark_for_deletion == null ? 'NA' : props.lead.mark_for_deletion ? 'No' : 'Yes',
        interested: props.lead?.ai_conversation_summary?.lead_interest == null ? 'NA' : props.lead.ai_conversation_summary.lead_interest ? 'Yes' : 'No'
    }
]);
</script>

<template>
    <ParamsIcon v-if="props.lead?.ai_conversation_summary && is_popover === false" :icon="leadIcon" :icon_color="`${leadIconColor}-500`" :icon_text_color="`${leadIconColor}-100`" title="Lead Health Check" :text="details" />
    <LeadPopOverDetails
        v-if="props.lead?.ai_conversation_summary && is_popover === true"
        :is_responsive="false"
        :icon="leadIcon"
        :icon_color="`${leadIconColor}-500`"
        :icon_text_color="`${leadIconColor}-100`"
        title="Lead Health Check"
        :lead="props.lead"
        :paramTextItems="details"
    >
        <slot></slot>
    </LeadPopOverDetails>
</template>
