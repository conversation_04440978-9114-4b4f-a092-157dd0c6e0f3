<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { formatDate, getHumanReadableDate } from '../../model';

// Define props
const props = defineProps<{
    targetTimestamp: number | string;
    type?: 'followup' | 'booking';
    size?: 'small' | 'medium' | 'large';
}>();

// Set default values
const timerType = computed(() => props.type || 'followup');
const timerSize = computed(() => props.size || 'medium');

// Reactive state
const remainingTime = ref({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
});
const isFinished = ref(false);
const isPastDue = ref(false);
const isCloseToDeadline = ref(false);

// Parse the target date from either timestamp or ISO string
const parseTargetDate = (value: number | string): Date => {
    if (typeof value === 'string') {
        return new Date(value);
    }
    return new Date(value * 1000);
};
const parseTargetDateToTimestamp = (value: number | string): Date => {
    if (typeof value === 'string') {
        return new Date(value).getTime() / 1000;
    }
    return value;
};

// Store the target date as a mutable variable
let targetDate = parseTargetDate(props.targetTimestamp);
let intervalId: number | null = null;

// Function to calculate remaining time
function updateCountdown(): void {
    const now = new Date();
    const difference = targetDate.getTime() - now.getTime();

    if (difference <= 0) {
        isFinished.value = true;
        isPastDue.value = Math.abs(difference) > 1000 * 60 * 60 * 24; // More than 1 day past
        remainingTime.value = { days: 0, hours: 0, minutes: 0, seconds: 0 };
        return;
    }

    // Check if close to deadline (less than 1 hour)
    isCloseToDeadline.value = difference < 1000 * 60 * 60;

    remainingTime.value = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / (1000 * 60)) % 60),
        seconds: Math.floor((difference / 1000) % 60)
    };
}

// Format numbers to always have two digits
const formatNumber = (num: number): string => {
    return num < 10 ? `0${num}` : `${num}`;
};

// Get status class based on remaining time
const statusClass = computed(() => {
    if (isFinished.value) {
        return isPastDue.value ? 'past-due' : 'expired';
    }
    if (isCloseToDeadline.value) {
        return 'urgent';
    }
    if (remainingTime.value.days < 1) {
        return 'soon';
    }
    return 'normal';
});

// Get status text
const statusText = computed(() => {
    if (isFinished.value) {
        return isPastDue.value ? 'Past Due' : 'Expired';
    }
    if (isCloseToDeadline.value) {
        return 'Urgent';
    }
    if (remainingTime.value.days < 1) {
        return 'Soon';
    }
    return timerType.value === 'followup' ? 'Follow-up' : 'Appointment';
});

// Start countdown on mount
onMounted(() => {
    updateCountdown();
    intervalId = window.setInterval(updateCountdown, 1000);
});

// Clear interval on unmount
onUnmounted(() => {
    if (intervalId) {
        clearInterval(intervalId);
    }
});

// Watch for changes in the targetTimestamp
watch(
    () => props.targetTimestamp,
    (newVal) => {
        targetDate = parseTargetDate(newVal);
        updateCountdown();
    }
);
</script>

<template>
    <div class="countdown-container" :class="[statusClass, timerSize]">
        <div class="countdown-header">
            <i :class="['status-icon pi', isFinished ? 'pi-exclamation-circle' : isCloseToDeadline ? 'pi-clock' : 'pi-calendar-clock']"></i>
            <span class="status-text">
                {{ getHumanReadableDate(parseTargetDateToTimestamp(props.targetTimestamp)) }}
            </span>
        </div>

        <div class="countdown-timer">
            <template v-if="!isFinished">
                <div v-if="remainingTime.days > 0" class="time-unit">
                    <span class="time-value">{{ remainingTime.days }}</span>
                    <span class="time-label">d</span>
                </div>
                <div class="time-unit">
                    <span class="time-value">{{ formatNumber(remainingTime.hours) }}</span>
                    <span class="time-label">h</span>
                </div>
                <div class="time-unit">
                    <span class="time-value">{{ formatNumber(remainingTime.minutes) }}</span>
                    <span class="time-label">m</span>
                </div>
                <div v-if="timerSize !== 'small'" class="time-unit">
                    <span class="time-value">{{ formatNumber(remainingTime.seconds) }}</span>
                    <span class="time-label">s</span>
                </div>
            </template>
            <div v-else class="expired-text">
                {{ isPastDue ? 'Overdue' : "Time's up" }}
            </div>
        </div>
    </div>
</template>

<style scoped>
.countdown-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 8px;
    border: 1px solid var(--surface-border);
    transition: all 0.2s ease;
    font-size: 0.85rem;
    width: fit-content;
    overflow: hidden;
    min-width: 160px;
    padding: 8px;
}

.countdown-container:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Size variants */
.countdown-container.small {
    font-size: 0.75rem;
}

.countdown-container.medium {
    font-size: 0.85rem;
}

.countdown-container.large {
    font-size: 0.95rem;
}

/* Status styles */
.normal {
    border-color: var(--p-surface-border) !important;
}

.soon {
    border-color: var(--p-blue-500) !important;
}

.urgent {
    border-color: var(--p-orange-500) !important;
}

.expired {
    border-color: var(--p-red-500) !important;
}

.past-due {
    border-color: var(--p-red-500) !important;
}

/* Dark mode adjustments */
:deep(.app-dark) .normal {
    background-color: var(--p-surface-hover);
}

:deep(.app-dark) .soon {
    border-color: var(--p-blue-600);
}

:deep(.app-dark) .urgent {
    border-color: var(--p-orange-600);
}

:deep(.app-dark) .expired,
:deep(.app-dark) .past-due {
    border-color: var(--p-red-600);
}

.countdown-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--p-surface-border);
}

.status-icon {
    font-size: 1em;
}

.normal .status-icon {
    color: var(--p-primary-color);
}

.soon .status-icon {
    color: var(--p-blue-500);
}

.urgent .status-icon {
    color: var(--p-orange-500);
}

.expired .status-icon,
.past-due .status-icon {
    color: var(--p-red-500);
}

.status-text {
    font-weight: 600;
}

.normal .status-text {
    color: var(--p-primary-color);
}

.soon .status-text {
    color: var(--p-blue-700);
}

.urgent .status-text {
    color: var(--p-orange-700);
}

.expired .status-text,
.past-due .status-text {
    color: var(--p-red-700);
}

.countdown-timer {
    display: flex;
    align-items: center;
    gap: 4px;
    font-variant-numeric: tabular-nums;
}

.time-unit {
    display: flex;
    align-items: baseline;
    margin: 0 2px;
}

.time-value {
    font-weight: 900;
    font-size: 1.3em;
}

.time-label {
    margin-left: 1px;
}

.expired-text {
    font-weight: 900;
    color: var(--p-red-600);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .countdown-container {
        width: 100%;
    }

    .countdown-header {
        justify-content: center;
    }

    .countdown-timer {
        justify-content: center;
    }
}
</style>
