<script setup lang="ts">
import { Lead, LeadPopOverDetails, ParamsIcon } from '@/entities/lead';

const props = defineProps<{
    lead: Lead | object;
}>();

const getLeadStatusLabel = (status: string): string | null => {
    switch (status) {
        case 'hot':
            return 'danger';

        case 'warm':
            return 'warn';

        case 'cold':
            return 'info';

        default:
            return null;
    }
};
</script>

<template>
    <div class="flex items-center gap-2">
        <LeadPopOverDetails
            v-if="props.lead.ai_conversation_summary"
            icon="pi-sparkles"
            title="AI Summary"
            :lead="props.lead"
            :paramTextItems="[
                {
                    Conversation_Summary: props.lead.ai_conversation_summary?.conversation_summary || props.lead.ai_conversation_summary?.ai_conversation_summary,
                    Insights_For_Conversion: props.lead.ai_conversation_summary?.sales_advice?.insights_for_conversion,
                    Qualification_Strategy: props.lead.ai_conversation_summary?.sales_advice?.qualification_strategy,
                    outcome: props.lead?.ai_conversation_summary?.lead_source_classification,
                    lead_interest: props.lead?.ai_conversation_summary?.lead_interest,
                    suggested_action:
                        props.lead?.lead_source === 'call' || props.lead?.lead_source === 'ai_call'
                            ? 'Voicemail or Callback'
                            : props.lead?.lead_source === 'facebook'
                              ? 'Facebook Messenger'
                              : props.lead?.lead_source === 'gravity_form'
                                ? 'Email'
                                : props.lead?.lead_source
                }
            ]"
        >
            <Tag :value="props.lead.lead_status" :severity="getLeadStatusLabel(props.lead.lead_status)" />
        </LeadPopOverDetails>
        <Tag v-else :value="props.lead.lead_status" :severity="getLeadStatusLabel(props.lead.lead_status)" />
    </div>
</template>
