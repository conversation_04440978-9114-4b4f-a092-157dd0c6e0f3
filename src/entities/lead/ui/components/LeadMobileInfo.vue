<script setup lang="ts">
import { computeLeadStatus, getHumanReadableDate, getLeadIconColor, Lead, LeadHealthCheck } from '@/entities/lead';
import { computed } from 'vue';

const props = defineProps<{
    lead: Lead | object;
    isSelected?: boolean;
    dateToDisplay?: string;
}>();

const leadStatus = computed(() => computeLeadStatus(props.lead));

const leadIconColor = computed(() => {
    return getLeadIconColor(leadStatus.value);
});
</script>

<template>
    <div class="flex self-center flex-wrap flex-column justify-start" style="gap: 0.5rem">
        <Badge class="mr-1" severity="success" v-if="isSelected"><i class="pi pi-check mr-1 text-white"></i></Badge>
        <span v-if="props.lead.name"> {{ props.lead.name }}</span>
        <span v-if="props.lead.name && (props.lead.phone || props.lead.email)" class="text-secondary">|</span>
        <span v-if="props.lead.phone" class="text-nowrap"> <i class="pi pi-phone mr-1"></i> {{ props.lead.phone }} </span>
        <span v-if="props.lead.phone && props.lead.email" class="text-secondary">|</span>
        <span v-if="props.lead.email" class="text-nowrap"> <i class="pi pi-envelope mr-1"></i> {{ props.lead.email }} </span>
        <span v-if="props.lead.name || props.lead.phone || props.lead.email" class="text-secondary">|</span>

        <span class="text-nowrap"> <i class="pi pi-clock mr-1"></i> {{ getHumanReadableDate(props.dateToDisplay === 'latestAppointmentDate' ? props.lead?.latestAppointmentDate : props.lead?.lastMessageTime) }} </span>
    </div>
</template>
