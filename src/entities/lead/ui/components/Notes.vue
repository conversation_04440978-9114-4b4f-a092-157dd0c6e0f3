<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue';
import { Lead, useLeadsStore } from '@/entities/lead';
import { useAuthStore } from '@/entities/auth';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';

const props = defineProps<{
    leadNotesDialog: boolean;
    lead: Lead;
    error?: any | null | undefined;
    notes: { note: string; createdBy: string; timeUpdated: number }[] | [];
}>();

const emits = defineEmits<{
    closeModal: [];
    openModal: [];
    notesUpdated: (notes: []) => void;
}>();

const leadStore = useLeadsStore();
const authStore = useAuthStore();
const confirmDialog = useConfirm();
const toast = useToast();

// Local reactive notes
const notes = ref(props.notes ? props.notes.map((note, idx) => ({ ...note, id: idx })) : []);

// Local reactive leadNotesDialog
const dialog = ref(props.leadNotesDialog);

// Selected notes for batch delete
const selectedNotes = ref<Set<number>>(new Set());

// Editor popup state
const isEditorVisible = ref(false);
const noteContent = ref('');
const currentNoteIndex = ref<number | null>(null);

// Toggle note editor visibility
const toggleNoteEditor = (show: boolean) => {
    isEditorVisible.value = show;

    if (!show) {
        // Reset editor state when closing
        noteContent.value = '';
        currentNoteIndex.value = null;
    } else {
        // When opening for a new note, ensure IDs are updated
        if (currentNoteIndex.value === null) {
            notes.value = notes.value ? notes.value.map((note, idx) => ({ ...note, id: idx })) : [];
        }
    }

    // Add/remove body scroll lock when editor is visible
    if (show) {
        document.body.classList.add('overflow-hidden');
    } else {
        document.body.classList.remove('overflow-hidden');
    }
};

// Open editor for new note
const openEditorDialogForNew = () => {
    noteContent.value = '';
    currentNoteIndex.value = null;
    toggleNoteEditor(true);
};

// Edit existing note
const editExistingNote = (noteIndex: number) => {
    noteContent.value = notes.value[noteIndex].note;
    currentNoteIndex.value = noteIndex;
    toggleNoteEditor(true);
};

// Save note
const saveNote = () => {
    if (noteContent.value.trim() === '') return;

    const now = Math.floor(Date.now() / 1000);
    if (currentNoteIndex.value === null) {
        // Add new note
        notes.value.unshift({
            note: noteContent.value,
            createdBy: authStore.user?.displayName || 'Unknown',
            timeUpdated: now,
            id: 0 // Will be remapped below
        });
        notes.value = notes.value ? notes.value.map((note, idx) => ({ ...note, id: idx })) : [];
    } else {
        // Edit existing note
        notes.value[currentNoteIndex.value].note = noteContent.value;
        notes.value[currentNoteIndex.value].timeUpdated = now;
    }

    toggleNoteEditor(false);
};

// Clean up when component is unmounted
onUnmounted(() => {
    document.body.classList.remove('overflow-hidden');
});

// Context menu
const menu = ref(null);
const items = ref([
    { label: 'Add New', icon: 'pi pi-fw pi-plus', command: () => openEditorDialogForNew() },
    { label: 'Remove', icon: 'pi pi-fw pi-trash', command: () => deleteSelectedNotes(), disabled: notes.value.length === 0 }
]);

// Format timestamp
const formatDate = (timestamp: number): string => {
    const now = new Date();
    const date = new Date(timestamp * 1000);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    }
    if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    }
    if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    }
    return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
};

// Categorize notes
const categorizedNotes = computed(() => {
    const justNow = [];
    const minutesAgo = [];
    const hoursAgo = [];
    const daysAgo = [];
    const earlier = [];

    notes.value.forEach((note) => {
        const formatted = formatDate(note.timeUpdated);
        if (formatted === 'Just now') justNow.push(note);
        else if (formatted.includes('minute')) minutesAgo.push(note);
        else if (formatted.includes('hour')) hoursAgo.push(note);
        else if (formatted.includes('day')) daysAgo.push(note);
        else earlier.push(note);
    });

    return { justNow, minutesAgo, hoursAgo, daysAgo, earlier };
});

// Configuration for dynamic rendering
const categorizedGroups = computed(() => [
    { key: 'justNow', label: 'JUST NOW' },
    { key: 'minutesAgo', label: 'MINUTES AGO' },
    { key: 'hoursAgo', label: 'HOURS AGO' },
    { key: 'daysAgo', label: 'DAYS AGO' },
    { key: 'earlier', label: 'EARLIER' }
]);

// Batch delete
const deleteSelectedNotes = () => {
    const indicesToDelete = Array.from(selectedNotes.value).sort((a, b) => b - a);
    confirmDialog.close();
    confirmDialog.require({
        message: 'Are you sure you want to proceed with deleting the selected notes?',
        header: 'Confirmation',
        icon: 'pi pi-exclamation-triangle',
        rejectProps: {
            label: 'Cancel',
            severity: 'secondary',
            outlined: true
        },
        acceptProps: {
            label: 'Save'
        },
        accept: async () => {
            indicesToDelete.forEach((index) => {
                notes.value.splice(index, 1);
            });
            selectedNotes.value.clear();
            await new Promise((resolve) => setTimeout(resolve, 1000));
            toast.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted to delete the notes', life: 3000 });
        },
        reject: () => {
            toast.add({ severity: 'info', summary: 'Rejected', detail: 'You have rejected to delete the notes', life: 3000 });
        }
    });
};

// Delete individual note
const deleteNote = (index: number) => {
    confirmDialog.close();
    confirmDialog.require({
        message: 'Are you sure you want to proceed with deleting this note?',
        header: 'Confirmation',
        icon: 'pi pi-exclamation-triangle',
        rejectProps: {
            label: 'Cancel',
            severity: 'secondary',
            outlined: true
        },
        acceptProps: {
            label: 'Save'
        },
        accept: async () => {
            notes.value.splice(index, 1);
            selectedNotes.value.delete(index);
            notes.value = notes.value ? notes.value.map((note, idx) => ({ ...note, id: idx })) : [];
            await new Promise((resolve) => setTimeout(resolve, 1000));
            toast.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted to delete the note', life: 3000 });
        },
        reject: () => {
            toast.add({ severity: 'info', summary: 'Rejected', detail: 'You have rejected to delete the note', life: 3000 });
        }
    });
};

// Cancel editor
const cancelEdit = () => {
    toggleNoteEditor(false);
    noteContent.value = '';
    currentNoteIndex.value = null;
};

// Checkbox toggle
const toggleSelection = (index: number) => {
    if (selectedNotes.value.has(index)) {
        selectedNotes.value.delete(index);
    } else {
        selectedNotes.value.add(index);
    }
};
watch(notes, async (newNotes, oldNotes) => {
    // if (newNotes === oldNotes) return;
    await leadStore.updateLead(props.lead.id as string, { notes: newNotes });
    emits('notesUpdated', newNotes);
});

// Sync dialog visibility with prop
watch(
    () => props.leadNotesDialog,
    (newVal) => {
        dialog.value = newVal;
    }
);

// Emit event when dialog opens/closes
watch(
    () => dialog.value,
    (isVisible) => {
        if (isVisible) {
            emits('openModal');
        } else {
            emits('closeModal');
        }
    }
);
</script>

<template>
    <Dialog :showHeader="true" :closable="false" v-model:visible="dialog" :dismissableMask="true" :draggable="true" :breakpoints="{ '960px': '75vw' }" :style="{ width: '30vw' }" :modal="true" :closeOnEscape="true" class="notes-dialog list-dialog">
        <template #header>
            <div class="flex items-center justify-between w-full">
                <div class="font-semibold text-xl">Notes</div>

                <div class="flex items-center">
                    <Button icon="pi pi-plus" class="p-button-rounded p-button-success mr-2" @click="toggleNoteEditor(true)" v-tooltip.bottom="'Add New Note'" />
                    <Button icon="pi pi-trash" class="p-button-rounded p-button-danger mr-2" @click="deleteSelectedNotes()" :disabled="selectedNotes.size === 0" v-tooltip.bottom="'Delete Selected Notes'" />
                    <Button icon="pi pi-times" class="p-button-rounded p-button-text" @click="emits('closeModal')" v-tooltip.bottom="'Close'" />
                </div>
            </div>
        </template>

        <div class="notes-container" :class="{ 'with-editor': isEditorVisible }">
            <Message v-if="props.error" severity="error" icon="pi pi-info-circle">{{ props.error.message }}</Message>

            <!-- Empty state -->
            <div v-if="!notes.length" class="empty-notes">
                <i class="pi pi-file-edit empty-icon"></i>
                <h3>No Notes Yet</h3>
                <p>Add your first note to keep track of important information about this lead.</p>
                <Button label="Add Note" icon="pi pi-plus" class="p-button-rounded p-button-success mt-3" @click="toggleNoteEditor(true)" />
            </div>

            <!-- Notes content -->
            <div v-else class="notes-list">
                <!-- Dynamic rendering of notification groups -->
                <div v-for="group in categorizedGroups" :key="group.key">
                    <div v-if="categorizedNotes[group.key].length" class="note-group">
                        <div class="note-group-header">{{ group.label }}</div>

                        <div v-for="note in categorizedNotes[group.key]" :key="`${group.key}-${note.id}`" class="note-card">
                            <div class="note-header">
                                <div class="note-author">
                                    <Checkbox :modelValue="selectedNotes.has(note.id)" @update:modelValue="toggleSelection(note.id)" :binary="true" class="mr-2" />
                                    <Avatar :label="note.createdBy.charAt(0)" shape="circle" size="small" class="mr-2" />
                                    <span class="author-name">{{ note.createdBy }}</span>
                                </div>
                                <span class="note-time">{{ formatDate(note.timeUpdated) }}</span>
                            </div>

                            <div class="note-content" v-html="note.note"></div>

                            <div class="note-actions">
                                <Button icon="pi pi-pencil" class="p-button-rounded p-button-text p-button-sm" @click="editExistingNote(note.id)" v-tooltip.bottom="'Edit Note'" />
                                <Button icon="pi pi-trash" class="p-button-rounded p-button-text p-button-danger p-button-sm" @click="deleteNote(note.id)" v-tooltip.bottom="'Delete Note'" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide-up Note Editor -->
        <div class="note-editor-container" :class="{ visible: isEditorVisible }">
            <div class="note-editor-backdrop" @click="toggleNoteEditor(false)"></div>
            <div class="note-editor-panel">
                <div class="note-editor-header">
                    <h3>{{ currentNoteIndex === null ? 'Add New Note' : 'Edit Note' }}</h3>
                    <Button icon="pi pi-times" class="p-button-rounded p-button-text" @click="toggleNoteEditor(false)" />
                </div>
                <div class="note-editor-content">
                    <Editor v-model="noteContent" editorStyle="height: 250px" placeholder="Type your note here..." />
                </div>
                <div class="note-editor-footer">
                    <Button label="Cancel" icon="pi pi-times" class="p-button-outlined" @click="toggleNoteEditor(false)" />
                    <Button label="Save" icon="pi pi-check" class="p-button-success" @click="saveNote" :disabled="!noteContent.trim()" autofocus />
                </div>
            </div>
        </div>

        <ConfirmDialog></ConfirmDialog>
    </Dialog>
</template>

<style scoped>
.notes-dialog :deep(.p-dialog-content) {
    padding: 0;
    overflow: hidden;
}

.notes-container {
    height: 70vh;
    overflow-y: auto;
    padding: 1rem;
    transition: height 0.3s ease;
}

.notes-container.with-editor {
    height: calc(70vh - 350px);
}

.empty-notes {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-color-secondary);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.empty-notes h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.empty-notes p {
    max-width: 300px;
    margin-bottom: 1.5rem;
}

.note-group {
    margin-bottom: 2rem;
}

.note-group-header {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-color-secondary);
    margin-bottom: 0.75rem;
    letter-spacing: 0.5px;
    padding-left: 0.5rem;
}

.note-card {
    background-color: var(--surface-card);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
    transition:
        box-shadow 0.2s,
        transform 0.2s;
}

.note-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--surface-border);
}

.note-author {
    display: flex;
    align-items: center;
}

.author-name {
    font-weight: 600;
    color: var(--text-color);
}

.note-time {
    font-size: 0.8rem;
    color: var(--text-color-secondary);
}

.note-content {
    padding: 1rem;
    color: var(--text-color);
    line-height: 1.5;
    overflow-wrap: break-word;
    word-break: break-word;
}

.note-actions {
    display: flex;
    justify-content: flex-end;
    padding: 0.5rem 1rem;
    border-top: 1px solid var(--surface-border);
    background-color: var(--surface-ground);
    border-radius: 0 0 8px 8px;
}

.editor-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1rem;
}

/* Dark mode adjustments */
:deep(.app-dark) .note-card {
    background-color: var(--surface-card);
    border-color: var(--surface-border);
}

:deep(.app-dark) .note-actions {
    background-color: var(--surface-ground);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .note-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .note-time {
        margin-top: 0.5rem;
        margin-left: 2.5rem;
    }

    .editor-actions {
        flex-direction: column;
    }

    .editor-actions .p-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Add these styles for the slide-up editor */
.note-editor-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    visibility: hidden;
    transform: translateY(100%);
    transition:
        transform 0.3s ease,
        visibility 0s linear 0.3s;
}

.note-editor-container.visible {
    visibility: visible;
    transform: translateY(0);
    transition:
        transform 0.3s ease,
        visibility 0s linear 0s;
}

.note-editor-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.note-editor-container.visible .note-editor-backdrop {
    opacity: 1;
}

.note-editor-panel {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--surface-overlay);
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.note-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--surface-border);
}

.note-editor-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-color);
}

.note-editor-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.note-editor-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
}

/* Animation for the slide-up */
@keyframes slide-up {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Dark mode adjustments */
:deep(.app-dark) .note-editor-panel {
    background-color: var(--surface-card);
    border-color: var(--surface-border);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .note-editor-panel {
        max-height: 90vh;
    }

    .note-editor-footer {
        flex-direction: column;
    }

    .note-editor-footer .p-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
