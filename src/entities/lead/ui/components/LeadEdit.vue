<script setup lang="ts">
import { ref, watch } from 'vue';
import { Lead, ParamsIcon } from '@/entities/lead';

const emits = defineEmits<{
    openModal: [];
    closeModal: [];
    saveLead: (lead: Lead | object) => void;
}>();
const props = defineProps<{
    leadDialog: boolean;
    lead: Lead | object;
    error?: any | null | undefined;
    loading: boolean;
    leadActions: any;
    leadStatus: any;
}>();

const dialog = ref(props.leadDialog);
// Local reactive copy of lead
const localLead = ref<Lead | object>({ ...props.lead });

// Sync dialog visibility with prop
watch(
    () => props.leadDialog,
    (newVal) => {
        dialog.value = newVal;
    }
);

// Emit event when dialog opens/closes
watch(
    () => dialog.value,
    (isVisible) => {
        if (isVisible) {
            emits('openModal');
        } else {
            emits('closeModal');
        }
    }
);

// Sync localLead when props.lead changes
watch(
    () => props.lead,
    (newLead) => {
        localLead.value = { ...newLead };
    },
    { deep: true }
);
</script>

<template>
    <Dialog v-model:visible="dialog" header="Lead Details" :closable="!props.loading" :draggable="true" :breakpoints="{ '960px': '75vw' }" :style="{ width: '30vw' }" :modal="true" class="list-dialog">
        <div class="flex flex-col gap-6">
            <Message v-if="props.error && props.error?.message" class="flex justify-center" icon="pi pi-exclamation-circle" severity="error" size="small" variant="simple">
                <span>{{ props.error.message }}</span>
            </Message>
        </div>
        <div class="flex flex-col gap-6">
            <!--
<div>
<label for="name" class="block font-bold mb-3">Name</label>
<IconField>
<InputText @input="validateNameField" id="name" v-model.trim="localLead.name" autofocus :invalid="leadErrors.name.length > 0" fluid />
<InputIcon v-if="leadErrors.name" class="pi pi-info-circle error" />
</IconField>
<small v-if="leadErrors.name" class="text-red-500">{{ leadErrors.name }}</small>
</div>

<div>
<label for="email" class="block font-bold mb-3">Email</label>
<IconField>
<InputText @input="validateEmailField" id="email" v-model.trim="localLead.email" autofocus :invalid="leadErrors.email.length > 0" fluid />
<InputIcon v-if="leadErrors.email" class="pi pi-info-circle error" />
</IconField>
<small v-if="leadErrors.email" class="text-red-500">{{ leadErrors.email }}</small>
</div>
-->
            <div>
                <label for="name" class="block font-bold mb-3">Name</label>
                <IconField>
                    <InputText id="name" v-model.trim="localLead.name" autofocus fluid />
                </IconField>
            </div>
            <div>
                <label for="email" class="block font-bold mb-3">Email</label>
                <IconField>
                    <InputText id="email" v-model.trim="localLead.email" autofocus fluid />
                </IconField>
            </div>
            <div>
                <label for="phone" class="block font-bold mb-3">Phone</label>
                <IconField>
                    <InputText id="phone" v-model.trim="localLead.phone" autofocus fluid />
                </IconField>
            </div>
            <div>
                <label for="address" class="block font-bold mb-3">Address</label>
                <IconField>
                    <InputText id="address" v-model.trim="localLead.address" autofocus fluid />
                </IconField>
            </div>

            <div>
                <label for="address" class="block font-bold mb-3">Blocked Lead?</label>
                <Select
                    id="lead_actions"
                    v-model="localLead.mark_for_deletion"
                    option-value="value"
                    :options="[
                        { label: 'No', value: false },
                        { label: 'Yes', value: true }
                    ]"
                    optionLabel="label"
                    placeholder="Select an Action"
                    fluid
                ></Select>
            </div>
            <div>
                <label for="lead_status" class="block font-bold mb-3">Lead Status</label>
                <Select id="lead_status" v-model="localLead.lead_status" option-value="value" :options="props.leadStatus" optionLabel="label" placeholder="Select a Status" fluid></Select>
            </div>
            <div>
                <label for="lead_actions" class="block font-bold mb-3">Lead Action</label>
                <Select id="lead_actions" v-model="localLead.lead_actions" option-value="value" :options="props.leadActions" optionLabel="label" placeholder="Select an Action" fluid></Select>
            </div>
        </div>

        <template #footer>
            <Button class="p-button-danger p-button-rounded mt-3" label="Cancel" icon="pi pi-times" text @click="emits('closeModal')" :disabled="props.loading" />
            <Button class="p-button-success p-button-rounded mt-3" :disabled="props.loading" :loading="props.loading" label="Save" icon="pi pi-check" @click="emits('saveLead', localLead)" />
        </template>
    </Dialog>
</template>
