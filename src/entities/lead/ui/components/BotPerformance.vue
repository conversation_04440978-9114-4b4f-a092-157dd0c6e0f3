<script setup lang="ts">
import {
    LeadGenerationOverviewWidget,
    EngagementMetricsWidget,
    LeadQualificationInsightsWidget,
    SalesPipelineStatusWidget,
    ConversationAnalysisWidget,
    LeadKPIs,
    LeadSourcesOverview,
    RecentLeadsTable,
    RecentLeadActivities,
    LeadFunnelAnalysis,
    useLeadsStore,
    getDatePlusMinusDays,
    LeadFilters,
    getDateDifferenceInDays,
    leadActionsArray
} from '@/entities/lead';
import { onMounted, Ref, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const leadStore = useLeadsStore();

const dateMax = 30;
// Initialize dateFrom and dateTo with today's date
const dateFrom = ref(getDatePlusMinusDays(new Date(), dateMax * -1));
const dateTo = ref(new Date());

const route = useRoute();
const router = useRouter();
const leadCategory = ref(route.params.id);

onMounted(async () => {
    if (!leadActionsArray.includes(leadCategory.value)) {
        await router.push({ name: 'notfound-auth' });
    }
    await onSearch();
});

watch(
    () => route.params.id,
    async (newId) => {
        if (leadActionsArray.includes(newId)) {
            leadCategory.value = newId;
            await onSearch();
        } else {
            router.push({ name: 'notfound-auth' });
        }
    }
);
// Update constraints when dateFrom changes
watch(dateFrom, async (newVal) => {
    const diffDays = getDateDifferenceInDays(dateFrom.value, dateTo.value);
    if (newVal > dateTo.value || diffDays > dateMax) {
        dateTo.value = getDatePlusMinusDays(dateFrom.value, dateMax);
    }

    await onSearch();
});

// Update constraints when dateTo changes
watch(dateTo, async (newVal) => {
    const diffDays = getDateDifferenceInDays(dateFrom.value, dateTo.value);
    if (newVal < dateFrom.value || diffDays > dateMax) {
        dateFrom.value = getDatePlusMinusDays(dateTo.value, dateMax * -1);
    }

    await onSearch();
});
const onSearch = async () => {
    const searchFilters = {
        lead_source: [leadCategory.value],
        dateRange: {
            lastMessageTimeStart: dateFrom?.value ? Math.floor(dateFrom?.value?.getTime() / 1000) : null,
            lastMessageTimeEnd: dateTo?.value ? Math.floor(dateTo?.value?.getTime() / 1000) : null
        },
        sort: { col: 'lastMessageTime', order: 'asc' }
    };

    await leadStore.getLeadsForDashboard(searchFilters); // Fetch initial leads
};
</script>

<template>
    <div class="grid grid-cols-12 gap-8">
        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0">
                <div class="flex justify-between mb-4 gap-2">
                    <div>
                        <span class="block text-muted-color font-medium mb-4">Date Filter</span>
                        <div>
                            <!-- DatePicker for dateFrom -->
                            <DatePicker class="mb-2" :showIcon="true" :showButtonBar="true" v-model="dateFrom"></DatePicker>

                            <!-- DatePicker for dateTo -->
                            <DatePicker :showIcon="true" :showButtonBar="true" v-model="dateTo"></DatePicker>
                        </div>
                    </div>
                    <div class="flex items-center justify-center bg-orange-100 dark:bg-orange-400/10 rounded-border" style="width: 5rem; height: 2.5rem">
                        <i class="pi pi-filter text-yellow-500 !text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <LeadKPIs :leads="leadStore.leads" :loading="leadStore.loading" />

        <div class="col-span-12 xl:col-span-6 xl:col-span-3">
            <SalesPipelineStatusWidget :leads="leadStore.leads" :loading="leadStore.loading" />
            <RecentLeadsTable :leads="leadStore.leads" :loading="leadStore.loading" />
        </div>
        <div class="col-span-12 xl:col-span-6 xl:col-span-3">
            <LeadFunnelAnalysis :leads="leadStore.leads" :loading="leadStore.loading" />
            <LeadQualificationInsightsWidget :leads="leadStore.leads" :loading="leadStore.loading" />
        </div>
    </div>
</template>

<style scoped lang="scss"></style>
