<script setup lang="ts">
import { FilterMatchMode } from '@primevue/core/api';
import { useToast } from 'primevue/usetoast';
import { computed, nextTick, onMounted, onUnmounted, Ref, ref, watch } from 'vue';
import {
    Lead,
    LeadFilters,
    LeadConversations,
    ParamsIcon,
    useLeadsStore,
    getHumanReadableDate,
    getDatePlusMinusDays,
    leadActionsArray,
    ImportLead,
    CountdownTimer,
    LeadDetails,
    Notes,
    LeadAIInsigths,
    LeadEdit,
    LeadPopOverDetails,
    LeadInfo,
    LeadTools,
    LeadStatusView,
    LeadMobileExpander,
    LeadMobileInfo,
    followUpParams
} from '@/entities/lead';
import { validateEmail, validateRequired } from '@/utils';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const leadCategory: Ref<string> = ref(route.params.id);

const leadStore = useLeadsStore();
const leads = ref<Lead[]>([]);
const totalRecords = ref<number>(0);
const currentPage = ref<number>(0);

const expandedRows = ref({});
const isMobile = ref(window.matchMedia('(max-width: 960px)').matches);

// Update isMobile value when the screen size changes
const updateIsMobile = () => {
    isMobile.value = window.matchMedia('(max-width: 960px)').matches;
};

onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
});

onMounted(async () => {
    // await leadStore.syncLeads();
    window.addEventListener('resize', updateIsMobile);
    await loadLazyData();
});

const toast = useToast();
const reset = ref<number>(0);
const followUpMessage = ref<string>('');
const reloadTable = ref<boolean>(false);
const dt = ref<any>();
const leadDialog = ref<boolean>(false);
const leadAiDialog = ref<boolean>(false);
const leadConversationsDialog = ref<boolean>(false);
const leadDetailsDialog = ref<boolean>(false);
const deleteLeadDialog = ref<boolean>(false);
const deleteLeadsDialog = ref<boolean>(false);
const sendLeadsFollowUpDialog = ref<boolean>(false);
const delayFollowUpDialog = ref<boolean>(false);
const markAsQualifiedDialog = ref<boolean>(false);
const importLeadsDialog = ref<boolean>(false);
const markForDeletion = ref<boolean>(false);
const lead = ref<Lead | null>(null);
const rows = ref<number>(10);
const directionTrigger = ref<string>('');
const leadErrors = ref<{ email: string; name: string }>({
    email: '',
    name: ''
});
const selectedLeads = ref<Lead[] | null>(null);
const filters = ref();
const sortbackup = ref({
    sortField: 'to_followup_date',
    sortOrder: 2
});

const initFilters = () => {
    selectedLeads.value = null;
    totalRecords.value = 0;
    rows.value = 10;
    reset.value = 0;
    leads.value = [];
    filters.value = {
        lastFilter: {},
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        leadAction: { value: null, matchMode: FilterMatchMode.IN },
        leadSource: { value: null, matchMode: FilterMatchMode.IN },
        leadStatus: { value: null, matchMode: FilterMatchMode.IN },
        dateFrom: { value: new Date(), matchMode: FilterMatchMode.EQUALS },
        dateTo: { value: getDatePlusMinusDays(new Date(), 30), matchMode: FilterMatchMode.EQUALS },
        isQualified: { value: true, matchMode: FilterMatchMode.EQUALS }
    };
};
initFilters();
const clearFilter = async () => {
    initFilters();
    reloadTable.value = true;
    directionTrigger.value = '';
    await loadLazyData();
};
const invalidSignUp = computed(() => {
    return validateRequired(lead?.value?.email as string).length > 0 || validateEmail(lead?.value?.email as string).length > 0;
});

/**
 * Validates the email field and sets error message.
 */
const validateEmailField = (): void => {
    if (validateRequired(lead?.value?.email as string) || validateEmail(lead?.value?.email as string)) {
        leadErrors.value.email = validateRequired(lead?.value?.email as string) || validateEmail(lead?.value?.email as string);
    } else {
        leadErrors.value.email = '';
    }
};

/**
 * Validates the name field and sets error message.
 */
const validateNameField = (): void => {
    if (validateRequired(lead?.value?.name as string)) {
        leadErrors.value.name = validateRequired(lead?.value?.name as string);
    } else {
        leadErrors.value.name = '';
    }
};

/**
 * Handles filter operations.
 */
const onFilter = async (): Promise<void> => {
    reloadTable.value = true;
    directionTrigger.value = '';
    await loadLazyData();
};

/**
 * Loads data based on current filters.
 * @param event Optional event parameter for pagination and sorting.
 */
const loadLazyData = async (event?: any): Promise<void> => {
    const { global, leadAction, leadSource, leadStatus, lastFilter, dateFrom, dateTo, isQualified } = filters.value;

    const searchFilters: LeadFilters = {
        searchTerm: global?.value ?? '',
        lead_actions: leadAction?.value ?? [],
        lead_status: leadStatus?.value ?? [],
        lead_source: leadSource?.value ?? [],
        is_qualified: isQualified.value,
        failed_sync: false,
        has_followup_date: true,
        dateRange: {
            lastMessageTimeStart: dateFrom?.value ? Math.floor(new Date(dateFrom.value.setHours(12, 0, 0, 0)).getTime() / 1000) : null,
            lastMessageTimeEnd: dateTo?.value ? Math.floor(new Date(dateTo.value.setHours(23, 59, 59, 999)).getTime() / 1000) : null
        },
        sort: { col: event?.sortField || 'to_followup_date', order: event?.sortOrder !== 1 ? 'desc' : 'asc' },
        page: { pageSize: event?.rows || rows.value, page: event?.page + 1 || 1, currentPage: event?.page || 0 },
        mark_for_deletion: markForDeletion?.value ?? false
    };

    if (['qualified', 'unqualified'].includes(leadCategory.value as string)) {
        searchFilters.is_qualified = leadCategory.value === 'qualified';
    } else if (['quoted', 'inquired', 'converted', 'booked'].includes(leadCategory.value as string)) {
        searchFilters.lead_actions = [leadCategory.value as string];
    } else if (['failed'].includes(leadCategory.value as string)) {
        searchFilters.failed_sync = true;
    } else if (['irrelevant'].includes(leadCategory.value as string)) {
        searchFilters.mark_for_deletion = true;
    }
    await leadStore.countTotalLeads(searchFilters);
    totalRecords.value = leadStore.totalLeads;
    const lastPage = Math.ceil(totalRecords.value / rows.value) - 1;

    let direction = 'first';

    if (event && event.sortField) {
        searchFilters.sort = { col: event.sortField, order: event.sortOrder === 1 ? 'asc' : 'desc' };
    } else {
        searchFilters.sort = { col: sortbackup.value.sortField, order: sortbackup.value.sortOrder === 1 ? 'asc' : 'desc' };
    }

    if (lastFilter?.sort?.order !== searchFilters?.sort?.order || lastFilter?.sort?.col !== searchFilters?.sort?.col || lastFilter?.page?.pageSize !== searchFilters?.page?.pageSize || reloadTable.value === true) {
        reloadTable.value = true;
        directionTrigger.value = '';
        direction = 'first';
        reset.value = 0;
        searchFilters.page.currentPage = 0;
        currentPage.value = 0;
        if (event?.page) event.page = 0;
    }

    if (directionTrigger?.value) {
        direction = 'current';
    } else if (event?.page === 0) {
        direction = 'first';
    } else if (event?.page === lastPage) {
        direction = 'last';
    } else if (event?.page > currentPage.value) {
        direction = 'next';
    } else if (event?.page < currentPage.value) {
        direction = 'prev';
    }

    await leadStore.fetchLeads(event?.rows || rows.value, searchFilters, direction);

    leads.value = leadStore.leads.filter((lead) => lead.id !== '64tKak7khn3au4cYuJqb');

    if (!directionTrigger?.value) {
        currentPage.value = event?.page || 0;
    }

    filters.value.lastFilter = searchFilters;
    if (typeof event === 'object') {
        if (event.sortField) {
            sortbackup.value = {
                sortField: event.sortField,
                sortOrder: event.sortOrder
            };
        } else {
            sortbackup.value = {
                sortField: 'to_followup_date',
                sortOrder: 2
            };
        }
    }
    reloadTable.value = false;
    rows.value = event?.rows || lastFilter?.page?.pageSize || 10;
    directionTrigger.value = '';
    selectedLeads.value = null;
};

const submitted = ref<boolean>(false);
const leadActions = ref([
    { label: 'Inquired', value: 'inquired' },
    { label: 'Quoted', value: 'quoted' },
    { label: 'Booked', value: 'booked' },
    { label: 'Converted', value: 'converted' }
]);
const leadSources = ref([
    { label: 'WhatsApp', value: 'whatsapp' },
    { label: 'Calls', value: 'call' },
    { label: 'AI Calls', value: 'ai_call' },
    { label: 'Facebook', value: 'facebook' },
    { label: 'Telegram', value: 'telegram' },
    { label: 'Email', value: 'email' },
    { label: 'Gravity Forms', value: 'gravity_form' }
]);
const leadStatus = ref([
    { label: 'Cold', value: 'cold' },
    { label: 'Warm', value: 'warm' },
    { label: 'Hot', value: 'hot' }
]);

const leadQualifications = ref([
    { label: 'Qualified', value: true },
    { label: 'Un-Qualified', value: false }
]);

/**
 * Hides the lead dialog.
 */
const hideDialog = (): void => {
    leadDialog.value = false;
    submitted.value = false;
};

/**
 * Saves a lead to the database.
 */
const saveLead = async (leadToSave: Lead | object): Promise<void> => {
    submitted.value = true;

    lead.value = leadToSave;
    try {
        if (lead.value.lead_actions === 'converted') {
            lead.value.to_followup_date = 0;
        }
        lead.value.keywords = [...(lead.value.name ? [lead.value.name] : []), ...(lead.value.email ? [lead.value.email] : []), ...(lead.value.phone ? [lead.value.phone] : []), ...(lead.value.id ? [lead.value.id] : [])];
        await leadStore.updateLead(lead?.value?.id as string, lead.value as Lead);
        leadDialog.value = false;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Lead Saved', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;

        await loadLazyData();
    }
};

/**
 * Prepares the lead form for editing.
 * @param prod The lead to be edited.
 */
const editLead = (prod: Lead): void => {
    leadDialog.value = true;
    lead.value = { ...prod, address: prod.address || prod.ai_conversation_summary?.address };
};

const showLeadAI = (prod: Lead): void => {
    leadAiDialog.value = true;
    lead.value = { ...prod };
};
const showLead = (prod: Lead): void => {
    leadDetailsDialog.value = true;
    lead.value = { ...prod };
};
const showLeadConversations = (prod: Lead): void => {
    leadConversationsDialog.value = true;
    followUpMessage.value = '';
    lead.value = { ...prod };
};
/**
 * Opens the confirmation dialog to delete a lead.
 * @param prod The lead to be deleted.
 */
const confirmDeleteLead = (prod: Lead): void => {
    lead.value = prod;
    deleteLeadDialog.value = true;
};

/**
 * Deletes a lead from the database.
 */
const deleteLead = async (): Promise<void> => {
    try {
        await leadStore.deleteLead(lead?.value?.id as string);
        deleteLeadDialog.value = false;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Lead Deleted', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};

/**
 * Exports the current data view to CSV.
 */
const exportCSV = (): void => {
    dt.value.exportCSV();
};

/**
 * Opens the confirmation dialog to delete selected leads.
 */
const confirmDeleteSelected = (): void => {
    deleteLeadsDialog.value = true;
};

const confirmSendLeadsFollowUpsSelected = (): void => {
    sendLeadsFollowUpDialog.value = true;
};

const confirmDelayFollowUp = (): void => {
    delayFollowUpDialog.value = true;
};

const confirmMarkAsQualified = (): void => {
    markAsQualifiedDialog.value = true;
};

const selectedDateDelayOption = ref(new Date());
const selectDelayOptionValue = ref('schedule');
const selectDelayOptionValues = ref([
    { name: 'Set a Schedule', value: 'schedule' },
    { name: 'Based on Recurrence Interval Set by AI', value: 'recurrence' }
]);

/**
 * Deletes selected leads from the database.
 */
const deleteSelectedLeads = async (): Promise<void> => {
    try {
        const leadsData = selectedLeads.value
            ? selectedLeads.value.map((lead) => {
                  const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000);
                  const twoDaysAgoTimestamp = Math.floor(twoDaysAgo.getTime() / 1000);
                  return {
                      ...lead,
                      to_followup_date: twoDaysAgoTimestamp
                  };
              })
            : undefined;

        await leadStore.updateLeads(leadsData as Partial<Lead>[]);

        deleteLeadsDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Leads Deleted', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        await clearFilter();
    }
};
const sendFollowUpsForSelectedLeads = async (): Promise<void> => {
    try {
        const ids = selectedLeads.value.map((lead) => lead.id.toString());

        await leadStore.followUpLeads(ids);
        sendLeadsFollowUpDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Leads follow-ups successfully sent!', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};
const delaySendingFollowUps = async (): Promise<void> => {
    try {
        if (selectDelayOptionValue.value === 'schedule') {
            const ids = selectedLeads?.value?.map((lead) => lead.id.toString());
            await leadStore.updateLeadsByIds(ids as string[], { to_followup_date: Math.floor(new Date(selectedDateDelayOption.value).getTime() / 1000) });
        } else if (selectDelayOptionValue.value === 'recurrence') {
            const currentTime = Math.floor(Date.now() / 1000);
            const leadsData = selectedLeads.value
                ? selectedLeads.value.map((lead) => {
                      const frequencyDays = lead?.ai_conversation_summary?.follow_up_recommendation?.recurrence_strategy?.frequency_days || 1;
                      const followUpDate = lead?.to_followup_date || lead?.ai_conversation_summary?.follow_up_recommendation?.to_followup_date || currentTime;
                      const nextFollowUpDateTime = followUpDate + frequencyDays * 86400;
                      return {
                          ...lead,
                          to_followup_date: nextFollowUpDateTime
                      };
                  })
                : undefined;

            await leadStore.updateLeads(leadsData as Partial<Lead>[]);
        }

        delayFollowUpDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: `Leads Marked as ${leadCategory.value !== 'irrelevant' ? 'Unblocked' : 'Blocked'}`, life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};

const markQualifiedSelectedLeads = async (isQualified: boolean = true): Promise<void> => {
    try {
        const ids = selectedLeads.value.map((lead) => lead.id.toString());

        await leadStore.qualifiedLeads(isQualified, ids);
        delayFollowUpDialog.value = false;
        selectedLeads.value = null;
        toast.add({ severity: 'success', summary: 'Successful', detail: `Leads Marked as ${leadCategory.value !== 'qualified' ? 'Qualified' : 'Unqualified'}`, life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore.error}`,
            detail: 'Error',
            life: 3000
        });
    } finally {
        directionTrigger.value = leadStore.previousLastVisible;
        await loadLazyData();
    }
};
const refreshCurrent = async (): Promise<void> => {
    directionTrigger.value = leadStore.previousLastVisible;
    await loadLazyData();
};
const getFollowUpMessage = async (id: string, messageParams: followUpParams): Promise<void> => {
    try {
        followUpMessage.value = await leadStore.getLeadFollowUpMessage([id], messageParams);
        console.log('followUpMessage', followUpMessage.value);
        toast.add({ severity: 'success', summary: 'Successful', detail: 'Message Generated', life: 3000 });
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore?.error || 'Please try again'}`,
            detail: 'Error',
            life: 3000
        });
    }
};
const sendFollowUpMessage = async (id: string, messageParams: followUpParams): Promise<void> => {
    try {
        followUpMessage.value = await leadStore.sendLeadFollowUpMessage([id], messageParams);

        if (!lead.value) {
            throw new Error('Lead not found');
        }
        const leadId = lead.value.id;
        const index = leads.value.findIndex((leadToFInd) => leadToFInd.id === leadId);

        if (index !== -1) {
            leads.value[index] = { ...leads.value[index], ...lead.value };
        } else {
            throw new Error('Lead not found');
        }

        toast.add({ severity: 'success', summary: 'Successful', detail: 'Message Sent', life: 3000 });
        if (messageParams?.channel === 'call' || messageParams?.channel === 'ai_call') {
            toast.add({ severity: 'info', summary: 'Info', detail: 'AI is currently calling the lead.', life: 3000 });
        }
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: `An error has occured. ${leadStore?.error || 'Lead not found'}`,
            detail: 'Error',
            life: 3000
        });
    }
};
/**
 * Returns the label for a given status.
 * @param status The status to get the label for.
 * @returns The label for the status.
 */
const getLeadActionLabel = (status: string): string | null => {
    switch (status) {
        case 'inquired':
            return 'primary';

        case 'quoted':
            return 'warn';

        case 'booked':
            return 'info';

        case 'converted':
            return 'success';

        default:
            return null;
    }
};
const getLeadSourceIcon = (status: string): string | null => {
    switch (status) {
        case 'call':
        case 'ai_call':
            return 'phone';

        case 'email':
            return 'envelope';

        case 'gravity_form':
            return 'calculator';

        case 'google_calendar_booking':
        case 'appointment_booking':
            return 'pi pi-calendar-plus';

        default:
            return status;
    }
};

// Update constraints when dateFrom changes
// Function to validate and constrain the dates to a 10-day range
const checkDates = async (trigger: string) => {
    const { dateFrom, dateTo } = filters.value;

    // Ensure dateFrom is not greater than dateTo
    if (trigger === 'dateFrom') {
        if (dateFrom.value && dateTo.value && dateFrom.value > dateTo.value) {
            dateTo.value = getDatePlusMinusDays(dateFrom.value, 30); // Set dateTo to 10 days after dateFrom
        }
    } else {
        // Ensure dateTo is not less than dateFrom
        if (dateTo.value && dateFrom.value && dateTo.value < dateFrom.value) {
            dateFrom.value = getDatePlusMinusDays(dateTo.value, -30); // Set dateFrom to 10 days before dateTo
        }
    }

    // If dateTo is missing, set it to 10 days after dateFrom
    if (!dateTo.value && dateFrom.value) {
        dateTo.value = getDatePlusMinusDays(dateFrom.value, 30);
    }

    // If dateFrom is missing, set it to 10 days before dateTo
    if (!dateFrom.value && dateTo.value) {
        dateFrom.value = getDatePlusMinusDays(dateTo.value, -30);
    }
    await onFilter();
};

const setExpandedRow = ($event: any) => {
    if (isMobile.value) {
        lead.value = $event.data ? $event.data : null;
        if (!expandedRows.value) expandedRows.value = [];

        if (expandedRows.value && expandedRows.value[lead.value.id]) {
            delete expandedRows.value[lead.value.id];
            lead.value = null;
        } else {
            expandedRows.value = {};
            expandedRows.value[lead.value.id] = true;
        }
    }
};
</script>

<template>
    <LeadInfo
        v-if="lead"
        @closeModal="
            leadAiDialog = false;
            leadConversationsDialog = false;
            leadDetailsDialog = false;
        "
        @leadAiDialog="leadAiDialog = true"
        @leadConversationsDialog="leadConversationsDialog = true"
        @leadDetailsDialog="leadDetailsDialog = true"
        :leadInfoDialog="leadAiDialog || leadConversationsDialog || leadDetailsDialog"
        :lead="lead"
        :hiddenTools="['combined', 'booking']"
    />
    <div>
        <div class="card">
            <Toolbar class="mb-6 responsive-toolbar">
                <template #start>
                    <div class="toolbar-buttons">
                        <Button
                            v-tooltip="'Send Selected follow-ups immediately'"
                            class="ml-4"
                            :loading="leadStore.loading"
                            label="Send Immediately"
                            icon="pi pi-send"
                            severity="secondary"
                            @click="confirmSendLeadsFollowUpsSelected"
                            :disabled="!selectedLeads || !selectedLeads.length || leadStore.loading"
                        />
                        <Button
                            v-tooltip="'Clears all scheduled/pending selected follow-ups'"
                            class="ml-4"
                            :loading="leadStore.loading"
                            label="Remove All"
                            icon="pi pi-trash"
                            severity="secondary"
                            @click="confirmDeleteSelected"
                            :disabled="!selectedLeads || !selectedLeads.length || leadStore.loading"
                        />
                        <Button
                            v-tooltip="'Option to reschedule dispatch time of the selected follow-ups'"
                            class="ml-4"
                            :loading="leadStore.loading"
                            label="Delay Send"
                            icon="pi pi-calendar-clock"
                            severity="secondary"
                            @click="confirmDelayFollowUp"
                            :disabled="!selectedLeads || !selectedLeads.length || leadStore.loading"
                        />
                    </div>
                </template>
            </Toolbar>

            <DataTable
                ref="dt"
                v-model:selection="selectedLeads"
                v-model:first="reset"
                v-model:value="leads"
                dataKey="id"
                :paginator="true"
                v-model:rows="rows"
                v-model:totalRecords="totalRecords"
                :lazy="true"
                :loading="leadStore.loading"
                @page="loadLazyData"
                @sort="loadLazyData"
                filterDisplay="menu"
                :filters="filters"
                :rowsPerPageOptions="[5, 10]"
                paginatorTemplate="FirstPageLink PrevPageLink  NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} leads"
                sort-mode="single"
                selectionMode="multiple"
                v-model:expandedRows="expandedRows"
                @row-click="setExpandedRow"
            >
                <template #header>
                    <div class="surface-card p-4 border-round shadow-1 w-full flex flex-wrap gap-2 items-center justify-between">
                        <!-- Header Section -->
                        <div class="flex flex-column align-items-start gap-3 mb-4">
                            <h4 class="m-0 text-lg font-semibold capitalize">Review Pending Follow-Ups</h4>

                            <!-- Buttons -->
                            <div class="flex flex-column sm:flex-row gap-2 w-full">
                                <Button type="button" icon="pi pi-filter-slash" label="Clear" outlined @click="clearFilter" class="w-full sm:w-auto" />
                                <Button type="button" icon="pi pi-refresh" label="Refresh" outlined @click="refreshCurrent" class="w-full sm:w-auto" />
                            </div>
                        </div>

                        <!-- Filter & Search Section -->
                        <div class="flex flex-wrap gap-3">
                            <!-- Lead Qualifications -->
                            <IconField v-if="!['qualified', 'unqualified'].includes(leadCategory as string)" class="search-inputs">
                                <Select v-model="filters['isQualified'].value" @change="onFilter" option-value="value" :options="leadQualifications" optionLabel="label" placeholder="Qualified" class="w-full" />
                            </IconField>

                            <!-- Search Input -->
                            <IconField class="search-inputs">
                                <InputIcon>
                                    <i class="pi pi-search" />
                                </InputIcon>
                                <InputText v-model="filters['global'].value" placeholder="Search..." @input="onFilter" class="w-full" />
                            </IconField>

                            <!-- Date Range -->
                            <div class="flex flex-column gap-2">
                                <DatePicker :showIcon="true" :showButtonBar="true" v-model="filters['dateFrom'].value" @date-select="checkDates('dateFrom')" class="w-full" />
                                <DatePicker :showIcon="true" :showButtonBar="true" v-model="filters['dateTo'].value" @date-select="checkDates('dateTo')" class="w-full" />
                            </div>
                        </div>
                    </div>
                </template>

                <Column v-if="!isMobile" selectionMode="multiple" style="width: 3rem" :exportable="false"></Column>
                <Column v-if="!isMobile" field="to_followup_date" header="Follow Up Date" sortable style="min-width: 10rem">
                    <template #body="{ data }">
                        <LeadPopOverDetails
                            icon="pi-calendar-plus"
                            title="Follow-up Review"
                            :lead="data"
                            :paramTextItems="[
                                {
                                    outcome: data?.ai_conversation_summary?.lead_source_classification,
                                    lead_interest: data?.ai_conversation_summary?.lead_interest,
                                    suggested_action:
                                        data?.lead_source === 'call' || data?.lead_source === 'ai_call'
                                            ? 'Voicemail or Callback'
                                            : data?.lead_source === 'facebook'
                                              ? 'Facebook Messenger'
                                              : data?.lead_source === 'gravity_form'
                                                ? 'Email'
                                                : data?.lead_source
                                }
                            ]"
                        >
                            <CountdownTimer :target-timestamp="data.to_followup_date" />
                        </LeadPopOverDetails>
                    </template>
                </Column>
                <Column v-if="!isMobile" header="Tools" field="conversations_count" sortable align="center" :exportable="false" style="min-width: 10.5rem">
                    <template #header="slotProps"> <i class="pi pi-fw pi-cog !text-2xl"></i> </template>
                    <template #body="slotProps">
                        <div class="flex gap-1">
                            <LeadTools
                                :loading="leadStore.loading"
                                :error="leadStore.error?.message"
                                :lead="slotProps.data"
                                @leadAiDialog="showLeadAI"
                                @leadConversationsDialog="showLeadConversations"
                                :hidden-tools="['notes', 'details']"
                                @leadDetailsDialog="showLead"
                            />
                        </div>
                    </template>
                </Column>
                <Column v-if="!isMobile" field="lead_status" header="Lead Status" filterField="leadStatus" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" style="min-width: 10rem">
                    <template #body="{ data }">
                        <LeadStatusView :lead="data" />
                    </template>
                    <template #filter="{ filterModel }">
                        <MultiSelect v-model="filters['leadStatus'].value" @value-change="onFilter" :options="leadStatus" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>
                <Column field="phone" v-if="!isMobile" header="Phone" sortable style="min-width: 12rem" />
                <Column field="name" v-if="!isMobile" header="Name" sortable style="min-width: 12rem" />
                <Column field="email" v-if="!isMobile" header="Email" sortable style="min-width: 10rem" />
                <Column v-if="!isMobile" field="address" header="Address" style="min-width: 10rem">
                    <template #body="{ data }">
                        {{ data.address || data.ai_conversation_summary.address }}
                    </template>
                </Column>
                <Column v-if="isMobile" filterField="leadSource" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" field="lastMessageTime" header="Lead Info" style="min-width: 12rem">
                    <template #body="{ data }">
                        <CountdownTimer :target-timestamp="data.to_followup_date" />
                        {{ getHumanReadableDate(data.to_followup_date) }}

                        <Divider type="dashed" style="margin-top: 5px; margin-bottom: 5px" />
                        <LeadMobileInfo :lead="data" />
                    </template>
                    <template #filter="{ filterModel }">
                        <MultiSelect v-model="filters['leadSource'].value" @value-change="onFilter" :options="leadSources" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>

                <Column v-if="!isMobile" field="lead_actions" header="Lead Action" filterField="leadAction" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" style="min-width: 10rem">
                    <template #body="{ data }">
                        <div class="flex items-center gap-2">
                            <Tag :value="data.lead_actions" :severity="getLeadActionLabel(data.lead_actions)" />
                        </div>
                    </template>
                    <template #filter="{ filterModel }" v-if="!['inquired', 'quoted', 'booked', 'converted'].includes(leadCategory)">
                        <MultiSelect v-model="filters['leadAction'].value" @value-change="onFilter" :options="leadActions" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>
                <Column v-if="!isMobile" field="lead_source" header="Lead Source" filterField="leadSource" :showFilterMatchModes="false" sortable :filterMenuStyle="{ width: '12rem' }" style="min-width: 12rem">
                    <template #body="{ data }">
                        <div class="flex items-center gap-2">
                            <Chip>
                                <span class="capitalize"> <i :class="`pi pi-${getLeadSourceIcon(data.lead_source)} mr-2`" /> {{ data.lead_source.replace(/_/g, ' ') }}</span>
                            </Chip>
                        </div>
                    </template>
                    <template #filter="{ filterModel }">
                        <MultiSelect v-model="filters['leadSource'].value" @value-change="onFilter" :options="leadSources" optionLabel="label" optionValue="value" placeholder="Any">
                            <template #option="slotProps">
                                <div class="flex items-center gap-2">
                                    <span>{{ slotProps.option.label }}</span>
                                </div>
                            </template>
                        </MultiSelect>
                    </template>
                </Column>

                <!-- Expansion Slot for Mobile View -->
                <template #expansion="slotProps" v-if="isMobile">
                    <LeadMobileExpander
                        :hiddenTools="['analyze', 'notes']"
                        @leadAiDialog="showLeadAI"
                        @leadConversationsDialog="showLeadConversations"
                        @leadDetailsDialog="showLead"
                        @leadEditDialog="editLead"
                        @leadDeleteDialog="confirmDeleteLead"
                        :lead="slotProps.data"
                    />
                </template>
            </DataTable>
        </div>

        <LeadAIInsigths v-if="lead" :leadAiDialog="leadAiDialog" :lead="lead" @openModal="leadAiDialog = true" @closeModal="leadAiDialog = false" />
        <LeadDetails v-if="lead" :leadDetailsDialog="leadDetailsDialog" :lead="lead" @openModal="leadDetailsDialog = true" @closeModal="leadDetailsDialog = false" />

        <LeadConversations
            v-if="lead"
            :lead="lead"
            :leadConversationsDialog="leadConversationsDialog"
            :conversations="lead.conversations"
            :defaultDate="lead.lastMessageTime"
            :loading="leadStore.loading"
            :error="leadStore.error?.message"
            :followUpMessage="followUpMessage"
            @openModal="leadConversationsDialog = true"
            @closeModal="leadConversationsDialog = false"
            @getFollowUpMessage="getFollowUpMessage"
            @sendFollowUpMessage="sendFollowUpMessage"
        />

        <!-- Scrollable container -->

        <LeadEdit
            v-if="lead"
            :leadDialog="leadDialog"
            :leadStatus="leadStatus"
            :leadActions="leadActions"
            :lead="lead"
            :loading="leadStore.loading"
            :error="leadStore.error?.message"
            @openModal="leadDialog = true"
            @closeModal="leadDialog = false"
            @saveLead="saveLead"
        />

        <Dialog v-model:visible="deleteLeadDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span v-if="lead"
                    >Are you sure you want to delete <b>{{ lead.name }}</b
                    >?</span
                >
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteLeadDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" @click="deleteLead" :disabled="leadStore.loading" :loading="leadStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="deleteLeadsDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to remove these scheduled follow-ups? They will be rescheduled to two days ago if removed.</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="deleteLeadsDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="deleteSelectedLeads" :loading="leadStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="sendLeadsFollowUpDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to immediately send follow-ups for the selected lead(s) using AI?</span>
            </div>

            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="sendLeadsFollowUpDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="sendFollowUpsForSelectedLeads" :loading="leadStore.loading" />
            </template>
        </Dialog>

        <Dialog v-model:visible="delayFollowUpDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to delay sending follow-ups for the selected lead/s?</span>
            </div>
            <div class="flex flex-col">
                <div class="flex items-center my-3">
                    <SelectButton class="fluid" v-model="selectDelayOptionValue" :options="selectDelayOptionValues" optionLabel="name" optionValue="value" />
                </div>
                <div v-if="selectDelayOptionValue === 'schedule'">
                    <DatePicker class="fluid" :showIcon="true" showTime :showButtonBar="true" v-model="selectedDateDelayOption" :minDate="new Date()"></DatePicker>
                </div>
                <div v-else>
                    <Tag severity="info" icon="pi pi-sparkles">Let AI decide the next schedule for follow-ups.</Tag>
                </div>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="delayFollowUpDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="delaySendingFollowUps" :loading="leadStore.loading" />
            </template>
        </Dialog>
        <Dialog v-model:visible="markAsQualifiedDialog" :style="{ width: '450px' }" header="Confirm" :modal="true" :closable="!leadStore.loading">
            <div class="flex items-center gap-4">
                <i class="pi pi-exclamation-triangle !text-3xl" />
                <span>Are you sure you want to Mark the selected lead/s as {{ leadCategory !== 'qualified' ? 'Qualified' : 'Un-Qualified' }}?</span>
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="delayFollowUpDialog = false" :disabled="leadStore.loading" />
                <Button label="Yes" icon="pi pi-check" text @click="markQualifiedSelectedLeads(leadCategory !== 'qualified')" :loading="leadStore.loading" />
            </template>
        </Dialog>
        <Dialog v-model:visible="importLeadsDialog" :style="{ width: '450px' }" header="Import Leads from Excel File" :modal="true" :closable="!leadStore.loading">
            <div>
                <ImportLead />
            </div>
            <template #footer>
                <Button label="No" icon="pi pi-times" text @click="importLeadsDialog = false" :disabled="leadStore.loading" />
            </template>
        </Dialog>
    </div>
</template>
<style scoped lang="scss">
@media (max-width: 767px) {
    .search-inputs {
        flex: 1 1 100%;
        width: 100%;
    }
}
</style>
