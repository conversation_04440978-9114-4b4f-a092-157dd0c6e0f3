<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { Lead, ParamsIcon, LeadHealthCheck } from '@/entities/lead';

const emits = defineEmits<{
    openModal: [];
    closeModal: [];
}>();
const props = defineProps<{
    leadDetailsDialog: boolean;
    lead: Lead | object;
}>();

const dialog = ref(props.leadDetailsDialog);

// Sync dialog visibility with prop
watch(
    () => props.leadDetailsDialog,
    (newVal) => {
        dialog.value = newVal;
    }
);

// Emit event when dialog opens/closes
watch(
    () => dialog.value,
    (isVisible) => {
        if (isVisible) {
            emits('openModal');
        } else {
            emits('closeModal');
        }
    }
);

// Computed property to check if the device is mobile
const isMobile = ref(window.matchMedia('(max-width: 960px)').matches);

// Update isMobile value when the screen size changes
const updateIsMobile = () => {
    isMobile.value = window.matchMedia('(max-width: 960px)').matches;
};

// Add and remove resize event listener
onMounted(() => {
    window.addEventListener('resize', updateIsMobile);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
});
</script>

<template>
    <Dialog header="Lead Details" v-model:visible="dialog" :dismissableMask="true" :draggable="true" :breakpoints="{ '960px': '75vw' }" :style="{ width: '30vw' }" :modal="true" class="list-dialog">
        <ParamsIcon icon="pi-user" title="Lead" :text="[props.lead]" />
        <ParamsIcon v-if="props.lead?.lead_details" icon="pi-ellipsis-h" title="More Details..." :text="[props.lead?.lead_details]" />

        <LeadHealthCheck v-if="isMobile" :lead="props.lead" :is_popover="false" />

        <template #footer>
            <Button label="Close" class="p-button-success p-button-rounded mt-3" @click="emits('closeModal')" />
        </template>
    </Dialog>
</template>
