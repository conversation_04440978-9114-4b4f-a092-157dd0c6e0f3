<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
import { Lead, LeadBooking } from '@/entities/lead';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import { bookAppointment } from '@/features/appointment-booking/services/appointmentService';

const emits = defineEmits<{
    openModal: [];
    closeModal: [];
}>();
const props = defineProps<{
    leadBookingsDialog: boolean;
    lead: Lead | object;
}>();

const dialog = ref(props.leadBookingsDialog);
const loading = ref(false);
const leadBookingDialog = ref(false);
const currentBookingAction = ref('add');
// Local reactive copy of lead
const localLead = ref<Lead | object>({ ...props.lead, booking_action: currentBookingAction.value });

const selectedAppointment = ref(null);
const confirm = useConfirm();
const toast = useToast();

// Function to handle rebooking
const handleRebook = (appointment: any) => {
    loading.value = true;
    try {
        selectedAppointment.value = appointment;
        currentBookingAction.value = 'update';
        leadBookingDialog.value = true;
        localLead.value.address = appointment.location_string || '';
        localLead.value.booking_action = currentBookingAction.value;

        localLead.value.selectedBooking = {
            id: appointment.id,
            appointmentDate: appointment.appointmentDate,
            eventId: appointment.eventId,
            gps_coordinates: appointment.gps_coordinates,
            location_string: appointment.location_string,
            google_link: appointment.google_link,
            notes: appointment.notes,
            lead: {
                id: localLead.value.id,
                name: localLead.value.name,
                email: localLead.value.email,
                phone: localLead.value.phone,
                address: localLead.value.address,
                booking_action: localLead.value.booking_action
            },
            doorType: appointment.doorType,
            doorSize: appointment.doorSize,
            customSize: appointment.customSize,
            doorColor: appointment.doorColor,
            customColor: appointment.customColor,
            tradeIn: appointment.tradeIn
        };
    } finally {
        loading.value = false;
    }
};

// Function to handle cancellation
const handleCancel = (appointment: any) => {
    loading.value = true;
    confirm.require({
        message: 'Are you sure you want to cancel this appointment?',
        header: 'Confirmation',
        icon: 'pi pi-exclamation-triangle',
        rejectProps: {
            label: 'Cancel',
            severity: 'secondary',
            outlined: true
        },
        acceptProps: {
            label: 'Yes'
        },
        accept: async () => {
            await bookAppointment({
                ...appointment,
                lead: {
                    id: localLead.value?.id || '',
                    name: localLead.value?.name || '',
                    email: localLead.value?.email || '',
                    phone: localLead.value?.phone || '',
                    address: localLead.value?.address || '',
                    booking_action: localLead.value?.booking_action || 'add'
                },
                action: 'cancel'
            });
            localLead.value.appointmentHistory = localLead.value.appointmentHistory.filter((app) => app.id !== appointment.id);
            loading.value = false;
            toast.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted to cancel the appointment', life: 3000 });
        },
        reject: () => {
            toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected to cancel the appointment', life: 3000 });
            loading.value = false;
        }
    });
    selectedAppointment.value = appointment;
    currentBookingAction.value = 'add';
    localLead.value.booking_action = currentBookingAction.value;
};

// Function to handle adding a new appointment
const handleAddAppointment = () => {
    selectedAppointment.value = null;
    currentBookingAction.value = 'add';
    leadBookingDialog.value = true;
    localLead.value.booking_action = currentBookingAction.value;
    localLead.value.selectedBooking = null;
    localLead.value.address = props.lead?.address || '';
};

// Format date from timestamp
const formatDate = (timestamp: number) => {
    if (!timestamp) return 'No date';
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString('en-GB', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
};

// Format time from ISO string
const formatTime = (isoString: string) => {
    if (!isoString) return '';
    const date = new Date(isoString);
    return date.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
};

// Get appointment status based on date
const getAppointmentStatus = (appointment: any) => {
    if (!appointment.appointmentDate) return 'info';

    const now = Math.floor(Date.now() / 1000);
    if (appointment.appointmentDate < now) return 'success';
    if (appointment.appointmentDate - now < 86400) return 'warning'; // Within 24 hours
    return 'info';
};

// Get appointment status text
const getAppointmentStatusText = (appointment: any) => {
    if (!appointment.appointmentDate) return 'No Date';

    const now = Math.floor(Date.now() / 1000);
    if (appointment.appointmentDate < now) return 'Completed';
    if (appointment.appointmentDate - now < 86400) return 'Upcoming Soon';
    return 'Scheduled';
};

// Open calendar event in new tab
const openCalendarEvent = (url: string) => {
    if (url) window.open(url, '_blank');
};

// Open Google Maps in new tab
const openGoogleMaps = (url: string) => {
    if (url) window.open(url, '_blank');
};

// Sync dialog visibility with prop
watch(
    () => props.leadBookingsDialog,
    (newVal) => {
        console.log('leadBookingsDialog-booking', newVal);
        dialog.value = newVal;
    },
    { immediate: true, deep: true }
);

// Emit event when dialog opens/closes
watch(
    () => dialog.value,
    (isVisible) => {
        if (isVisible) {
            emits('openModal');
        } else {
            emits('closeModal');
        }
    }
);

// Sync localLead when props.lead changes
watch(
    () => props.lead,
    (newLead) => {
        localLead.value = { ...newLead };
        localLead.value.appointmentHistory = localLead.value?.appointmentHistory?.filter((app) => app && app.appointmentDate) || [];
    },
    { deep: true, immediate: true }
);

// Computed property to check if the device is mobile
const isMobile = ref(window.matchMedia('(max-width: 960px)').matches);

// Update isMobile value when the screen size changes
const updateIsMobile = () => {
    isMobile.value = window.matchMedia('(max-width: 960px)').matches;
};

// Add and remove resize event listener
onMounted(() => {
    window.addEventListener('resize', updateIsMobile);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
});

const saveFormData = (appointment: any) => {
    loading.value = true;

    try {
        if (localLead.value?.appointmentHistory) {
            // Create a new array with updated appointment data
            localLead.value.appointmentHistory = localLead.value.appointmentHistory.map((app: any) => {
                if (app.id === appointment.id) {
                    const newData = {
                        updated: true,
                        id: appointment.id,
                        appointmentDate: appointment.appointmentDate,
                        eventId: appointment.eventId,
                        gps_coordinates: appointment.gps_coordinates,
                        location_string: appointment.location_string,
                        google_link: appointment.google_link,
                        notes: appointment.notes,
                        lead: {
                            id: localLead.value.id,
                            name: localLead.value.name,
                            email: localLead.value.email,
                            phone: localLead.value.phone,
                            address: localLead.value.address,
                            booking_action: localLead.value.booking_action
                        },
                        doorType: appointment.doorType,
                        doorSize: appointment.doorSize,
                        customSize: appointment.customSize,
                        doorColor: appointment.doorColor,
                        customColor: appointment.customColor,
                        tradeIn: appointment.tradeIn
                    };

                    // Return updated appointment with calendar_event
                    return {
                        ...newData,
                        calendar_event: {
                            ...app.calendar_event, // Keep existing calendar event data
                            location: appointment.location_string, // Update location
                            start: { dateTime: new Date(appointment.appointmentDate * 1000).toISOString() },
                            end: { dateTime: new Date((appointment.appointmentDate + 3600) * 1000).toISOString() }
                        }
                    };
                } else {
                    return app;
                }
            });

            // For new appointments, add them to the history
            if (!localLead.value.appointmentHistory.some((app) => app.id === appointment.id) && appointment.id) {
                const newAppointment = {
                    updated: true,
                    id: appointment.id,
                    appointmentDate: appointment.appointmentDate,
                    eventId: appointment.eventId,
                    gps_coordinates: appointment.gps_coordinates,
                    location_string: appointment.location_string,
                    google_link: appointment.google_link,
                    notes: appointment.notes,
                    lead: {
                        id: localLead.value.id,
                        name: localLead.value.name,
                        email: localLead.value.email,
                        phone: localLead.value.phone,
                        address: localLead.value.address,
                        booking_action: localLead.value.booking_action
                    },
                    doorType: appointment.doorType,
                    doorSize: appointment.doorSize,
                    customSize: appointment.customSize,
                    doorColor: appointment.doorColor,
                    customColor: appointment.customColor,
                    tradeIn: appointment.tradeIn,
                    calendar_event: {
                        location: appointment.location_string,
                        start: { dateTime: new Date(appointment.appointmentDate * 1000).toISOString() },
                        end: { dateTime: new Date((appointment.appointmentDate + 3600) * 1000).toISOString() }
                    }
                };

                localLead.value.appointmentHistory.push(newAppointment);
            }
        }

        // Show success toast
        toast.add({
            severity: 'success',
            summary: 'Appointment Saved',
            detail: 'The appointment has been successfully saved',
            life: 3000
        });
    } catch (error) {
        // Show error toast
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save appointment',
            life: 3000
        });
        console.error('Error saving appointment:', error);
    } finally {
        loading.value = false;
    }
};

// Add function to check for overlapping appointments
const checkOverlappingAppointments = (appointment: any) => {
    if (!appointment || !appointment.appointmentDate || !localLead.value?.appointmentHistory) {
        return false;
    }

    const appointmentTime = appointment.appointmentDate;
    const thirtyMinutesInSeconds = 30 * 60; // 30 minutes in seconds

    // Check if there are other appointments within 30 minutes
    return localLead.value.appointmentHistory.some((app) => {
        // Skip comparing with itself
        if (app.id === appointment.id) return false;

        // Check if appointment times are within 30 minutes of each other
        return app.appointmentDate && Math.abs(app.appointmentDate - appointmentTime) < thirtyMinutesInSeconds;
    });
};

// Add function to get overlap warning text
const getOverlapWarningText = (appointment: any) => {
    if (!checkOverlappingAppointments(appointment)) return null;

    return 'Overlapping appointment detected';
};
</script>

<template>
    <Dialog
        header="Lead Bookings"
        :baseZIndex="2105"
        v-model:visible="dialog"
        :dismissableMask="!loading"
        :closable="!loading"
        :draggable="!loading"
        :breakpoints="{ '960px': '75vw' }"
        :style="{ width: '30vw', zIndex: 2100 }"
        :modal="true"
        class="list-dialog"
    >
        <!-- Loading overlay -->
        <div v-if="loading" class="loading-overlay">
            <ProgressSpinner strokeWidth="4" class="loading-spinner" />
        </div>

        <!-- Add New Appointment Button -->
        <div class="add-appointment-container" v-if="localLead.appointmentHistory && localLead.appointmentHistory.length > 0">
            <Button icon="pi pi-plus" label="Add New Appointment" class="p-button-success p-button-rounded" @click="handleAddAppointment()" :disabled="loading" />
        </div>

        <div v-if="localLead.appointmentHistory && localLead.appointmentHistory.length > 0" class="appointment-history">
            <Timeline :value="localLead.appointmentHistory" class="appointment-timeline" unstyled>
                <template #content="slotProps">
                    <Card class="appointment-card">
                        <template #header>
                            <div class="appointment-header">
                                <div class="appointment-date">
                                    <i class="pi pi-calendar mr-2"></i>
                                    <span>{{ formatDate(slotProps.item.appointmentDate) }}</span>
                                </div>
                                <div class="appointment-status-container">
                                    <!-- Overlap warning badge -->
                                    <Badge v-if="checkOverlappingAppointments(slotProps.item)" severity="warning" value="!" class="overlap-badge mr-2" v-tooltip.top="getOverlapWarningText(slotProps.item)" />
                                    <!-- Regular status tag -->
                                    <Tag :severity="getAppointmentStatus(slotProps.item)" class="appointment-status">
                                        {{ getAppointmentStatusText(slotProps.item) }}
                                    </Tag>
                                </div>
                            </div>
                        </template>

                        <template #title>
                            <div class="appointment-title">
                                <h3>{{ slotProps.item.calendar_event?.summary || 'Appointment' }}</h3>
                            </div>
                        </template>

                        <template #subtitle>
                            <div class="appointment-location">
                                <i class="pi pi-map-marker mr-2"></i>
                                <span>{{ slotProps.item.location_string || 'No location specified' }}</span>
                            </div>
                        </template>

                        <template #content>
                            <div class="appointment-details">
                                <div class="appointment-time">
                                    <i class="pi pi-clock mr-2"></i>
                                    <span>{{ formatTime(slotProps.item.calendar_event?.start?.dateTime) }} - {{ formatTime(slotProps.item.calendar_event?.end?.dateTime) }}</span>
                                </div>

                                <!-- Overlap warning message -->
                                <Message v-if="checkOverlappingAppointments(slotProps.item)" severity="warning" class="overlap-warning mt-2">
                                    <div class="flex align-items-center">
                                        <i class="pi pi-exclamation-triangle mr-2"></i>
                                        <span>This appointment overlaps with another appointment within 30 minutes.</span>
                                    </div>
                                </Message>

                                <Divider />

                                <div v-if="slotProps.item.doorType || slotProps.item.doorSize || slotProps.item.doorColor" class="product-details">
                                    <h4>Product Details</h4>
                                    <div class="product-specs">
                                        <div v-if="slotProps.item.doorType" class="spec-item">
                                            <span class="spec-label">Door Type:</span>
                                            <span class="spec-value">{{ slotProps.item.doorType }}</span>
                                        </div>
                                        <div v-if="slotProps.item.doorSize" class="spec-item">
                                            <span class="spec-label">Size:</span>
                                            <span class="spec-value">{{ slotProps.item.doorSize }}{{ slotProps.item.customSize ? ` (${slotProps.item.customSize})` : '' }}</span>
                                        </div>
                                        <div v-if="slotProps.item.doorColor" class="spec-item">
                                            <span class="spec-label">Color:</span>
                                            <span class="spec-value">{{ slotProps.item.doorColor }}{{ slotProps.item.customColor ? ` (${slotProps.item.customColor})` : '' }}</span>
                                        </div>
                                        <div v-if="slotProps.item.tradeIn" class="spec-item">
                                            <span class="spec-label">Trade-In:</span>
                                            <span class="spec-value">Yes</span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="slotProps.item.notes && slotProps.item.notes.length > 0" class="appointment-notes">
                                    <h4>Notes</h4>
                                    <div v-for="(note, index) in slotProps.item.notes" :key="index" class="note-item">
                                        <i class="pi pi-info-circle mr-2"></i>
                                        <span v-html="note" />
                                    </div>
                                </div>
                            </div>
                        </template>

                        <template #footer>
                            <div class="appointment-actions">
                                <Button
                                    v-if="slotProps.item.calendar_event?.htmlLink && !slotProps.item.updated"
                                    icon="pi pi-calendar"
                                    label="View in Calendar"
                                    class="p-button-outlined p-button-sm"
                                    @click="openCalendarEvent(slotProps.item.calendar_event.htmlLink)"
                                    :disabled="loading"
                                />

                                <Button v-if="slotProps.item.google_link" icon="pi pi-map" label="View on Map" class="p-button-outlined p-button-sm" @click="openGoogleMaps(slotProps.item.google_link)" :disabled="loading" />

                                <!-- Add spacing -->
                                <div class="flex-grow-1"></div>

                                <!-- Cancel and Rebook buttons -->
                                <Button icon="pi pi-times" label="Cancel" severity="danger" class="p-button-outlined p-button-sm" @click="handleCancel(slotProps.item)" :disabled="loading || getAppointmentStatus(slotProps.item) === 'success'" />

                                <Button icon="pi pi-refresh" label="Rebook" severity="info" class="p-button-outlined p-button-sm" @click="handleRebook(slotProps.item)" :disabled="loading" />
                            </div>
                        </template>
                    </Card>
                </template>
            </Timeline>
        </div>
        <div v-else class="no-appointments">
            <i class="pi pi-calendar-times" style="font-size: 3rem"></i>
            <p>No appointment history available</p>
            <Button icon="pi pi-plus" label="Schedule First Appointment" class="p-button-success p-button-rounded mt-3" @click="handleAddAppointment()" :disabled="loading" />
        </div>

        <template #footer>
            <Button label="Close" @click="emits('closeModal')" :disabled="loading" />
        </template>
    </Dialog>

    <!-- Add LeadBooking component -->
    <LeadBooking
        v-if="props.lead"
        :lead="{
            id: localLead?.id || '',
            name: localLead?.name || '',
            email: localLead?.email || '',
            phone: localLead?.phone || '',
            address: localLead?.address || '',
            selectedBooking: localLead?.selectedBooking || null,
            booking_action: localLead?.booking_action || 'add'
        }"
        @saveFormData="saveFormData"
        :leadBookingDialog="leadBookingDialog"
        @openModal="leadBookingDialog = true"
        @closeModal="leadBookingDialog = false"
    />

    <!-- Add ConfirmDialog component -->
    <ConfirmDialog></ConfirmDialog>
</template>

<style scoped>
.appointment-history {
    padding: 1rem 0;
}

.appointment-timeline {
    margin-top: 1rem;
}

.appointment-card {
    margin-bottom: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition:
        transform 0.2s,
        box-shadow 0.2s;
}

.appointment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--surface-section);
    border-radius: 8px 8px 0 0;
}

.appointment-date {
    display: flex;
    align-items: center;
    font-weight: 600;
}

.appointment-status-container {
    display: flex;
    align-items: center;
}

.appointment-status {
    font-size: 0.8rem;
}

.overlap-badge {
    margin-right: 0.5rem;
}

.appointment-title h3 {
    margin: 0.5rem 0;
    font-size: 1.2rem;
    color: var(--text-color);
}

.appointment-location {
    display: flex;
    align-items: center;
    color: var(--text-color-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.appointment-time {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin: 0.5rem 0;
}

.product-details,
.appointment-notes {
    margin-top: 1rem;
}

.product-details h4,
.appointment-notes h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.product-specs {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.5rem;
}

.spec-item {
    display: flex;
    flex-direction: column;
    padding: 0.5rem;
    background-color: var(--surface-ground);
    border-radius: 4px;
}

.spec-label {
    font-size: 0.8rem;
    color: var(--text-color-secondary);
}

.spec-value {
    font-weight: 600;
    margin-top: 0.25rem;
}

.note-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: var(--surface-hover);
    border-radius: 4px;
}

.appointment-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: space-between;
    flex-wrap: wrap;
}

.flex-grow-1 {
    flex-grow: 1;
}

@media (max-width: 768px) {
    .appointment-actions {
        flex-direction: column;
    }

    .appointment-actions .p-button {
        width: 100%;
        margin-bottom: 0.25rem;
    }

    .flex-grow-1 {
        display: none;
    }
}

.no-appointments {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--text-color-secondary);
    text-align: center;
}

.add-appointment-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    color: var(--text-color-secondary);
    text-align: center;
}

.mt-3 {
    margin-top: 1rem;
}

/* Dark mode support */
:deep(.p-dialog-content) {
    position: relative;
}

@media (prefers-color-scheme: dark) {
    .loading-overlay {
        background-color: rgba(0, 0, 0, 0.7);
    }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .loading-spinner {
        width: 40px;
        height: 40px;
    }
}
</style>
