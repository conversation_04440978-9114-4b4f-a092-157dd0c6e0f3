<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { computeLeadStatus, getLeadButtonSeverity, getLeadIcon, Lead, LeadHealthCheck } from '@/entities/lead';

const emits = defineEmits<{
    leadAiDialog: (lead: Lead | object) => void;
    leadConversationsDialog: (lead: Lead | object) => void;
    leadNotesDialog: (lead: Lead | object) => void;
    leadDetailsDialog: (lead: Lead | object) => void;
    leadAnalyzeDialog: (lead: Lead | object) => void;
}>();
const props = defineProps<{
    error?: any | null | undefined;
    loading?: boolean;
    lead: Lead | object;
    hiddenTools?: string[];
}>();

// Local reactive copy of lead
const localLead = ref<Lead | object>({ ...props.lead });

// Sync localLead when props.lead changes
watch(
    () => props.lead,
    (newLead) => {
        localLead.value = { ...newLead };
    },
    { deep: true }
);

const leadStatus = computed(() => computeLeadStatus(localLead.value));

const leadIcon = computed(() => {
    return getLeadIcon(leadStatus.value);
});

const leadButtonIcon = computed(() => leadIcon.value);
const leadButtonSeverity = computed(() => {
    return getLeadButtonSeverity(leadStatus.value);
});
</script>

<template>
    <div class="flex gap-1 flex-wrap">
        <LeadHealthCheck :lead="props.lead" :is_popover="true">
            <Button :icon="leadButtonIcon" :severity="leadButtonSeverity" v-tooltip="'Lead Analysis'" placeholder="Top" v-if="typeof localLead?.ai_conversation_summary === 'object'" outlined rounded @click="emits('leadAiDialog', localLead)" />
        </LeadHealthCheck>
        <Button icon="pi pi-sparkles" v-if="!hiddenTools?.includes('analyze')" outlined rounded @click="emits('leadAnalyzeDialog', localLead)" v-tooltip="'Analyze this lead using AI'" placeholder="Top" />
        <Button icon="pi pi-eye" v-tooltip="'Lead Details'" placeholder="Top" outlined rounded class="mr-1" @click="emits('leadDetailsDialog', localLead)" v-if="!hiddenTools?.includes('details')" />
        <OverlayBadge size="small" severity="contrast" :value="localLead.conversations_count || 0">
            <Button icon="pi pi-comments" v-tooltip="'Conversations'" placeholder="Top" outlined rounded @click="emits('leadConversationsDialog', localLead)" class="mr-1" />
        </OverlayBadge>
        <OverlayBadge size="small" severity="contrast" :value="localLead.notes_count || 0" v-if="!hiddenTools?.includes('notes')">
            <Button icon="pi pi-file-edit" v-tooltip="'Notes'" placeholder="Top" outlined rounded @click="emits('leadNotesDialog', localLead)" />
        </OverlayBadge>
    </div>
</template>
