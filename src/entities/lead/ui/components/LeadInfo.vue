<script setup lang="ts">
import { Lead, LeadList, LeadHealthCheck, ParamsIcon, LeadPopOverDetails, computeLeadStatus, getLeadIconColor, LeadBookings } from '@/entities/lead';
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';

const emits = defineEmits<{
    openModal: [];
    closeModal: [];
    leadAiDialog: [];
    leadConversationsDialog: [];
    leadNotesDialog: [];
    leadDetailsDialog: [];
    leadBookingDetailDialog: (trigger: boolean) => void;
}>();
const props = defineProps<{
    leadInfoDialog: boolean;
    leadBookingDetailDialog?: boolean;
    lead: Lead | object;
    hiddenTools?: string[];
}>();

const dialogList = ref(false);
const combinedLeads = ref([]);

const leadBookingDialog = ref(props.leadBookingDetailDialog);

const dialogInfo = computed(() => {
    console.log('leadBookingDialog', leadBookingDialog.value);
    leadBookingDialog.value = props.leadBookingDetailDialog;
    return props.leadInfoDialog || dialogList.value === true || leadBookingDialog.value === true;
});
// Create a computed property for the combined dialog state
const dialog = computed(() => {
    return dialogInfo.value;
});

// Watch for changes in the dialog state
watch(dialog, (isVisible) => {
    if (isVisible) {
        emits('openModal');
    } else {
        closeAllModals();
    }
});
watch(
    () => props.leadBookingDetailDialog,
    (isVisible) => {
        // Properly handle undefined and boolean values
        if (isVisible === undefined) {
            // Keep current state if undefined
            return;
        }
        // Set to the actual value (true or false)
        leadBookingDialog.value = !isVisible;
    },
    { immediate: true, deep: true }
);
watch(
    () => dialogList.value,
    (isVisible) => {
        combinedLeads.value = [];
        if (props.lead?.id) combinedLeads.value.push(props.lead.id);
        if (props.lead?.email) combinedLeads.value.push(props.lead.email);
        if (props.lead?.phone) combinedLeads.value.push(props.lead.phone);
        // showCombinedLeads(isVisible)
    }
);

const getLeadSourceIcon = (status: string): string | null => {
    switch (status) {
        case 'call':
        case 'ai_call':
            return 'phone';

        case 'email':
            return 'envelope';

        case 'gravity_form':
            return 'calculator';
        case 'google_calendar_booking':
        case 'appointment_booking':
            return 'pi pi-calendar-plus';

        default:
            return status;
    }
};

// Computed property to check if the device is mobile
const isMobile = ref(window.matchMedia('(max-width: 960px)').matches);

const items = ref([
    {
        label: 'Details',
        icon: 'pi pi-eye',
        show: isMobile.value,
        command: () => {
            closeAllModals();
            emits('leadDetailsDialog');
        }
    },
    {
        label: 'Analysis',
        icon: 'pi pi-lightbulb',
        show: true,
        command: () => {
            closeAllModals();
            emits('leadAiDialog');
        }
    },
    {
        label: 'Conversations',
        icon: 'pi pi-comments',
        show: true,
        command: () => {
            closeAllModals();
            emits('leadConversationsDialog');
        }
    },
    {
        label: 'Notes',
        icon: 'pi pi-file-edit',
        show: true,
        command: () => {
            closeAllModals();
            emits('leadNotesDialog');
        }
    },
    {
        label: 'Combined Leads',
        icon: 'pi pi-link',
        show: props?.hiddenTools?.includes('combined') ? false : true,
        command: () => {
            closeAllModals();
            dialogList.value = true;
        }
    },
    {
        label: 'Book Appointment',
        icon: 'pi pi-calendar-plus',
        show: props?.hiddenTools?.includes('booking') ? false : true,
        command: () => {
            closeAllModals();
            showBooking(true);
        }
    }
]);

// Update isMobile value when the screen size changes
const updateIsMobile = () => {
    isMobile.value = window.matchMedia('(max-width: 960px)').matches;
    items.value[0].show = isMobile.value;
};

const showBooking = (trigger: boolean) => {
    leadBookingDialog.value = trigger;
    emits('leadBookingDetailDialog', trigger);
    if (trigger === false) return;
    emits('closeModal');

    dialogList.value = false;
};

// Add and remove resize event listener
onMounted(() => {
    window.addEventListener('resize', updateIsMobile);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
});

// Computed properties
const leadStatus = computed(() => computeLeadStatus(props.lead));

const leadIconColor = computed(() => {
    return getLeadIconColor(leadStatus.value);
});

const closeAllModals = () => {
    emits('closeModal');
    dialogList.value = false;
    leadBookingDialog.value = false;
    emits('leadBookingDetailDialog', false);
};
</script>

<template>
    <SpeedDial
        :model="items"
        v-if="isMobile && (dialogInfo || dialogList || leadBookingDialog)"
        :radius="100"
        :transitionDelay="80"
        :tooltipOptions="{ position: 'right' }"
        direction="down"
        :mask="false"
        style="position: fixed; left: calc(0% + 1rem); top: calc(0% + 1rem); z-index: 9999"
    >
        <template #item="{ item, toggleCallback }">
            <div v-show="item.show" class="flex flex-col items-center bg-white dark:bg-black justify-between gap-2 p-2 border rounded border-surface-200 dark:border-surface-700 w-20 cursor-pointer" @click="toggleCallback">
                <span :class="item.icon" />
            </div>
        </template>
    </SpeedDial>
    <Drawer v-if="!isMobile" class="lead-info" v-model:visible="dialog" position="left" style="max-height: calc(100vh); height: auto; overflow: auto" :modal="false" :dismissable="true">
        <template #header>
            <div class="w-full flex items-center gap-2 !text-1xl">
                <Avatar style="min-width: 40px" size="large" :label="`${props.lead.name?.charAt(0) || props.lead.email?.charAt(0) || 'L'}`" shape="circle" />
                <span class="font-bold">{{ props.lead.name || props.lead.email || props.lead.phone }}</span>
            </div>
        </template>
        <Divider type="dashed" style="margin-top: 2px" />
        <ParamsIcon
            :icon="`pi-${getLeadSourceIcon(props.lead.lead_source)}`"
            :title="['google_calendar_booking', 'appointment_booking'].includes(props.lead.lead_source) ? 'Booking' : props.lead.lead_source"
            :icon_color="`${leadIconColor}-500`"
            :icon_text_color="`${leadIconColor}-100`"
            :hasCombineLeads="props.lead?.email?.length > 0 || props.lead?.phone?.length > 0"
            :text="[
                {
                    ...Object.fromEntries(Object.entries(props.lead).filter(([key]) => !['lead_source', 'source_id', 'is_qualified'].includes(key)))
                }
            ]"
        />
        <Divider type="dashed" style="margin-top: -25px" v-if="!props?.hiddenTools?.includes('booking')" />
        <Button label="Book Appointment" icon="pi pi-calendar-plus" severity="warn" @click="showBooking(true)" class="mb-3" v-if="!props?.hiddenTools?.includes('booking')" />

        <Divider type="dashed" style="margin-top: 2px" v-if="lead?.ai_conversation_summary" />
        <LeadHealthCheck :lead="props.lead" :is_popover="false" />
        <Divider type="dashed" style="margin-top: 2px" />
        <template #footer>
            <div class="flex gap-4">
                <a v-for="(item, idx) in items" :key="idx" v-show="item.show" class="flex flex-col items-center justify-between gap-2 p-2 border rounded border-surface-200 dark:border-surface-700 w-20 cursor-pointer" @click="item.command">
                    <LeadPopOverDetails :is_responsive="false" :is_component="false" :tooltip-text="item.label">
                        <span style="z-index: 2103" :class="item.icon" />
                    </LeadPopOverDetails>
                </a>
            </div>
        </template>
    </Drawer>

    <Dialog
        v-if="combinedLeads.length"
        header="Combined Leads"
        v-model:visible="dialogList"
        :dismissableMask="true"
        :draggable="true"
        class="list-dialog"
        :breakpoints="{ '960px': '90vw' }"
        :position="isMobile ? 'center' : 'right'"
        :style="{ width: 'calc(100vw - 320px)' }"
        :modal="true"
    >
        <LeadList :combinedLead="combinedLeads" />
    </Dialog>

    <LeadBookings v-if="props.lead" :lead="props.lead" :leadBookingsDialog="leadBookingDialog" @openModal="showBooking(true)" @closeModal="showBooking(false)" />
</template>

<style lang="scss"></style>
