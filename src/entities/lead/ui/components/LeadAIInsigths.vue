<script setup lang="ts">
import { ref, watch } from 'vue';
import { Lead, ParamsIcon } from '@/entities/lead';

const emits = defineEmits<{
    openModal: [];
    closeModal: [];
}>();
const props = defineProps<{
    leadAiDialog: boolean;
    lead: Lead | object;
}>();

const dialog = ref(props.leadAiDialog);

// Sync dialog visibility with prop
watch(
    () => props.leadAiDialog,
    (newVal) => {
        dialog.value = newVal;
    }
);

// Emit event when dialog opens/closes
watch(
    () => dialog.value,
    (isVisible) => {
        if (isVisible) {
            emits('openModal');
        } else {
            emits('closeModal');
        }
    }
);
</script>

<template>
    <Dialog header="AI Insights" v-model:visible="dialog" :dismissableMask="true" :draggable="true" :breakpoints="{ '960px': '75vw' }" :style="{ width: '30vw' }" :modal="true" class="list-dialog">
        <ParamsIcon icon="pi-sparkles" title="Conversation Summary" :text="props.lead.ai_conversation_summary?.conversation_summary || props.lead.ai_conversation_summary?.ai_conversation_summary" />

        <ParamsIcon
            icon="pi-calendar-plus"
            title="Follow-up Review"
            :text="[
                {
                    strategy: props.lead.ai_conversation_summary?.sales_advice?.follow_up_strategies[0]?.strategy,
                    outcome: props.lead?.ai_conversation_summary?.lead_source_classification,
                    lead_interest: props.lead?.ai_conversation_summary?.lead_interest,
                    suggested_action:
                        props.lead?.lead_source === 'call' || props.lead?.lead_source === 'ai_call'
                            ? 'AI Call or Callback'
                            : props.lead?.lead_source === 'facebook'
                              ? 'Facebook Messenger'
                              : props.lead?.lead_source === 'gravity_form'
                                ? 'Email'
                                : props.lead?.lead_source,
                    follow_up_date: props.lead?.to_followup_date
                }
            ]"
        />
        <ParamsIcon icon="pi-lightbulb" title="Insights For Conversion" :text="props.lead.ai_conversation_summary?.sales_advice?.insights_for_conversion" />
        <ParamsIcon icon="pi-bullseye" title="Qualification Strategy" :text="props.lead.ai_conversation_summary?.sales_advice?.qualification_strategy" />

        <template #footer>
            <Button class="p-button-success p-button-rounded mt-3" label="Close" @click="emits('closeModal')" />
        </template>
    </Dialog>
</template>
