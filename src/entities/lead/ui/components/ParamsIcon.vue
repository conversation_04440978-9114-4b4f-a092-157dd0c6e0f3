<script setup lang="ts">
import { ParamsDetails } from '@/entities/lead';

const emits = defineEmits<{
    showCombinedLeads: (trigger: boolean) => void;
}>();

const props = withDefaults(
    defineProps<{
        hasCombineLeads?: boolean;
        title?: string;
        text?: string | object;
        icon?: string;
        icon_color?: string;
        icon_text_color?: string;
        no_border?: boolean;
    }>(),
    {
        hasCombineLeads: false,
        title: 'N/A',
        text: 'N/A',
        icon: 'pi-users',
        icon_color: 'indigo-200',
        icon_text_color: 'indigo-700',
        no_border: false
    }
);
const showCombinedLeads = (trigger) => {
    emits('showCombinedLeads', trigger);
};
</script>

<template>
    <div :class="`col-span-12 md:col-span-12 lg:col-span-4 p-0 lg:pr-8 ${props.no_border ? '' : ' lg:pb-8 mb-4 lg:mt-0'} `" v-if="props.text">
        <div :class="typeof props.text === 'object' || props.no_border ? '' : 'border-surface-300 dark:border-surface-500 border-b'">
            <div class="bg-surface-0 dark:bg-surface-900">
                <div class="flex items-center justify-start">
                    <div :class="`flex items-center justify-center bg-${props.icon_color} mb-2`" style="width: 3rem; height: 3rem; border-radius: 10px">
                        <i :class="`pi ${props.icon} !text-2xl text-${props.icon_text_color}`"></i>
                    </div>
                    <h2 class="ml-4 mb-2 text-surface-900 dark:text-surface-0 capitalize">{{ props.title }}</h2>
                </div>
                <div v-if="!props.hasCombineLeads">
                    <ParamsDetails v-if="typeof props.text === 'object'" :properties="props.text" />
                    <div v-else :class="`text-surface-600 dark:text-surface-200 ${props.no_border ? '' : 'pb-2'}`">{{ props.text }}</div>
                </div>
                <div v-else>
                    <ParamsDetails v-if="typeof props.text === 'object'" :properties="props.text" @showCombinedLeads="showCombinedLeads" />
                    <div v-else :class="`text-surface-600 dark:text-surface-200 ${props.no_border ? '' : 'pb-2'}`">{{ props.text }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss"></style>
