<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
import { Lead } from '@/entities/lead';
import AppointmentBooking from '@/features/appointment-booking/AppointmentBooking.vue';

const emits = defineEmits<{
    openModal: [];
    closeModal: [];
    saveFormData: (formData: any | object) => void;
}>();
const props = defineProps<{
    leadBookingDialog: boolean;
    lead: Lead | object;
}>();

const dialog = ref(props.leadBookingDialog);

// Sync dialog visibility with prop
watch(
    () => props.leadBookingDialog,
    (newVal) => {
        dialog.value = newVal;
    }
);

// Emit event when dialog opens/closes
watch(
    () => dialog.value,
    (isVisible) => {
        if (isVisible) {
            emits('openModal');
        } else {
            emits('closeModal');
        }
    }
);

// Computed property to check if the device is mobile
const isMobile = ref(window.matchMedia('(max-width: 960px)').matches);

// Update isMobile value when the screen size changes
const updateIsMobile = () => {
    isMobile.value = window.matchMedia('(max-width: 960px)').matches;
};

// Add and remove resize event listener
onMounted(() => {
    window.addEventListener('resize', updateIsMobile);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
});

// Set dialog title based on booking action
const dialogTitle = computed(() => {
    switch (props.lead.booking_action) {
        case 'update':
            return 'Reschedule Appointment';
        case 'cancel':
            return 'Cancel Appointment';
        default:
            return 'Book Appointment';
    }
});

// Emit event when form data is saved
const saveFormData = (data) => {
    dialog.value = true;
    emits('saveFormData', data);
};
// Close dialog and emit event
const closeModal = (data: any) => {
    dialog.value = false;
    emits('saveFormData', data);
};
</script>

<template>
    <Dialog :header="dialogTitle" :contentStyle="{ padding: '0 !important' }" v-model:visible="dialog" :dismissableMask="true" :draggable="true" :breakpoints="{ '960px': '75vw' }" :modal="true">
        <AppointmentBooking :isEmbedded="true" @closeModal="closeModal" @saveFormData="saveFormData" :leadData="props.lead" />
    </Dialog>
</template>
