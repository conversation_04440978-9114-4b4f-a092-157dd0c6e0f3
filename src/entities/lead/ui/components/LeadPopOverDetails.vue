<script setup lang="ts">
import { ref, nextTick, computed } from 'vue';

import ParamsIcon from './ParamsIcon.vue';
import { Lead } from '@/entities/lead';

// Define props
interface ParamTextItem {
    [key: string]: any;
}

const props = withDefaults(
    defineProps<{
        is_component?: boolean; // Icon for ParamsIcon
        icon?: string; // Icon for ParamsIcon
        icon_color?: string; // Icon for ParamsIcon
        is_responsive?: boolean; // Icon for ParamsIcon
        icon_text_color?: string; // Icon for ParamsIcon
        title?: string; // Icon for ParamsIcon
        lead?: Lead | object; // Icon for ParamsIcon
        paramTextItems?: ParamTextItem[]; // Customizable text items for ParamsIcon
        tooltipText?: string; // Customizable text items for ParamsIcon
    }>(),
    {
        is_component: true,
        is_responsive: true,
        tooltipText: ''
    }
);

// Refs
const opLeadInterest = ref<any | null>(null);
const leadInterestTrigger = ref<HTMLElement | null>(null);
const isMouseOverLeadInterest = ref<boolean>(false);
const loadDataOp = ref<any | null>(null); // Will be set dynamically from data prop
const details = ref<any | null>(props.paramTextItems); // Will be set dynamically from data prop
details.value = details.value?.filter((item) => item && Object.keys(item).length > 0 && Object.values(item)[0] !== null);

const toggleOpLeadInterest = (event: MouseEvent, data?: any) => {
    isMouseOverLeadInterest.value = true;
    if (data) loadDataOp.value = data;

    const panel = opLeadInterest.value;

    if (!panel) return;
    panel.hide();
    if (panel.visible) {
        panel.hide();
        nextTick(() => {
            panel.show(event);
        });
    } else {
        panel.show(event);
    }
};

const onMouseLeaveLeadInterest = () => {
    isMouseOverLeadInterest.value = false;

    const panel = opLeadInterest.value;
    if (!panel) return;

    if (panel?.visible) {
        panel.hide();
    }
};

const breakpoints = computed(() => {
    return props.is_responsive ? { '2000px': '30vw', '960px': '50vw', '640px': '90vw' } : {};
});
</script>

<template>
    <div v-if="props.is_component" ref="leadInterestTrigger" @mouseover="toggleOpLeadInterest($event, props.lead)" @mouseleave="onMouseLeaveLeadInterest">
        <OverlayPanel ref="opLeadInterest" :breakpoints="breakpoints" v-if="details">
            <ParamsIcon :icon="props.icon" :title="props.title" :text="details" :icon_color="props.icon_color" :icon_text_color="props.icon_text_color" />
        </OverlayPanel>
        <slot></slot>
    </div>
    <span v-else ref="leadInterestTrigger" @mouseover="toggleOpLeadInterest($event)" @mouseleave="onMouseLeaveLeadInterest">
        <OverlayPanel ref="opLeadInterest" :baseZIndex="3000" :breakpoints="breakpoints">
            {{ props.tooltipText }}
        </OverlayPanel>
        <slot></slot>
    </span>
</template>
