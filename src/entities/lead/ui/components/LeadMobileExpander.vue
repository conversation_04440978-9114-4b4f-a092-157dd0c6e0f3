<script setup lang="ts">
import { computeLeadStatus, getHumanReadableDate, getLeadIconColor, Lead, <PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>, LeadStatusView, LeadTools } from '@/entities/lead';
import { computed } from 'vue';

const emits = defineEmits<{
    leadAiDialog: (lead: Lead | object) => void;
    leadConversationsDialog: (lead: Lead | object) => void;
    leadNotesDialog: (lead: Lead | object) => void;
    leadDetailsDialog: (lead: Lead | object) => void;
    leadAnalyzeDialog: (lead: Lead | object) => void;
    leadEditDialog: (lead: Lead | object) => void;
    leadDeleteDialog: (lead: Lead | object) => void;
}>();

const props = defineProps<{
    lead: Lead | object;
    hiddenTools?: string[];
}>();

const getLeadActionLabel = (status: string): string | null => {
    switch (status) {
        case 'inquired':
            return 'primary';

        case 'quoted':
            return 'warn';

        case 'booked':
            return 'info';

        case 'converted':
            return 'success';

        default:
            return null;
    }
};

const getLeadSourceIcon = (status: string): string | null => {
    switch (status) {
        case 'call':
        case 'ai_call':
            return 'phone';

        case 'email':
            return 'envelope';

        case 'gravity_form':
            return 'calculator';

        case 'google_calendar_booking':
        case 'appointment_booking':
            return 'pi pi-calendar-plus';

        default:
            return status;
    }
};
</script>

<template>
    <div class="expanded-rows grid p-4 border-top-1 surface-border dark:bg-gray-900 dark:text-white text-sm">
        <!-- Lead Tools -->
        <div class="col-12 md:col-6 flex flex-column gap-2">
            <h6 class="mb-2 font-semibold text-md dark:text-gray-100">Lead Tools</h6>
            <div class="flex flex-wrap gap-2">
                <LeadTools
                    :lead="props.lead"
                    @leadAiDialog="emits('leadAiDialog', props.lead)"
                    @leadConversationsDialog="emits('leadConversationsDialog', props.lead)"
                    @leadNotesDialog="emits('leadNotesDialog', props.lead)"
                    @leadDetailsDialog="emits('leadDetailsDialog', props.lead)"
                    @leadAnalyzeDialog="emits('leadAnalyzeDialog', props.lead)"
                    :hiddenTools="props.hiddenTools"
                />
            </div>
        </div>

        <!-- Address -->
        <div class="col-12 md:col-6 flex flex-column gap-2" v-if="props.lead?.address || props.lead?.ai_conversation_summary?.address">
            <h6 class="mb-2 font-semibold text-md dark:text-gray-100">Address</h6>
            <p class="text-sm dark:text-gray-300">{{ props.lead.address || props.lead.ai_conversation_summary.address }}</p>
        </div>

        <!-- Last Message -->
        <div class="col-12 md:col-6 flex flex-column gap-2">
            <h6 class="mb-2 font-semibold text-md dark:text-gray-100">Last Message</h6>
            <p class="text-sm dark:text-gray-300">
                {{ getHumanReadableDate(props.lead.lastMessageTime) }}
            </p>
        </div>

        <!-- Lead Source -->
        <div class="col-12 md:col-6 flex flex-column gap-2">
            <h6 class="mb-2 font-semibold text-md dark:text-gray-100">Lead Source</h6>

            <span class="capitalize">
                <i :class="`pi pi-${getLeadSourceIcon(props.lead.lead_source)} mr-2`" />
                {{ props.lead.lead_source.replace(/_/g, ' ') }}
            </span>
        </div>

        <!-- Lead Action -->
        <div class="col-12 md:col-6 flex flex-column gap-2">
            <h6 class="mb-2 font-semibold text-md dark:text-gray-100">Lead Action</h6>
            <Tag :value="props.lead.lead_actions" :severity="getLeadActionLabel(props.lead.lead_actions)" class="w-fit text-smdark:bg-gray-800 dark:text-gray-200" />
        </div>

        <!-- Lead Status -->
        <div class="col-12 md:col-6 flex flex-column gap-2">
            <h6 class="mb-2 font-semibold text-md dark:text-gray-100">Lead Status</h6>
            <LeadStatusView :lead="props.lead" />
        </div>

        <!-- Actions -->
        <div class="col-12 flex flex-wrap gap-2">
            <div class="flex flex-wrap gap-2">
                <Button icon="pi pi-pencil" label="Edit" outlined @click="emits('leadEditDialog', props.lead)" class="dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700" />
                <Button icon="pi pi-trash" label="Delete" outlined severity="danger" @click="emits('leadDeleteDialog', props.lead)" class="dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/30" />
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
/* Optional: Add smooth transitions for dark mode */
.dark .p-button {
    transition:
        background-color 0.2s,
        color 0.2s;
}

.dark .p-button:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.expanded-rows {
    h6 {
        min-width: 110px;
        font-size: 1rem;
    }
    .col-12 {
        padding: 5px 0;
    }
}
</style>
