<script setup lang="ts">
import { detailsIcon, getHumanReadableDate } from '@/entities/lead';
import { ref } from 'vue';

const props = defineProps<{
    properties: Record<string, any>[];
}>();

const emits = defineEmits<{
    showCombinedLeads: (trigger: boolean) => void;
}>();

const keysToRemove = [
    'sequence',
    'updated',
    'created',
    'kind',
    'etag',
    'icaluid',
    'eventtype',
    'to_lead',
    'queueTime',
    'apiVersion',
    'uri',
    'toFormatted',
    'fromFormatted',
    'parentCallSid',
    'accountSid',
    'phoneNumberSid',
    'Body',
    'SmsMessageSid',
    'NumMedia',
    'ReferralNumMedia',
    'AccountSid',
    'SmsSid',
    'ApiVersion',
    'SmsStatus',
    'MessageSid',
    'MessageType',
    'NumSegments',
    'text',
    'mark_for_deletion',
    'conversations_count',
    'html',
    'subject',
    'channel'
];
const dateKeys = ['lastMessageTime', 'to_followup_date', 'follow_up_date'];
// Ensure props.properties is always an array
const safeProperties = Array.isArray(props.properties) ? props.properties : [];

// Preprocess the properties to filter out non-string keys
const filteredProperties = safeProperties.map((property) => {
    const filtered = {};
    for (const [key, value] of Object.entries(property)) {
        if (typeof value !== 'object' && value && !keysToRemove.includes(key.toLowerCase()) && isNaN(Number(key))) {
            let arrKey = key;
            if (key === 'lastMessageTime') arrKey = 'Last_Interaction';
            filtered[arrKey] = {};
            filtered[arrKey].value = dateKeys.includes(key) ? getHumanReadableDate(value) : value;
            filtered[arrKey].props = detailsIcon(value);
        }
    }
    return filtered;
});
</script>

<template>
    <div v-for="(property, index) in filteredProperties" :key="index">
        <!-- Loop through each property object -->
        <div v-for="(value, key) in property" :key="key" class="flex items-center py-2 border-surface-300 dark:border-surface-500 border-b">
            <span class="flex justify-center items-center">
                <strong class="lg:text-sm capitalize">{{ key.replace(/_/g, ' ') }}:</strong>
            </span>
            <span class="ml-6 flex flex-col break-all">
                <Button
                    v-if="key === 'combined_leads'"
                    label="Click "
                    icon="pi pi-link"
                    size="small"
                    class="text-surface-600 dark:text-surface-200 lg:text-sm capitalize bg-yellow-200"
                    style="cursor: pointer; word-break: break-word"
                    @click="emits('showCombinedLeads', true)"
                />

                <span v-else class="text-surface-600 dark:text-surface-200 lg:text-sm capitalize" style="word-break: break-word">
                    <span v-if="!value.props?.icon" v-html="value.value" />
                    <span v-else> {{ value.value }} <i :class="`pi pi-fw pi-${value.props?.icon} text-xl text-${value.props?.color}-500 ml-2`" /> </span>
                </span>
            </span>
        </div>
    </div>
</template>

<style scoped lang="scss"></style>
