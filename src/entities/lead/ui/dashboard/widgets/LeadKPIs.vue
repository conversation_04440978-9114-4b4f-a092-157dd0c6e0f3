<script setup lang="ts">
import { chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const totalLeads = computed(() => props.leads.length);
const qualifiedLeads = computed(() => props.leads.filter((lead) => lead.is_qualified).length);
const followUps = computed(() => props.leads.filter((lead) => lead.lead_status === 'hot').length);
</script>

<template>
    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
        <div class="card mb-0">
            <div class="card flex flex-col items-center" v-if="props.loading">
                <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
            </div>
            <div v-else class="flex justify-between mb-4">
                <div>
                    <span class="block text-muted-color font-medium mb-4">Total Leads</span>
                    <div class="text-surface-900 dark:text-surface-0 font-medium text-xl">{{ totalLeads }}</div>
                </div>
                <div class="flex items-center justify-center bg-blue-100 dark:bg-blue-400/10 rounded-border" style="width: 2.5rem; height: 2.5rem">
                    <i class="pi pi-users text-blue-500 !text-xl"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
        <div class="card mb-0">
            <div class="card flex flex-col items-center" v-if="props.loading">
                <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
            </div>
            <div v-else class="flex justify-between mb-4">
                <div>
                    <span class="block text-muted-color font-medium mb-4">Qualified Leads</span>
                    <div class="text-surface-900 dark:text-surface-0 font-medium text-xl">{{ qualifiedLeads }}</div>
                </div>
                <div class="flex items-center justify-center bg-green-100 dark:bg-green-400/10 rounded-border" style="width: 2.5rem; height: 2.5rem">
                    <i class="pi pi-check-circle text-green-500 !text-xl"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
        <div class="card mb-0">
            <div class="card flex flex-col items-center" v-if="props.loading">
                <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
            </div>
            <div v-else class="flex justify-between mb-4">
                <div>
                    <span class="block text-muted-color font-medium mb-4">Hot Leads</span>
                    <div class="text-surface-900 dark:text-surface-0 font-medium text-xl">{{ followUps }}</div>
                </div>
                <div class="flex items-center justify-center bg-orange-100 dark:bg-orange-400/10 rounded-border" style="width: 2.5rem; height: 2.5rem">
                    <i class="pi pi-bolt text-orange-500 !text-xl"></i>
                </div>
            </div>
        </div>
    </div>
</template>
