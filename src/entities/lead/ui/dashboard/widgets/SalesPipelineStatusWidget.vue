<script setup lang="ts">
import { chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
// Helper function to format dates
const formatDateRange = (start: Date, end: Date): string => {
    const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
    return `${start.toLocaleDateString('en-US', options)} - ${end.toLocaleDateString('en-US', options)}`;
};

// Group leads by week
const weeklyData = computed(() => {
    const groupedData: { [key: string]: any } = {};

    // Process each lead
    props.leads.forEach((lead) => {
        const date = new Date(lead.lastMessageTime * 1000); // Convert timestamp to Date
        const startOfWeek = new Date(date.setDate(date.getDate() - date.getDay())); // Start of the week (Sunday)
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // End of the week (Saturday)

        const weekLabel = formatDateRange(startOfWeek, endOfWeek);

        if (!groupedData[weekLabel]) {
            groupedData[weekLabel] = {
                cold: 0,
                warm: 0,
                hot: 0
            };
        }

        groupedData[weekLabel][lead.lead_status]++;
    });

    // Transform grouped data into an array for rendering
    return Object.keys(groupedData).map((week) => ({
        week,
        cold: groupedData[week].cold,
        warm: groupedData[week].warm,
        hot: groupedData[week].hot
    }));
});

const documentStyle = getComputedStyle(document.documentElement);
// Prepare chart data
const chartData = computed(() => {
    const weeks = weeklyData.value.map((data) => data.week);
    const coldData = weeklyData.value.map((data) => data.cold);
    const warmData = weeklyData.value.map((data) => data.warm);
    const hotData = weeklyData.value.map((data) => data.hot);

    return {
        labels: weeks,
        datasets: [
            {
                label: 'Cold Leads',
                backgroundColor: documentStyle.getPropertyValue('--p-cyan-500'), // Red for cold leads
                hoverBackgroundColor: documentStyle.getPropertyValue('--p-cyan-400'), // Red for cold leads
                data: coldData,
                barThickness: 32
            },
            {
                label: 'Warm Leads',
                backgroundColor: documentStyle.getPropertyValue('--p-orange-400'), // Orange for warm leads
                hoverBackgroundColor: documentStyle.getPropertyValue('--p-orange-300'), // Orange for warm leads
                data: warmData,
                barThickness: 32
            },
            {
                label: 'Hot Leads',
                backgroundColor: documentStyle.getPropertyValue('--p-red-500'), // Green for hot leads
                hoverBackgroundColor: documentStyle.getPropertyValue('--p-red-400'), // Green for hot leads
                data: hotData,
                barThickness: 32
            }
        ]
    };
});

// Chart options
const chartOptionsParams = ref(null);

const setchartOptionsParams = () => {
    return chartOptions();
};

watch([getPrimary, getSurface, isDarkTheme], () => {
    chartOptionsParams.value = setchartOptionsParams();
});

onMounted(() => {
    chartOptionsParams.value = setchartOptionsParams();
});
</script>

<template>
    <div class="card">
        <div class="font-semibold text-xl mb-4">Sales Pipeline Status</div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>
        <Chart type="bar" v-else :data="chartData" :options="chartOptionsParams" />
    </div>
</template>
