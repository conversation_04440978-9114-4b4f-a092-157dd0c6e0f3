<script setup lang="ts">
import { chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const documentStyle = getComputedStyle(document.documentElement);

const funnelData = computed(() => ({
    labels: ['Cold', 'Warm', 'Hot'],
    datasets: [
        {
            label: 'Leads',
            barThickness: 82,
            backgroundColor: [documentStyle.getPropertyValue('--p-cyan-500'), documentStyle.getPropertyValue('--p-orange-400'), documentStyle.getPropertyValue('--p-red-500')],
            hoverBackgroundColor: [documentStyle.getPropertyValue('--p-cyan-400'), documentStyle.getPropertyValue('--p-orange-300'), documentStyle.getPropertyValue('--p-red-400')],
            data: [props.leads.filter((lead) => lead.lead_status === 'cold').length, props.leads.filter((lead) => lead.lead_status === 'warm').length, props.leads.filter((lead) => lead.lead_status === 'hot').length]
        }
    ]
}));

// Chart options
const chartOptionsParams = ref(null);

const setchartOptionsParams = () => {
    return chartOptions();
};

watch([getPrimary, getSurface, isDarkTheme], () => {
    chartOptionsParams.value = setchartOptionsParams();
});

onMounted(() => {
    chartOptionsParams.value = setchartOptionsParams();
});
</script>

<template>
    <div class="card">
        <div class="font-semibold text-xl mb-4">Lead Funnel Analysis</div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>
        <Chart type="bar" v-else :data="funnelData" :options="chartOptionsParams" />
    </div>
</template>
