<script setup lang="ts">
import { Lead, chartOptions } from '@/entities/lead';
import { computed, onMounted, ref, watch } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const documentStyle = getComputedStyle(document.documentElement);
const barData = computed(() => ({
    labels: ['AI Interactions', 'Human Interactions', 'AI & Human Interactions'],
    datasets: [
        {
            label: 'Interactions',
            barThickness: 82,
            data: [
                props.leads.reduce((sum, lead) => sum + (lead.ai_conversation_summary?.interaction_type === 'Human' || 0), 0),
                props.leads.reduce((sum, lead) => sum + (lead.ai_conversation_summary?.interaction_type !== 'AI' || 0), 0),
                props.leads.reduce((sum, lead) => sum + (lead.ai_conversation_summary?.interaction_type !== 'AI & Human' || 0), 0)
            ],
            backgroundColor: [documentStyle.getPropertyValue('--p-purple-500'), documentStyle.getPropertyValue('--p-green-500'), documentStyle.getPropertyValue('--p-cyan-500')],
            hoverBackgroundColor: [documentStyle.getPropertyValue('--p-purple-400'), documentStyle.getPropertyValue('--p-green-400'), documentStyle.getPropertyValue('--p-cyan-400')]
        }
    ]
}));

const chartOptionsParams = ref(null);

const setchartOptionsParams = () => {
    return chartOptions();
};

watch([getPrimary, getSurface, isDarkTheme], () => {
    chartOptionsParams.value = setchartOptionsParams();
});

onMounted(() => {
    chartOptionsParams.value = setchartOptionsParams();
});
</script>

<template>
    <div class="card">
        <div class="font-semibold text-xl mb-4">Engagement Metrics</div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>
        <Chart type="bar" v-else :data="barData" :options="chartOptionsParams" />
    </div>
</template>
