<script setup lang="ts">
import { formatDate, chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const recentActivities = computed(() => {
    return props.leads
        .filter((lead) => lead.lead_source !== 'gravity_form' && lead.ai_conversation_summary?.conversation_summary)
        .slice(-5)
        .map((lead) => {
            const leadObj = Object.keys(lead.conversations);
            const lastConversation = leadObj.length > 0 ? lead.conversations[leadObj.length - 1].message : 'No conversations yet';

            return {
                name: lead.phone || lead.email || lead.name,
                activity: lead.ai_conversation_summary?.conversation_summary || lastConversation || 'New lead generated',
                timestamp: formatDate(lead.lastMessageTime),
                icon:
                    {
                        whatsapp: 'pi-whatsapp text-green-500',
                        facebook: 'pi-facebook text-blue-500',
                        telegram: 'pi-send text-purple-500',
                        email: 'pi-envelope text-yellow-500',
                        call: 'pi-phone text-success-500'
                    }[lead.lead_source] || 'pi-question-circle text-gray-500' // fallback icon for unknown sources
            };
        });
});
</script>

<template>
    <div class="card">
        <div class="flex items-center justify-between mb-6">
            <div class="font-semibold text-xl">Recent Lead Activities</div>
        </div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>
        <ul class="p-0 mx-0 mt-0 mb-6 list-none" v-else>
            <li v-for="(activity, index) in recentActivities" :key="index" class="flex items-center py-2 border-b border-surface">
                <div class="w-12 h-12 flex items-center justify-center bg-surface-300 dark:bg-surface-500 rounded-full mr-4 shrink-0">
                    <i :class="['pi', activity.icon, '!text-xl']"></i>
                </div>
                <span class="text-surface-900 dark:text-surface-0 leading-normal">
                    {{ activity.name }}
                    <span class="text-surface-700 dark:text-surface-100">{{ activity.activity }} at {{ activity.timestamp }}</span>
                </span>
            </li>
        </ul>
    </div>
</template>
