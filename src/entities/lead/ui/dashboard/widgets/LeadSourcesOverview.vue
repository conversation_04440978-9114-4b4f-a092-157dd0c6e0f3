<script setup lang="ts">
import { chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const leadSources = computed(() => {
    const sources = ['whatsapp', 'facebook', 'telegram', 'email', 'call', 'gravity_form'];
    return sources.map((source) => ({
        name: source.charAt(0).toUpperCase() + source.slice(1),
        percentage: Math.round((props.leads.filter((lead) => lead.lead_source === source).length / props.leads.length) * 100 || 0),
        color: {
            whatsapp: '-green-500',
            facebook: '-blue-500',
            telegram: '-purple-500',
            email: '-yellow-500',
            call: '-red-500',
            gravity_form: '-cyan-500'
        }[source]
    }));
});
</script>

<template>
    <div class="card">
        <div class="flex justify-between items-center mb-6">
            <div class="font-semibold text-xl">Lead Sources Overview</div>
        </div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>
        <ul class="list-none p-0 m-0" v-else>
            <li v-for="(source, index) in leadSources" :key="index" class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                <div>
                    <span class="text-surface-900 dark:text-surface-0 font-medium mr-2 mb-1 md:mb-0 capitalize">{{ source.name.replace(/_/g, ' ') }}</span>
                </div>
                <div class="mt-2 md:mt-0 flex items-center">
                    <div class="bg-surface-300 dark:bg-surface-500 rounded-border overflow-hidden w-40 lg:w-24" style="height: 8px">
                        <div :class="[`bg${source.color}`, 'h-full']" :style="{ width: `${source.percentage}%` }"></div>
                    </div>
                    <span :class="[`text${source.color}`, 'ml-4 font-medium']">{{ source.percentage }}%</span>
                </div>
            </li>
        </ul>
    </div>
</template>
