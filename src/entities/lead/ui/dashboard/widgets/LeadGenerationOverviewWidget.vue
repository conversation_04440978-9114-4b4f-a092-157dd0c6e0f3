<script setup lang="ts">
import { chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const documentStyle = getComputedStyle(document.documentElement);

const lineData = computed(() => {
    if (!props.leads.length) return { labels: [], datasets: [] };

    // Get min and max timestamps directly from sorted leads
    const sortedLeads = [...props.leads].sort((a, b) => a.lastMessageTime - b.lastMessageTime);
    const minDate = new Date(sortedLeads[0].lastMessageTime * 1000);
    const maxDate = new Date(sortedLeads[sortedLeads.length - 1].lastMessageTime * 1000);

    const timeSpan = (maxDate - minDate) / (1000 * 60 * 60 * 24); // Total days
    let labels = [],
        data = [];

    if (timeSpan <= 7) {
        // Use daily labels
        const dailyData = {};
        props.leads.forEach((lead) => {
            const dateStr = new Date(lead.lastMessageTime * 1000).toLocaleDateString();
            dailyData[dateStr] = (dailyData[dateStr] || 0) + 1;
        });
        labels = Object.keys(dailyData);
        data = Object.values(dailyData);
    } else if (timeSpan <= 60) {
        // Use range labels (weekly-based but dynamic grouping)
        let currentDate = new Date(minDate);
        while (currentDate <= maxDate) {
            let fromDate = new Date(currentDate);
            let toDate = new Date(fromDate);
            toDate.setDate(toDate.getDate() + 6); // Group by week-like ranges

            let label = `${fromDate.toLocaleDateString()} - ${toDate.toLocaleDateString()}`;
            let total = props.leads
                .filter((lead) => {
                    let leadDate = new Date(lead.lastMessageTime * 1000);
                    return leadDate >= fromDate && leadDate <= toDate;
                })
                .reduce((sum, lead) => sum + 1, 0);

            labels.push(label);
            data.push(total);

            currentDate.setDate(currentDate.getDate() + 7);
        }
    } else {
        // Use monthly labels
        let currentDate = new Date(minDate);
        while (currentDate <= maxDate) {
            let fromDate = new Date(currentDate);
            let toDate = new Date(fromDate.getFullYear(), fromDate.getMonth() + 1, 0); // End of month

            let label = `${fromDate.toLocaleDateString()} - ${toDate.toLocaleDateString()}`;
            let total = props.leads
                .filter((lead) => {
                    let leadDate = new Date(lead.lastMessageTime * 1000);
                    return leadDate >= fromDate && leadDate <= toDate;
                })
                .reduce((sum, lead) => sum + 1, 0);

            labels.push(label);
            data.push(total);

            currentDate.setMonth(currentDate.getMonth() + 1);
        }
    }

    return {
        labels,
        datasets: [
            {
                label: 'Leads Generated',
                data,
                backgroundColor: documentStyle.getPropertyValue('--p-blue-500'),
                hoverBackgroundColor: documentStyle.getPropertyValue('--p-blue-400'),
                fill: true
            }
        ]
    };
});

const chartOptionsParams = ref(null);

const setchartOptionsParams = () => {
    return chartOptions();
};

watch([getPrimary, getSurface, isDarkTheme], () => {
    chartOptionsParams.value = setchartOptionsParams();
});

onMounted(() => {
    chartOptionsParams.value = setchartOptionsParams();
});

const pieData = computed(() => ({
    labels: ['WhatsApp', 'Facebook Messenger', 'Telegram', 'Email', 'Call', 'Gravity Form'],
    datasets: [
        {
            data: [
                props.leads.filter((lead) => lead.lead_source === 'whatsapp').length,
                props.leads.filter((lead) => lead.lead_source === 'facebook').length,
                props.leads.filter((lead) => lead.lead_source === 'telegram').length,
                props.leads.filter((lead) => lead.lead_source === 'email').length,
                props.leads.filter((lead) => lead.lead_source === 'call').length,
                props.leads.filter((lead) => lead.lead_source === 'gravity_form').length
            ],
            backgroundColor: [
                documentStyle.getPropertyValue('--p-green-500'),
                documentStyle.getPropertyValue('--p-blue-500'),
                documentStyle.getPropertyValue('--p-purple-500'),
                documentStyle.getPropertyValue('--p-yellow-500'),
                documentStyle.getPropertyValue('--p-red-500'),
                documentStyle.getPropertyValue('--p-cyan-500')
            ],
            hoverBackgroundColor: [
                documentStyle.getPropertyValue('--p-green-400'),
                documentStyle.getPropertyValue('--p-blue-400'),
                documentStyle.getPropertyValue('--p-purple-400'),
                documentStyle.getPropertyValue('--p-yellow-400'),
                documentStyle.getPropertyValue('--p-red-400'),
                documentStyle.getPropertyValue('--p-cyan-400')
            ]
        }
    ]
}));
</script>

<template>
    <div class="card">
        <div class="font-semibold text-xl mb-4">Lead Generation Overview</div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>

        <div class="grid grid-cols-12 gap-8" v-else>
            <div class="col-span-12 lg:col-span-6">
                <Chart type="line" :data="lineData" :options="chartOptionsParams" />
            </div>
            <div class="col-span-12 lg:col-span-6">
                <Chart type="pie" :data="pieData" :options="chartOptionsParams" />
            </div>
        </div>
    </div>
</template>
