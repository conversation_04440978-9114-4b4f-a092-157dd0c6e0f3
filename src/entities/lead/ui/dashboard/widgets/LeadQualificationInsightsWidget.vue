<script setup lang="ts">
import { chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const documentStyle = getComputedStyle(document.documentElement);
const gaugeData = computed(() => ({
    labels: ['Qualified Leads', 'Unqualified Leads'],
    datasets: [
        {
            data: [props.leads.filter((lead) => lead.is_qualified).length, props.leads.filter((lead) => !lead.is_qualified).length],
            backgroundColor: [documentStyle.getPropertyValue('--p-blue-500'), documentStyle.getPropertyValue('--p-red-500')],
            hoverBackgroundColor: [documentStyle.getPropertyValue('--p-blue-400'), documentStyle.getPropertyValue('--p-red-400')]
        }
    ]
}));

const chartOptionsParams = ref(null);

const setchartOptionsParams = () => {
    return chartOptions();
};

watch([getPrimary, getSurface, isDarkTheme], () => {
    chartOptionsParams.value = setchartOptionsParams();
});

onMounted(() => {
    chartOptionsParams.value = setchartOptionsParams();
});
</script>

<template>
    <div class="card flex flex-col items-center">
        <div class="font-semibold text-xl mb-4">Lead Qualification Insights</div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>
        <Chart v-else type="doughnut" :data="gaugeData" :options="chartOptionsParams" />
    </div>
</template>
