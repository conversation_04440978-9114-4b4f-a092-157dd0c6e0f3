<script setup lang="ts">
import { Lead } from '@/entities/lead';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();

const formatTimestamp = (timestamp: number) => new Date(timestamp * 1000).toLocaleString();
</script>

<template>
    <div class="card">
        <div class="font-semibold text-xl mb-4">Recent Leads</div>
        <DataTable :value="props.leads" :sortOrder="2" sortField="lastMessageTime" :rows="5" :paginator="true" :loading="props.loading" responsiveLayout="scroll">
            <Column field="lastMessageTime" header="Last Interaction" :sortable="true" style="width: 20%">
                <template #body="slotProps">
                    {{ formatTimestamp(slotProps.data.lastMessageTime) }}
                </template>
            </Column>
            <Column field="name" header="Name" :sortable="true" style="width: 35%"></Column>
            <Column field="phone" header="Phone" :sortable="true" style="width: 25%"></Column>
            <Column field="lead_source" header="Source" :sortable="true" style="width: 20%"></Column>
        </DataTable>
    </div>
</template>
