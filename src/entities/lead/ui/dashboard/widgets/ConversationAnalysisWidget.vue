<script setup lang="ts">
import { chartOptions, Lead } from '@/entities/lead';
import { computed, watch, ref, onMounted } from 'vue';

import { useLayout } from '@/layout/composables/layout';

const { getPrimary, getSurface, isDarkTheme } = useLayout();

const props = defineProps<{
    leads: Lead[];
    loading: boolean;
}>();
const sentimentData = computed(() => {
    const positiveCount = props.leads.filter((lead) => typeof lead.ai_conversation_summary === 'string' && lead.ai_conversation_summary.includes('positive')).length;
    const neutralCount = props.leads.filter((lead) => typeof lead.ai_conversation_summary === 'string' && lead.ai_conversation_summary.includes('neutral')).length;
    const negativeCount = props.leads.filter((lead) => typeof lead.ai_conversation_summary === 'string' && lead.ai_conversation_summary.includes('negative')).length;

    return {
        labels: ['Positive', 'Neutral', 'Negative'],
        datasets: [
            {
                data: [positiveCount, neutralCount, negativeCount],
                backgroundColor: ['#10B981', '#F59E0B', '#EF4444']
            }
        ]
    };
});
</script>

<template>
    <div class="card flex flex-col items-center">
        <div class="font-semibold text-xl mb-4">Conversation Sentiment Analysis</div>
        <div class="card flex flex-col items-center" v-if="props.loading">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="transparent" animationDuration=".5s" aria-label="Loading..." />
        </div>
        <Chart v-else type="polarArea" :data="sentimentData" />
    </div>
</template>
