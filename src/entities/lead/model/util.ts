import { Lead } from '@/entities/lead';

import CryptoJS from 'crypto-js';
import { marked } from 'marked';
/**
 * Formats a timestamp into a human-readable relative time string.
 * @param {string | number | Date} timestamp The timestamp to format.
 * @returns {string} A formatted string representing the relative time.
 */
export const formatDate = (timestamp: number): string => {
    const now = new Date();
    const date = new Date(timestamp * 1000);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        // Show the full date if the difference is more than a week
        return date.toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true // Use 12-hour format with AM/PM
        });
    }
};

/**
 * Converts a timestamp into a human-readable date string.
 * @param {number} timestamp The timestamp to convert.
 * @returns {string} A formatted date string.
 */
export const getHumanReadableDate = (timestamp: number): string => {
    const now = new Date(timestamp * 1000); // Get the current date from Date.now()
    return now.toLocaleString('en-US', {
        // Example format for US
        weekday: 'long',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true // Use 12-hour format with AM/PM
    });
};
export const getShortHumanReadableDate = (timestamp: number): string => {
    const now = new Date(timestamp * 1000); // Get the current date from Date.now()
    return now.toLocaleString('en-US', {
        // Example format for US
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true // Use 12-hour format with AM/PM
    });
};

// Helper function to calculate a date 10 days ahead or behind a given date
export const getDatePlusMinusDays = (date: Date | number | string, addDays: number) => {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + addDays);
    return newDate;
};

export const getDateDifferenceInDays = (date1: Date, date2: Date) => {
    return Math.abs((date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24));
};

export const chartOptions = () => {
    const documentStyle = getComputedStyle(document.documentElement);
    const borderColor = documentStyle.getPropertyValue('--surface-border');
    const textMutedColor = documentStyle.getPropertyValue('--text-color-secondary');
    return {
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        responsive: true,
        borderColor: 'transparent',
        scales: {
            x: {
                stacked: true,
                grid: {
                    color: 'transparent',
                    borderColor: 'transparent'
                }
            },
            y: {
                stacked: true,
                ticks: {
                    color: textMutedColor
                },

                grid: {
                    color: borderColor,
                    borderColor: 'transparent',
                    drawTicks: false
                }
            }
        }
    };
};

// Helper function to determine lead status
export const computeLeadStatus = (lead: Lead) => {
    if (lead?.mark_for_deletion || lead?.spam_detection?.is_spam) return 'danger';

    const interest = !!lead?.ai_conversation_summary?.lead_interest;

    if (lead?.is_qualified) {
        // Qualified + Interested → healthy
        if (interest) return 'healthy';
        // Qualified + Not Interested → warning
        return 'warning';
    }

    // Not Qualified
    if (interest) return 'warning'; // Shows interest but not qualified
    return 'danger'; // No interest and not qualified
};

export const getLeadIconColor = (leadStatus: string) => {
    switch (leadStatus) {
        case 'healthy':
            return 'green';
        case 'warning':
            return 'yellow';
        case 'danger':
            return 'red';
        default:
            return 'gray';
    }
};

export const detailsIcon = (value: string) => {
    let icon = '';
    let color = '';
    let tooltip = '';

    if (value == 'Yes' || value == 'True' || value == 'true' || value == 'yes') {
        icon = 'check';
        color = 'green';
        tooltip = 'Yes: This item is confirmed.';
    } else if (value == 'NA' || value == 'na') {
        icon = 'exclamation-triangle';
        color = 'yellow';
        tooltip = 'N/A: This item is not applicable.';
    } else if (value == 'No' || value == 'False' || value == 'false' || value == 'no') {
        icon = 'times';
        color = 'red';
        tooltip = 'No: This item is not confirmed.';
    } else {
        return {
            color: false,
            value: value,
            icon: false,
            tooltip: false
        };
    }
    return {
        color: color,
        value: value,
        icon: icon,
        tooltip: tooltip
    };
};
export const getLeadIcon = (leadStatus: string) => {
    switch (leadStatus) {
        case 'healthy':
            return 'pi pi-thumbs-up';
        case 'warning':
            return 'pi pi-exclamation-triangle';
        case 'danger':
            return 'pi pi-thumbs-down';
        default:
            return 'pi pi-question';
    }
};
export const getLeadButtonSeverity = (leadStatus: string) => {
    switch (leadStatus) {
        case 'healthy':
            return 'success';
        case 'warning':
            return 'warn';
        case 'danger':
            return 'danger';
        default:
            return 'secondary';
    }
};

// Decryption function using CryptoJS
const secretKey = 'F7pIGUyA38LJP/5NDsPxn0yaVOxaN0LqZMOIQ5C0ubmajhx1Enp1wRicRAmANKb2\n';

export const decryptData = (encrypted: string) => {
    const bytes = CryptoJS.AES.decrypt(encrypted, secretKey);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
    try {
        return JSON.parse(decryptedData);
    } catch (e) {
        console.error('Decryption failed or invalid JSON format', e);
        return null;
    }
};

export const encryptData = (data: object) => {
    try {
        return CryptoJS.AES.encrypt(JSON.stringify(data), secretKey).toString();
    } catch (e) {
        console.error('Encryption failed', e);
        return null;
    }
};

export const renderMarkdown = (text: string, removeHtml: boolean = false) => {
    if (!text) return '';
    if (text) {
        const markdownRegex = /[#_*~`>\[\]\(\)\-\+\!\|]/;
        let newText = text?.replace(/^html\s*/, '').trim();

        // Remove code block syntax but preserve content
        newText = newText?.replace(/```(json|html)\s*|\s*```/g, '');

        // First remove HTML if requested
        if (removeHtml) {
            // Remove HTML tags
            newText = newText?.replace(/<[^>]*>/g, '');
            // Remove HTML entities
            const div = document.createElement('div');
            div.innerHTML = newText;
            newText = div.textContent || div.innerText || '';
        }

        // Check if content contains markdown patterns and doesn't already have HTML
        if (newText && markdownRegex.test(newText) && !/<[a-z][\s\S]*>/i.test(newText)) {
            // Convert markdown to HTML
            const htmlText = marked(newText) as string;
            // If we need to remove HTML, do it again after markdown conversion
            return removeHtml ? htmlText?.replace(/<[^>]*>/g, '') : htmlText;
        }

        // Return the text without code block markers
        return newText;
    }
};
