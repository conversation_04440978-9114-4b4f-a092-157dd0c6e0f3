/**
 * @interface Lead
 * Represents the structure of a lead document.
 */
export const leadActionsArray = ['email', 'facebook', 'telegram', 'whatsapp', 'call', 'gravity_form', 'inquired', 'quoted', 'booked', 'converted', 'qualified', 'irrelevant', 'unqualified', 'failed', 'list', 'followups'] as const;

export interface Lead {
    id?: string;
    tenantId?: string | null;
    createdAt?: string | null;
    booking_action?: string;
    selectedBooking?: any;
    first_name?: string;
    last_name?: string;
    name?: string;
    email?: string;
    phone?: string;
    gender?: string;
    source_id?: string;
    profile_pic?: string;
    lead_source?: 'email' | 'facebook' | 'telegram' | 'whatsapp' | 'call' | 'gravity_form' | 'google_calendar_booking' | 'appointment_booking';
    locale?: string;
    timezone?: string;
    conversations?: Array<any>; // Replace `any` with a more specific type if needed
    notes?: []; // Replace `any` with a more specific type if needed
    lastMessageTime?: number; // Timestamp
    lead_status?: 'cold' | 'warm' | 'hot';
    lead_actions?: 'inquired' | 'quoted' | 'booked' | 'converted';
    is_assigned?: boolean;
    assigned_to?: string;
    address?: string;
    is_qualified?: boolean;
    to_followup_date?: number; // Timestamp
    ai_conversation_summary?: string | object | any;
    sales_advice?: string | object;
    spam_detection?: string | object | any;
    keywords?: Array<any> | object | any;
    lead_details?: any;
    ai_insights?: any;
    preferredAppointmentDateTime?: any;
    conversations_count?: number;
    notes_count?: number;
    mark_for_deletion?: boolean;
    appointmentHistory?: any;
}

export interface LeadFilters {
    name?: string;
    id?: string;
    email?: string;
    searchTerm?: string;
    searchTerms?: string[];
    number?: string;
    lead_source?: string[];
    lead_status?: string[];
    lead_actions?: string[];
    is_assigned?: boolean;
    mark_for_deletion?: boolean;
    assigned_to?: string;
    is_qualified?: boolean;
    failed_sync?: boolean;
    has_followup_date?: boolean;
    to_followup_date?: number;
    sort?: {
        col: string;
        order: 'asc' | 'desc';
    };
    page?: {
        pageSize?: number;
        page?: number;
        currentPage?: number;
    };
    dateRange?: {
        lastMessageTimeStart?: number;
        lastMessageTimeEnd?: number;
        to_followup_dateStart?: number;
        to_followup_dateEnd?: number;
    };
}

export interface followUpParams {
    channel: string;
    message?: string;
    voice?: string;
    script?: string;
}
