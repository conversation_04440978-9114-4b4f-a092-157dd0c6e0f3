import { firestore } from '@/firebase';
import { collection, doc } from 'firebase/firestore';

export const leadsCollection = collection(firestore, 'leads');

/**
 * Retrieves a Firestore document reference for a specific lead.
 * @param {string} id - The unique identifier for the lead.
 * @returns {DocumentReference} Firestore document reference.
 */
export const leadDocument = (id: string) => doc(firestore, 'leads', id);
