import { defineStore } from 'pinia';
import { ref } from 'vue';
import { setDoc, getDocs, query, where, addDoc, getDoc, limit, limitToLast, startAfter, startAt, endBefore, endAt, orderBy, writeBatch, doc, getCountFromServer, or, and, deleteDoc } from 'firebase/firestore';
import { followUpParams, Lead, leadDocument, LeadFilters, leadsCollection } from '@/entities/lead';
import { AuthError } from '@/entities/auth';
import { getUserLoggedInData, requestGetFollowUpMessage, requestLeadAnalysisByIds, requestSendFollowUpMessage, requestSendFollowUpMessageByIds } from '@/shared';
import { useAppointmentEntityStore } from '@/entities/appointments';
import { bookAppointment } from '@/features/appointment-booking/services/appointmentService';

/**
 * @function useLeadsStore
 * A Pinia store for managing CRUD operations on leads with Firebase Firestore integration.
 */
export const useLeadsStore = defineStore('leads', () => {
    const appointmentStore = useAppointmentEntityStore();

    /**
     * @property {Lead[]} leads - The list of leads stored in the state.
     */
    const leads = ref<Lead[]>([]);
    const totalLeads = ref<number>(0);
    const loading = ref<boolean>(false); // Loading state
    // Define the required columns (case-insensitive)
    const requiredUploadColumns = ref(['first_name', 'last_name', 'phone', 'email', 'postcode']);

    /**
     * @property {any} lastVisible - Tracks the last visible document for pagination.
     */
    const previousLastVisible = ref<any>(null);
    const lastVisible = ref<any>(null);
    const pageNumber = ref<number>(1);
    const firstVisible = ref<any>(null);
    const error = ref<AuthError | null>(null); // Error state
    const breadCrumbs = ref<any[]>([]); // Error state
    /**
     * Generates Firestore query constraints based on provided filters.
     * @param filters - The filters to apply to the Firestore query.
     * @returns An array of QueryConstraint objects for Firestore querying.
     */
    const generateFirestoreConstraints = (filters: LeadFilters = {}, removeSort: boolean = false) => {
        let q = query(leadsCollection);

        // facebook bot messages
        // q = query(q, where('id', '!=', '64tKak7khn3au4cYuJqb'));

        if (filters.mark_for_deletion === true) {
            q = query(q, where('mark_for_deletion', '==', true));
        } else {
            q = query(q, where('mark_for_deletion', '==', false));
        }
        // Apply filters
        // Search:
        if (filters.searchTerm) {
            q = query(q, where('keywords', 'array-contains', filters.searchTerm));
        }
        if (filters.searchTerms) {
            q = query(q, where('keywords', 'array-contains-any', filters.searchTerms));
        }
        if (filters.failed_sync === true) {
            q = query(q, where('ai_conversation_summary', '==', ''));
        }
        if (filters.lead_source && filters.lead_source.length > 0) {
            q = query(q, where('lead_source', 'in', filters.lead_source));
        }
        if (filters.lead_actions && filters.lead_actions.length > 0) {
            q = query(q, where('lead_actions', 'in', filters.lead_actions));
        }
        if (filters.is_qualified !== undefined) {
            q = query(q, where('is_qualified', '==', filters.is_qualified));
        }

        if (filters.lead_status && filters.lead_status.length > 0) {
            q = query(q, where('lead_status', 'in', filters.lead_status));
        }

        if (filters.has_followup_date === true) {
            if (filters.dateRange?.lastMessageTimeStart && filters.dateRange?.lastMessageTimeEnd) {
                q = query(q, where('to_followup_date', '>=', filters.dateRange.lastMessageTimeStart));
                q = query(q, where('to_followup_date', '<=', filters.dateRange.lastMessageTimeEnd));
            } else {
                const todayStart = new Date();
                todayStart.setHours(0, 0, 0, 0); // Start of today
                q = query(q, where('to_followup_date', '>=', todayStart.getTime() / 1000));
            }
        } else {
            // Apply date range filters
            if (filters.dateRange?.lastMessageTimeStart && filters.dateRange?.lastMessageTimeEnd) {
                q = query(q, where('lastMessageTime', '>=', filters.dateRange.lastMessageTimeStart));
                q = query(q, where('lastMessageTime', '<=', filters.dateRange.lastMessageTimeEnd));
            }
        }

        // Apply sorting
        if (filters.sort?.col && filters.sort?.order && !removeSort) {
            q = query(q, orderBy(filters.sort.col, filters.sort.order));
        }

        return q;
    };

    /**
     * Fetches paginated leads from Firestore with optional filters and sorting.
     * @param {number} pageSize - Number of leads to fetch per page.
     * @param {Object} filters - Filters to apply to the query.
     * @param {string} [filters.name] - Filter by lead name (partial match).
     * @param {string} [filters.id] - Filter by lead ID (exact match).
     * @param {string} [filters.number] - Filter by lead phone number (partial match).
     * @param {string} [filters.lead_source] - Filter by lead source (exact match).
     * @param {string} [filters.lead_status] - Filter by lead status (exact match).
     * @param {boolean} [filters.is_assigned] - Filter by whether the lead is assigned.
     * @param {string} [filters.assigned_to] - Filter by assigned lead ID (exact match).
     * @param {boolean} [filters.is_converted] - Filter by whether the lead is converted.
     * @param {boolean} [filters.is_qualified] - Filter by whether the lead is qualified.
     * @param {Object} [filters.dateRange] - Date range filter for timestamp fields.
     * @param {number} [filters.dateRange.lastMessageTimeStart] - Start timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.lastMessageTimeEnd] - End timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.to_followup_dateStart] - Start timestamp for to_followup_date.
     * @param {number} [filters.dateRange.to_followup_dateEnd] - End timestamp for to_followup_date.
     * @param direction
     * @param {string} [sortOptions.field] - Field to sort by ('lastMessageTime' or 'to_followup_date').
     * @param {'asc' | 'desc'} [sortOptions.order] - Sort order ('asc' or 'desc').
     * @returns {Promise<void>}
     */
    const fetchLeads = async (pageSize: number = 10, filters: LeadFilters = {}, direction: 'next' | 'prev' | 'first' | 'last' | 'current' | '' = ''): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            leads.value = [];
            let q = generateFirestoreConstraints(filters);

            if (direction === 'current') {
                if (previousLastVisible.value) {
                    q = query(q, startAt(previousLastVisible.value));
                }
            } else if (direction === 'next') {
                if (lastVisible.value) {
                    previousLastVisible.value = lastVisible.value;
                    q = query(q, startAfter(lastVisible.value));
                }
                pageNumber.value++;
            } else if (direction === 'prev') {
                if (firstVisible.value) {
                    q = query(q, endBefore(firstVisible.value));
                    q = query(q, limitToLast(pageSize));
                }
                pageNumber.value = Math.max(1, pageNumber.value - 1);
            } else if (direction === 'first') {
                breadCrumbs.value = [];
                pageNumber.value = 1;
                lastVisible.value = null;
                firstVisible.value = null;
                previousLastVisible.value = null;
            } else if (direction === 'last') {
                breadCrumbs.value = [];

                const totalCount = totalLeads.value;
                const lastPage = Math.ceil(totalCount / pageSize);
                pageNumber.value = lastPage;

                // Calculate how many items to skip
                const offset = (lastPage - 1) * pageSize;
                if (offset > 0) {
                    // Get the first document of the last page to use as startAfter point
                    const offsetQuery = query(q, limit(offset));

                    const offsetSnapshot = await getDocs(offsetQuery);
                    if (!offsetSnapshot.empty) {
                        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
                        q = query(q, startAfter(lastDoc), limit(pageSize));
                    }
                } else {
                    q = query(q, limit(pageSize));
                }
            } else {
                // Default case (first page)
                pageNumber.value = 1;
                lastVisible.value = null;
                firstVisible.value = null;
                previousLastVisible.value = null;
                if (pageSize > 0) q = query(q, limit(pageSize));
            }
            if (direction !== 'last' && direction !== 'prev') {
                q = query(q, limit(pageSize));
            }

            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                firstVisible.value = querySnapshot.docs[0];
                lastVisible.value = querySnapshot.docs[querySnapshot.docs.length - 1];

                const fetchedLeads = querySnapshot.docs.map((doc) => {
                    const data = doc.data() as any;
                    const notes = data.notes || [];

                    let conversations = data.conversations || [];
                    if (!Array.isArray(conversations)) {
                        // If conversations is not an array, convert it to an array
                        conversations = Object.entries(conversations).map(([key, value], index) => ({
                            // type assertion here
                            id: key,
                            ...(value as any)
                        }));
                    }

                    return {
                        id: doc.id,
                        conversations: conversations,
                        ...data,
                        address: data.address || data.ai_conversation_summary?.address || '',
                        conversations_count: conversations.length || 0,
                        notes_count: notes.length || 0
                    };
                }) as Lead[];
                leads.value = fetchedLeads;
            } else {
                leads.value = [];
            }
        } catch (err: any) {
            console.error('Error fetching leads:', err);
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            leads.value = [];
        } finally {
            loading.value = false;
        }
    };

    /**
     * Counts the total number of leads in Firestore based on filters.
     * @param {Object} filters - Filters to apply to the query.
     * @param {string} [filters.name] - Filter by lead name (partial match).
     * @param {string} [filters.id] - Filter by lead ID (exact match).
     * @param {string} [filters.number] - Filter by lead phone number (partial match).
     * @param {string} [filters.lead_source] - Filter by lead source (exact match).
     * @param {string} [filters.lead_status] - Filter by lead status (exact match).
     * @param {boolean} [filters.is_assigned] - Filter by whether the lead is assigned.
     * @param {string} [filters.assigned_to] - Filter by assigned lead ID (exact match).
     * @param {boolean} [filters.is_converted] - Filter by whether the lead is converted.
     * @param {boolean} [filters.is_qualified] - Filter by whether the lead is qualified.
     * @param {Object} [filters.dateRange] - Date range filter for timestamp fields.
     * @param {number} [filters.dateRange.lastMessageTimeStart] - Start timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.lastMessageTimeEnd] - End timestamp for lastMessageTime.
     * @param {number} [filters.dateRange.to_followup_dateStart] - Start timestamp for to_followup_date.
     * @param {number} [filters.dateRange.to_followup_dateEnd] - End timestamp for to_followup_date.
     * @returns {Promise<number>} - The total count of leads matching the filters.
     */
    const countTotalLeads = async (filters: LeadFilters = {}): Promise<void> => {
        try {
            const countFilterst = filters;
            delete countFilterst.sort;
            loading.value = true;
            error.value = null;

            const q = generateFirestoreConstraints(countFilterst);
            const snapshot = await getCountFromServer(q);
            totalLeads.value = snapshot.data().count;
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error counting total leads:', err);
        } finally {
            loading.value = false;
        }
    };
    const getLeadsForDashboard = async (filters: LeadFilters = {}): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;

            const q = generateFirestoreConstraints(filters);
            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                const fetchedLeads = querySnapshot.docs.map((doc) => {
                    const data = doc.data() as any;

                    let conversations = data.conversations || [];
                    const notes = data.notes || [];

                    if (!Array.isArray(conversations)) {
                        // If conversations is not an array, convert it to an array
                        conversations = Object.entries(conversations).map(([key, value], index) => ({
                            // type assertion here
                            id: key,
                            ...(value as any)
                        }));
                    }

                    return {
                        id: doc.id,
                        conversations: conversations,
                        conversations_count: conversations.length || 0,
                        notes_count: notes.length || 0,
                        ...data
                    };
                }) as Lead[];
                leads.value = fetchedLeads;
            } else {
                leads.value = [];
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error counting total leads:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Adds a new lead to Firestore.
     * @param {Omit<Lead, 'id'>} leadData - The lead data to be added (excluding the ID).
     * @returns {Promise<void>}
     */
    const addLead = async (leadData: Omit<Lead, 'id'>): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const user = await getUserLoggedInData();
            leadData.tenantId = user?.tenantId || '';
            const docRef = await addDoc(leadsCollection, leadData);
            leads.value.push({ id: docRef.id, ...leadData });
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error adding lead:', err);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Adds multiple new leads to Firestore in a batch.
     * @param {Omit<Lead, 'id'>[]} leadsData - An array of lead data to be added (excluding IDs).
     * @returns {Promise<void>}
     */
    const addLeads = async (leadsData: Omit<Lead, 'id'>[]): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const batch = writeBatch(leadsCollection.firestore);
            const newLeads: Lead[] = [];
            leadsData.forEach(async (leadData) => {
                const docRef = doc(leadsCollection); // Create a new document reference
                const user = await getUserLoggedInData();
                leadData.tenantId = user?.tenantId || '';
                batch.set(docRef, leadData); // Add the data to the batch
                newLeads.push({ id: docRef.id, ...leadData } as Lead); // Add to the local array
            });
            await batch.commit(); // Commit the batch write
            leads.value.push(...newLeads); // Update the local state
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error adding multiple leads:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Syncs all leads in Firestore by setting the ai_conversation_summary to an empty string.
     * @returns {Promise<void>}
     */
    const syncLeads = async (): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const msg = `<p>Thank you for providing the quote. I've reviewed everything, and it all looks good to me. I'd like to proceed with the purchase.</p>

<p>For your reference, I've included all the necessary details about my selection, including the door size, colour, delivery/installation address, contact number, and email address. Everything should be in order for you to move forward.</p>

<p>Could you kindly let me know the next steps to finalise the order? Also, if there's anything I need to prepare ahead of the installation, please do let me know.</p>`;
            const q = query(leadsCollection);

            console.log('Syncing Init...');

            // Pagination variables
            const batchSize = 200;
            let lastDoc = null;
            let hasMoreDocs = true;
            let totalProcessed = 0;

            while (hasMoreDocs) {
                // Build paginated query
                let paginatedQuery = q;
                if (lastDoc) {
                    paginatedQuery = query(q, startAfter(lastDoc));
                }
                paginatedQuery = query(paginatedQuery, limit(batchSize));

                const querySnapshot = await getDocs(paginatedQuery);

                if (querySnapshot.empty) {
                    hasMoreDocs = false;
                    break;
                }

                const leadsToUpdate = querySnapshot.docs.map((doc) => {
                    const data = doc.data() as any;
                    if (data.phone && typeof data.phone === 'string' && data.phone.startsWith('0')) {
                        data.phone = data.phone.replace(/^0/, '+44');
                    }
                    // Force address to be an empty string if not defined
                    data.address = data?.address ?? data?.ai_conversation_summary?.address ?? '';
                    data.email = data?.email ?? '';
                    data.phone = data?.phone ?? '';
                    data.name = data?.name ?? '';

                    // data.conversations[0].message = msg;
                    data.keywords = [...(data.name ? [data.name] : []), ...(data.email ? [data.email] : []), ...(data.phone ? [data.phone] : []), ...(data.id ? [data.id] : [])];
                    return {
                        id: doc.id,
                        ...data
                    };
                });

                console.log(`Processing batch of ${leadsToUpdate.length} leads...`);

                // Create a new batch for each group of leads
                const batch = writeBatch(leadsCollection.firestore);
                leadsToUpdate.forEach((data) => batch.set(leadDocument(data.id), data, { merge: true }));

                // Commit the current batch
                await batch.commit();

                // Update pagination variables
                lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
                totalProcessed += querySnapshot.docs.length;

                console.log(`Processed ${totalProcessed} leads so far`);
            }

            console.log(`All ${totalProcessed} leads synced successfully.`);
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error syncing leads:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Updates multiple leads in Firestore in a batch.
     * @param {Array<{ id: string; updatedData: Partial<Lead> }>} leadsToUpdate - An array of objects, each containing the lead ID and the data to update.
     * @returns {Promise<void>}
     */
    const updateLeads = async (leadsToUpdate: Partial<Lead>[]): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const batch = writeBatch(leadsCollection.firestore);

            leadsToUpdate.forEach((lead) => {
                batch.update(leadDocument(lead.id as string), lead as object);
            });

            await batch.commit();
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error updating multiple leads:', err);
        } finally {
            loading.value = false;
        }
    };

    const updateLeadsByIds = async (leadIds: string[], updatedData: Partial<Lead>): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const batch = writeBatch(leadsCollection.firestore);
            leadIds.forEach((id) => batch.update(leadDocument(id), updatedData));
            await batch.commit();
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error updating multiple leads:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Updates an existing lead in Firestore.
     * @param {string} id - The ID of the lead to update.
     * @param {Partial<Lead>} updatedData - The updated fields of the lead.
     * @returns {Promise<void>}
     */
    const updateLead = async (id: string, updatedData: Partial<Lead>): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const leadRef = leadDocument(id);

            // Remove undefined values from updatedData
            const filteredUpdatedData = Object.fromEntries(Object.entries(updatedData).filter(([_, v]) => v !== undefined));
            await setDoc(leadRef, filteredUpdatedData, { merge: true });
            const index = leads.value.findIndex((lead) => lead.id === id);
            if (index !== -1) {
                leads.value[index] = { ...leads.value[index], ...updatedData };
            }

            if (updatedData.lead_actions === 'converted' && (updatedData.email || updatedData.phone)) {
                let q = query(leadsCollection);
                if (updatedData.phone && updatedData.email) {
                    q = query(q, or(where('phone', '==', updatedData.phone), where('email', '==', updatedData.email)));
                } else if (updatedData.phone) {
                    q = query(q, where('phone', '==', updatedData.phone));
                } else if (updatedData.email) {
                    q = query(q, where('email', '==', updatedData.email));
                }

                const querySnapshot = await getDocs(q);
                const leadsToUpdate = querySnapshot.docs.map((doc) => {
                    const data = doc.data() as any;
                    data.to_followup_date = 0;
                    return {
                        id: doc.id,
                        ...data
                    };
                });
                console.log('Syncing and removing follow ups for converted...');
                console.log(leadsToUpdate);
                console.log(leadsToUpdate.length);
                if (leadsToUpdate.length) {
                    const batch = writeBatch(leadsCollection.firestore);
                    leadsToUpdate.forEach((data) => batch.set(leadDocument(data.id), data, { merge: true }));
                    await batch.commit();
                }
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error updating lead:', err);
        } finally {
            loading.value = false;
        }
    };

    /**
     * Deletes a lead from Firestore.
     * @param {string} id - The ID of the lead to delete.
     * @returns {Promise<void>}
     */
    const deleteLead = async (id: string): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            const leadDocRef = leadDocument(id);
            await deleteDoc(leadDocRef);
            const localLead = leads.value.find((lead) => lead.id === id);

            const appointments = await appointmentStore.searchAppointments({ searchTerms: [id] });

            for (const appointment of appointments) {
                await appointmentStore.deleteAppointment(appointment.id as string);
                await bookAppointment({
                    ...appointment,
                    lead: {
                        id: localLead?.id || '',
                        name: localLead?.name || '',
                        email: localLead?.email || '',
                        phone: localLead?.phone || '',
                        address: localLead?.address || '',
                        booking_action: localLead?.booking_action || 'add'
                    },
                    action: 'cancel'
                });
            }
            leads.value = leads.value.filter((lead) => lead.id !== id);
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
        } finally {
            loading.value = false;
        }
    };

    const deleteLeads = async (leadIds: string[]) => {
        const batch = writeBatch(leadsCollection.firestore);
        try {
            loading.value = true;
            error.value = null;

            for (const id of leadIds) {
                const leadDocRef = leadDocument(id);
                batch.delete(leadDocRef);
                const localLead = leads.value.find((lead) => lead.id === id);
                const appointments = await appointmentStore.searchAppointments({ searchTerms: [id] });
                for (const appointment of appointments) {
                    await appointmentStore.deleteAppointment(appointment.id as string);
                    try {
                        await bookAppointment({
                            ...appointment,
                            lead: {
                                id: localLead?.id || '',
                                name: localLead?.name || '',
                                email: localLead?.email || '',
                                phone: localLead?.phone || '',
                                address: localLead?.address || '',
                                booking_action: localLead?.booking_action || 'add',
                                tenantId: localLead?.tenantId || ''
                            },
                            action: 'cancel'
                        });
                    } catch (err) {
                        console.error('Error cancelling appointment:', err);
                    }
                }
            }

            await batch.commit();
            leads.value = leads.value.filter((lead) => !leadIds.includes(lead.id as string));
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting leads:', err);
        } finally {
            loading.value = false;
        }
    };
    const analyzeLeads = async (leadIds: string[]) => {
        try {
            loading.value = true;
            error.value = null;
            const processed = await requestLeadAnalysisByIds({ ids: leadIds });
            loading.value = false;
            if (!processed) {
                error.value = { code: 'unknown', message: "There's an error analyzing the lead. Please try again" };
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting leads:', err);
        } finally {
            loading.value = false;
        }
    };
    const followUpLeads = async (leadIds: string[]) => {
        try {
            loading.value = true;
            error.value = null;
            const processed = await requestSendFollowUpMessageByIds({ ids: leadIds });
            loading.value = false;
            if (!processed) {
                error.value = { code: 'unknown', message: "There's an error sending leads follow up. Please try again" };
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting leads:', err);
        } finally {
            loading.value = false;
        }
    };
    const getLeadFollowUpMessage = async (leadIds: string[], messageParams: followUpParams) => {
        try {
            loading.value = true;
            error.value = null;
            const processed = await requestGetFollowUpMessage({ ids: leadIds, messageParams });
            return processed;
            loading.value = false;
            if (!processed) {
                error.value = { code: 'unknown', message: "There's an error getting the lead follow up message. Please try again" };
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting leads:', err);
        } finally {
            loading.value = false;
        }
    };
    const sendLeadFollowUpMessage = async (leadIds: string[], messageParams: followUpParams) => {
        try {
            loading.value = true;
            error.value = null;
            const processed = await requestSendFollowUpMessage({ ids: leadIds, messageParams });
            loading.value = false;
            if (!processed) {
                error.value = { code: 'unknown', message: "There's an error sending the lead follow up message. Please try again" };
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting leads:', err);
        } finally {
            loading.value = false;
        }
    };
    const outOfScopeLeads = async (isOutOfScope: boolean = true, leadIds: string[]) => {
        console.log('isOutOfScope');
        console.log(isOutOfScope);
        const batch = writeBatch(leadsCollection.firestore);
        try {
            loading.value = true;
            error.value = null;
            leadIds.forEach((id) => {
                batch.set(leadDocument(id), { mark_for_deletion: isOutOfScope }, { merge: true });
            });
            await batch.commit();
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting leads:', err);
        } finally {
            loading.value = false;
        }
    };
    const qualifiedLeads = async (isQualified: boolean = true, leadIds: string[]) => {
        const batch = writeBatch(leadsCollection.firestore);
        try {
            loading.value = true;
            error.value = null;
            leadIds.forEach((id) => {
                batch.set(leadDocument(id), { is_qualified: isQualified }, { merge: true });
            });
            await batch.commit();
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error deleting leads:', err);
        } finally {
            loading.value = false;
        }
    };
    /**
     * Fetches a single lead by its ID from Firestore.
     * @param {string} id - The ID of the lead to fetch.
     * @returns {Promise<Lead | null>} - The lead object or null if not found.
     */
    const getLead = async (id: string): Promise<Lead | null> => {
        try {
            loading.value = true;
            error.value = null;
            const leadDoc = await getDoc(leadDocument(id));
            if (leadDoc.exists()) {
                return { id: leadDoc.id, ...leadDoc.data() } as Lead;
            }
            return null;
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error fetching lead:', err);
            return null;
        } finally {
            loading.value = false;
        }
    };
    const importLeadsFromExcelFile = async (data: any[]): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;

            // Validate the Excel columns
            const columns = Object.keys(data[0] || {}).map((col) => col.toLowerCase());
            const missingColumns = requiredUploadColumns.value.filter((requiredCol) => !columns.includes(requiredCol));

            if (missingColumns.length > 0) {
                throw new Error(`Missing or invalid columns: ${missingColumns.join(', ')}`);
            }

            // Convert column headers to lowercase for consistency
            const normalizedData = data.map((row: any) => Object.fromEntries(Object.entries(row).map(([key, value]) => [key.toLowerCase(), value])));

            const batch = writeBatch(leadsCollection.firestore); // Create a Firestore batch

            // Track skipped records
            const skippedRecords: string[] = [];

            // Process all records in parallel using Promise.all
            await Promise.all(
                normalizedData.map(async (item) => {
                    // Validate if the record already exists based on phone or email
                    let existingDoc;

                    const q = query(leadsCollection, or(where('phone', '==', item.phone), where('email', '==', item.email)));

                    const querySnapshot = await getDocs(q);
                    if (!querySnapshot.empty) {
                        existingDoc = querySnapshot.docs[0]; // Return the first matching document
                    }
                    const user = await getUserLoggedInData();
                    item.tenantId = user?.tenantId || '';
                    if (existingDoc) {
                        // Merge the data into the existing document
                        const docRef = leadDocument(existingDoc.id); // Reference the existing document
                        batch.set(docRef, item, { merge: true }); // Merge the new data into the existing document
                        console.log(`Merged record with phone ${item.phone} and email ${item.email}`);
                    } else {
                        // Add a new document if no match is found
                        const docRef = doc(leadsCollection); // Generate a new document reference
                        batch.set(docRef, item); // Create a new document
                        console.log(`Added new record with phone ${item.phone} and email ${item.email}`);
                    }
                })
            );

            // Commit the batch to Firestore
            await batch.commit();
            console.log('Batch committed successfully');

            if (skippedRecords.length > 0) {
                console.warn(`Skipped ${skippedRecords.length} records:`, skippedRecords);
            }
        } catch (err: any) {
            let errMsg = 'Unknown Error.';
            if (err instanceof Error) errMsg = err.message;
            error.value = { code: err.code, message: errMsg };
            console.error('Error fetching lead:', err);
        } finally {
            loading.value = false;
        }
    };

    return {
        requiredUploadColumns,
        leads,
        totalLeads,
        firstVisible,
        lastVisible,
        previousLastVisible,
        pageNumber,
        loading,
        error,
        getLeadsForDashboard,
        followUpLeads,
        getLeadFollowUpMessage,
        sendLeadFollowUpMessage,
        analyzeLeads,
        outOfScopeLeads,
        qualifiedLeads,
        fetchLeads,
        countTotalLeads,
        getLead,
        addLead,
        addLeads,
        syncLeads,
        updateLeads,
        updateLeadsByIds,
        updateLead,
        deleteLead,
        importLeadsFromExcelFile,
        deleteLeads
    };
});
