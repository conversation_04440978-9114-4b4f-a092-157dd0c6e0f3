<script setup lang="ts">
import { useLayout } from '@/layout/composables/layout';

const { logo } = useLayout();
</script>

<template>
    <div class="flex items-center justify-center min-h-screen overflow-hidden">
        <div class="flex flex-col items-center justify-center">
            <img :src="`/logo/${logo}`" class="mb-8 w-16 shrink-0 mx-auto" />
            <div style="border-radius: 56px; padding: 0.3rem; background: linear-gradient(180deg, color-mix(in srgb, var(--primary-color), transparent 60%) 10%, var(--surface-ground) 30%)">
                <div class="w-full bg-surface-0 dark:bg-surface-900 py-20 px-8 sm:px-20 flex flex-col items-center" style="border-radius: 53px">
                    <span class="text-primary font-bold text-3xl">404</span>
                    <h1 class="text-surface-900 dark:text-surface-0 font-bold text-3xl lg:text-5xl mb-2">Not Found</h1>
                    <div class="text-surface-600 dark:text-surface-200 mb-8">Requested resource is not available.</div>
                    <p class="text-gray-600 mb-6">Oops! The page you are looking for doesn't exist or has been moved.</p>
                    <Button as="router-link" label="Go Back" to="/" />
                </div>
            </div>
        </div>
    </div>
</template>
