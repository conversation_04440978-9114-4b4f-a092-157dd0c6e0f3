<script setup lang="ts">
import { useLayout } from '@/layout/composables/layout';
import { useAuthStore } from '@/entities/auth';

const { logo } = useLayout();
const authStore = useAuthStore();
</script>

<template>
    <div class="flex items-center justify-center min-h-screen overflow-hidden">
        <div class="flex flex-col items-center justify-center">
            <img :src="`/logo/${logo}`" class="mb-8 w-16 shrink-0 mx-auto" />
            <div style="border-radius: 56px; padding: 0.3rem; background: linear-gradient(180deg, color-mix(in srgb, var(--primary-color), transparent 60%) 10%, var(--surface-ground) 30%)">
                <div class="w-full bg-surface-0 dark:bg-surface-900 py-20 px-8 sm:px-20 flex flex-col items-center" style="border-radius: 53px">
                    <span class="text-primary font-bold text-3xl">403</span>
                    <h1 class="text-surface-900 dark:text-surface-0 font-bold text-3xl lg:text-5xl mb-2">Forbidden</h1>
                    <div class="text-surface-600 dark:text-surface-200 mb-8">Notice: Awaiting Company Access</div>
                    <p class="text-gray-600 mb-6">Oops! You haven't been added to a company yet.</p>
                    <p class="text-gray-600 mb-6">Please wait for an email invitation from Liftt — your administrator will send it once your account has been set up.</p>
                    <Button label="Logout" class="w-full mt-3" @click="authStore.logout" />
                    <Divider align="center" type="dashed">
                        <span class="text-surface-600 dark:text-surface-400">or </span>
                    </Divider>
                    <Button label="Back to Dashboard" class="w-full mt-3" severity="secondary" outlined as="router-link" to="/"></Button>
                </div>
            </div>
        </div>
    </div>
</template>
