import { initializeApp, type FirebaseOptions } from 'firebase/app';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';
import { getMessaging, onBackgroundMessage } from 'firebase/messaging/sw';
import { onMessage, getToken } from 'firebase/messaging';

const prodConfig: FirebaseOptions = {
    apiKey: 'AIzaSyCk3LigkE79r8qwrBdAmHxlyDSO9iXi7Fk',
    authDomain: 'wyspre-ai.firebaseapp.com',
    projectId: 'wyspre-ai',
    storageBucket: 'wyspre-ai.firebasestorage.app',
    messagingSenderId: '147632747972',
    appId: '1:147632747972:web:f3a397d77587e3e6082091'
};

let config;
let aidKey: string;
config = prodConfig;
aidKey = 'BAKmP-1qHAb2SfrRXrrDaYIuNTixJ7NrOag5HqZrwKWgQiL60oMXd8NNwaxqK_NxoS-nmEvAwsOKMoon7DmzeoY';

const siteURL = 'https://wyspre-ai.firebaseapp.com';

const app = initializeApp(config);
// const analytics = getAnalytics(app);
const messaging = getMessaging(app);
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);
const functions = getFunctions(app);

if (location.hostname === 'localhost') {
    // Connect emulators
    // connectAuthEmulator(auth, 'http://localhost:9099');
    // connectFirestoreEmulator(db, 'localhost', 8080);
    // connectFunctionsEmulator(functions, 'localhost', 5001);
}

export const authReady = new Promise<void>((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
        unsubscribe();
        // Make sure the token is ready for the rest of auth
        await user?.getIdToken(true);
        resolve();
    });
});

export { app as firebase, auth, db as firestore, storage, functions, siteURL, aidKey, messaging, onMessage, getToken, onBackgroundMessage };
