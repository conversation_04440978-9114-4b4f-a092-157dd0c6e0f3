/* Utils */
.clearfix:after {
    content: ' ';
    display: block;
    clear: both;
}

.card {
    background: var(--surface-card);
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: var(--content-border-radius);

    &:last-child {
        margin-bottom: 0;
    }
}

.p-toast {
    &.p-toast-top-right,
    &.p-toast-top-left,
    &.p-toast-top-center {
        top: 100px;
    }
}

.p-inputicon {
    &.topfix {
        top: 40% !important;
    }
    &.error {
        color: var(--error-color);
    }
}

/* Lead Right Drawer Info  */

.p-drawer-open:has(.lead-info) {
    z-index: 2102 !important;
}

.p-overlay-mask-enter:has(.list-dialog) {
    z-index: 2100 !important;
}


/* Lead List  */
.p-datatable-filter-buttonbar {
    display: none !important;
}
.responsive-toolbar .p-toolbar-group-start,
.responsive-toolbar .p-toolbar-group-end {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.toolbar-buttons,
.toolbar-buttons-end {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Responsive adjustments for small screens */
@media (max-width: 768px) {
    .toolbar-buttons,
    .toolbar-buttons-end {
        flex-direction: column;
        width: 100%;
    }

    .toolbar-buttons > .p-button,
    .toolbar-buttons-end > .p-button {
        width: 100%;
        justify-content: center;
    }

    .toolbar-buttons-end {
        margin-top: 1rem;
    }
}

.pac-container {
    &.pac-logo {
        z-index: 9999 !important;
    }
}


/* Loading overlay styles */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    border-radius: 6px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
}
