import { createApp } from 'vue';
import App from './App.vue';
import { router } from './router';
import { VueCookieNext } from 'vue-cookie-next';
import { createPinia } from 'pinia';

import Aura from '@primevue/themes/aura';
import PrimeVue from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import ToastService from 'primevue/toastservice';

import '@/assets/styles.scss';
import '@/assets/tailwind.css';
import { definePreset } from '@primevue/themes';

const stylePreset = definePreset(Aura, {
    semantic: {
        primary: {
            50: '#f9fafb', // Very light gray
            100: '#f3f4f6', // Light gray
            200: '#e5e7eb', // Slightly darker gray
            300: '#d1d5db', // Medium-light gray
            400: '#9ca3af', // Medium gray
            500: '#6b7280', // Slightly darker medium gray
            600: '#4b5563', // Darker gray
            700: '#374151', // Close to base color
            800: '#202124', // Base color
            900: '#18191b', // Very dark gray
            950: '#0f1012' // Almost black
        }
    }
});

const app = createApp(App);
const pinia = createPinia();

app.use(router());

app.use(pinia);
app.use(PrimeVue, {
    theme: {
        preset: stylePreset,
        options: {
            darkModeSelector: '.app-dark'
        }
    }
});
app.use(ToastService);
app.use(ConfirmationService);
app.use(VueCookieNext);
VueCookieNext.config({ expire: '30d' });
app.mount('#app');
