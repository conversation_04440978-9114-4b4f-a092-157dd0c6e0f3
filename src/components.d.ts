/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {};

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Avatar: typeof import('primevue/avatar')['default']
    BestSellingWidget: typeof import('./components/dashboard/BestSellingWidget.vue')['default']
    Button: typeof import('primevue/button')['default']
    Card: typeof import('primevue/card')['default']
    Chart: typeof import('primevue/chart')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    Chip: typeof import('primevue/chip')['default']
    Column: typeof import('primevue/column')['default']
    DataTable: typeof import('primevue/datatable')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Editor: typeof import('primevue/editor')['default']
    FeaturesWidget: typeof import('./components/landing/FeaturesWidget.vue')['default']
    FileUpload: typeof import('primevue/fileupload')['default']
    FloatingConfigurator: typeof import('./components/FloatingConfigurator.vue')['default']
    FooterWidget: typeof import('./components/landing/FooterWidget.vue')['default']
    HeroWidget: typeof import('./components/landing/HeroWidget.vue')['default']
    HighlightsWidget: typeof import('./components/landing/HighlightsWidget.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    InputGroup: typeof import('primevue/inputgroup')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Menu: typeof import('primevue/menu')['default']
    Message: typeof import('primevue/message')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    NotificationsWidget: typeof import('./components/dashboard/NotificationsWidget.vue')['default']
    OverlayBadge: typeof import('primevue/overlaybadge')['default']
    Panel: typeof import('primevue/panel')['default']
    Password: typeof import('primevue/password')['default']
    PricingWidget: typeof import('./components/landing/PricingWidget.vue')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    RecentSalesWidget: typeof import('./components/dashboard/RecentSalesWidget.vue')['default']
    RevenueStreamWidget: typeof import('./components/dashboard/RevenueStreamWidget.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('primevue/select')['default']
    SplitButton: typeof import('primevue/splitbutton')['default']
    StatsWidget: typeof import('./components/dashboard/StatsWidget.vue')['default']
    Tag: typeof import('primevue/tag')['default']
    Textarea: typeof import('primevue/textarea')['default']
    Timeline: typeof import('primevue/timeline')['default']
    Toast: typeof import('primevue/toast')['default']
    Toolbar: typeof import('primevue/toolbar')['default']
    TopbarWidget: typeof import('./components/landing/TopbarWidget.vue')['default']
  }
  export interface ComponentCustomProperties {
    StyleClass: typeof import('primevue/styleclass')['default']
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
