import { computed, reactive } from 'vue';

const isDarkThemeStored = localStorage.getItem('darkTheme') === 'true';

const layoutConfig = reactive({
    preset: 'Aura',
    primary: 'liftt',
    surface: 'neutral',
    darkTheme: isDarkThemeStored,
    menuMode: 'static'
});

const layoutState = reactive({
    staticMenuDesktopInactive: false,
    overlayMenuActive: false,
    profileSidebarVisible: false,
    configSidebarVisible: false,
    staticMenuMobileActive: false,
    menuHoverActive: false,
    activeMenuItem: null
});

export function useLayout() {
    /* eslint-disable @typescript-eslint/no-explicit-any */
    const setActiveMenuItem = (item: any) => {
        layoutState.activeMenuItem = item.value || item;
    };

    const toggleDarkMode = () => {
        if (!document.startViewTransition) {
            executeDarkModeToggle();

            return;
        }

        document.startViewTransition(() => executeDarkModeToggle());
    };

    const lightOrDarkMode = () => {
        if (!document.startViewTransition) {
            executeDefaultMode();

            return;
        }

        document.startViewTransition(() => executeDefaultMode());
    };

    const executeDefaultMode = () => {
        if (!layoutConfig.darkTheme) {
            document.documentElement.classList.remove('app-dark');
        } else {
            document.documentElement.classList.add('app-dark');
        }
    };

    const executeDarkModeToggle = () => {
        layoutConfig.darkTheme = !layoutConfig.darkTheme;
        document.documentElement.classList.toggle('app-dark');
        localStorage.setItem('darkTheme', layoutConfig.darkTheme.toString());
    };

    const toggleMenu = () => {
        if (layoutConfig.menuMode === 'overlay') {
            layoutState.overlayMenuActive = !layoutState.overlayMenuActive;
        }

        if (window.innerWidth > 991) {
            layoutState.staticMenuDesktopInactive = !layoutState.staticMenuDesktopInactive;
        } else {
            layoutState.staticMenuMobileActive = !layoutState.staticMenuMobileActive;
        }
    };

    const isSidebarActive = computed(() => layoutState.overlayMenuActive || layoutState.staticMenuMobileActive);

    const isDarkTheme = computed(() => layoutConfig.darkTheme);
    const logo = computed(() => (!layoutConfig.darkTheme ? 'logo-dark.svg' : 'logo-light.svg'));

    const getPrimary = computed(() => layoutConfig.primary);

    const getSurface = computed(() => layoutConfig.surface);

    return {
        logo,
        layoutConfig,
        layoutState,
        toggleMenu,
        isSidebarActive,
        isDarkTheme,
        getPrimary,
        getSurface,
        setActiveMenuItem,
        toggleDarkMode,
        lightOrDarkMode
    };
}
