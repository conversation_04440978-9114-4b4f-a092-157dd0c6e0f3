<script setup lang="ts">
import { useLayout } from '@/layout/composables/layout';
import AppConfigurator from './AppConfigurator.vue';
import { useAuthStore } from '@/entities/auth';

const { toggleMenu, toggleDarkMode, isDarkTheme, logo } = useLayout();
const authStore = useAuthStore();
const items = [
    {
        label: 'Profile',
        command: () => {}
    },
    {
        separator: true
    },
    {
        label: 'Logout',
        command: () => {
            authStore.logout();
        }
    }
];
</script>

<template>
    <div class="layout-topbar">
        <div class="layout-topbar-logo-container">
            <button class="layout-menu-button layout-topbar-action" @click="toggleMenu">
                <i class="pi pi-bars"></i>
            </button>
            <router-link to="/" class="layout-topbar-logo">
                <img width="54" height="11" :src="`/logo/${logo}`" />
            </router-link>
        </div>

        <div class="layout-topbar-actions">
            <div class="layout-config-menu">
                <button type="button" class="layout-topbar-action" @click="toggleDarkMode">
                    <i :class="['pi', { 'pi-moon': isDarkTheme, 'pi-sun': !isDarkTheme }]"></i>
                </button>
                <!--
                <div class="relative">
                    <button
                        v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scalein', leaveToClass: 'hidden', leaveActiveClass: 'animate-fadeout', hideOnOutsideClick: true }"
                        type="button"
                        class="layout-topbar-action layout-topbar-action-highlight"
                    >
                        <i class="pi pi-palette"></i>
                    </button>
                    <AppConfigurator />
                </div>
                -->
            </div>

            <button
                class="layout-topbar-menu-button layout-topbar-action"
                v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scalein', leaveToClass: 'hidden', leaveActiveClass: 'animate-fadeout', hideOnOutsideClick: true }"
            >
                <i class="pi pi-ellipsis-v"></i>
            </button>

            <div class="layout-topbar-menu hidden lg:block">
                <div class="layout-topbar-menu-content">
                    <!--
                  <button type="button" class="layout-topbar-action">
                      <i class="pi pi-calendar"></i>
                      <span>Calendar</span>
                  </button>
                  <button type="button" class="layout-topbar-action">
                      <i class="pi pi-inbox"></i>
                      <span>Messages</span>
                  </button>
                  -->
                    <SplitButton plain :model="items">
                        <i class="pi pi-user"></i>
                    </SplitButton>
                </div>
            </div>
        </div>
    </div>
</template>
