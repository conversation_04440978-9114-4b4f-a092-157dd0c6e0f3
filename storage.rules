rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {


  	function isSignedIn() {
      return request.auth.uid != null;
    }

    function isAdmin() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role ==  "admin";
    }

    match /users/{userId} {
      function isMyDoc() {
        return isSignedIn() && request.auth.uid == userId;
      }

      allow read, write: if isMyDoc() || isAdmin();
      allow create: if isAdmin();
    }
    match /leads/{leadId} {
      allow read, write: if isSignedIn() && isAdmin();
    }

    match /chats/{document=**} {
      allow read: if request.time < timestamp.date(2025, 10, 10);
    }
  }
}
