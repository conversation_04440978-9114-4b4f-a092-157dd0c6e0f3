{"name": "sakai-vue", "version": "4.2.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --fix . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@eslint/config-array": "^0.19.1", "@googlemaps/js-api-loader": "^1.16.8", "@primevue/themes": "^4.2.4", "chart.js": "3.3.2", "crypto-js": "^4.2.0", "firebase": "^11.6.0", "markdown-it": "^14.1.0", "marked": "^15.0.11", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.2.4", "quill": "^2.0.3", "vue": "^3.4.34", "vue-cookie-next": "^1.3.0", "vue-router": "^4.4.0", "vue-tel-input": "^9.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.18.0", "@primevue/auto-import-resolver": "^4.2.5", "@rushstack/eslint-patch": "^1.8.0", "@types/crypto-js": "^4.2.2", "@types/node": "^22.10.6", "@types/vue-tel-input": "^2.1.7", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^9.0.0", "autoprefixer": "^10.4.19", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "globals": "^15.14.0", "postcss": "^8.4.40", "prettier": "^3.2.5", "sass": "^1.55.0", "tailwindcss": "^3.4.6", "tailwindcss-primeui": "^0.3.4", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.3.5", "vite-plugin-checker": "^0.8.0"}}